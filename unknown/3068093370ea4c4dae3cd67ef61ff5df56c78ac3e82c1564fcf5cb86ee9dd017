<script lang="ts" setup>
import { Delete02Icon, ImageAdd02Icon } from 'hugeicons-vue'
import { CircleStencil, Cropper } from 'vue-advanced-cropper'
import 'vue-advanced-cropper/dist/style.css'

const { t } = useI18n()
const nuxt = useNuxtApp()

interface LocalProfileImageType {
  isLoaded: boolean
  uploadFailed: boolean
  rawFile?: string
  url?: string
  key?: string
  file?: File
}

interface ProfileImageDataInterface {
  url: string | null
  key: string | null
}

const profileImageData = defineModel<ProfileImageDataInterface>('profileImageData', { required: true })

// Cropper related refs
const cropperModal = ref(false)
const cropperAspectRatio = 16 / 9
const imagePreview = ref<string | null>(null)
const cropper = ref<InstanceType<typeof Cropper>>()
const fileInput = ref<HTMLInputElement | null>(null)

const localProfileImage = reactive<LocalProfileImageType>({
  isLoaded: false,
  uploadFailed: false,
})

onMounted(() => {
  if (profileImageData.value?.url && profileImageData.value?.key) {
    localProfileImage.url = profileImageData.value.url
    localProfileImage.key = profileImageData.value.key
    localProfileImage.isLoaded = true
  }
})

// Image handling functions
async function uploadProfileImage(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  try {
    const response: { image_key: string, image_url: string } = await nuxt.$api('/api/organiser/images/upload', {
      method: 'POST',
      body: formData,
    })
    if (response) {
      localProfileImage.key = response.image_key
      localProfileImage.url = response.image_url
      localProfileImage.isLoaded = true
      profileImageData.value = {
        key: response.image_key,
        url: response.image_url,
      }
    }
    else {
      localProfileImage.uploadFailed = true
    }
  }
  catch (error) {
    localProfileImage.uploadFailed = true
    errorToast(`${t('errors/file_errors/upload_error')} ${error}`)
  }
}

function handleFileSelect(event: Event) {
  const input = event.target as HTMLInputElement
  if (input.files?.[0]) {
    const file = input.files[0]
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string
      cropperModal.value = true
      localProfileImage.rawFile = imagePreview.value
      localProfileImage.file = file
      localProfileImage.isLoaded = false
      localProfileImage.uploadFailed = false
    }
    reader.readAsDataURL(file)
  }
}

function confirmCrop() {
  if (cropper.value) {
    const result = cropper.value.getResult()
    if (result.canvas) {
      result.canvas.toBlob(async (blob) => {
        if (blob) {
          const file = new File([blob], 'profile-image.jpg', {
            type: 'image/jpeg',
            lastModified: Date.now(),
          })
          localProfileImage.file = file
          await uploadProfileImage(file)
          cropperModal.value = false
        }
      }, 'image/jpeg')
    }
  }
}

async function removeImage() {
  if (localProfileImage.key) {
    try {
      await nuxt.$api(`/api/organiser/images/delete/${localProfileImage.key}`, {
        method: 'DELETE',
      })
    }
    catch (erorr) {
      errorToast(`${t('errors/file_errors/delete_image_error')} ${erorr}`)
    }
  }
  resetCroperState()
}

function resetCroperState() {
  Object.assign(localProfileImage, {
    isLoaded: false,
    uploadFailed: false,
    rawFile: undefined,
    url: undefined,
    key: undefined,
    file: undefined,
  })
  if (profileImageData.value) {
    profileImageData.value.key = ''
    profileImageData.value.url = ''
  }
  if (fileInput.value)
    fileInput.value.value = ''
}

async function retryCoverUpload() {
  if (!localProfileImage.file) {
    errorToast(`${t('errors/file_errors/retry_fail')}`)
    return
  }

  // Reset state for new attempt
  localProfileImage.isLoaded = false
  localProfileImage.uploadFailed = false

  await uploadProfileImage(localProfileImage.file)
}
</script>

<template>
  <v-dialog v-model="cropperModal" max-width="800">
    <!-- @focusout="resetCroperState"> -->
    <v-card>
      <v-card-title class="text-base-medium mt-1 text-center">
        {{ $t('organiser.data.profile_image.crop') }}
      </v-card-title>
      <v-card-text>
        <div class="md:h-96 w-full relative">
          <Cropper
            ref="cropper"
            :src="imagePreview"
            :stencil-props="{ aspectRatio: cropperAspectRatio }"
            :stencil-component="CircleStencil"
            :resize-image="{ adjustStencil: false }"
            default-boundaries="fit"
          />
        </div>
      </v-card-text>
      <v-card-actions>
        <v-btn color="error" class="text-base-medium" @click="() => { resetCroperState(), cropperModal = false }">
          {{ $t('organiser.data.profile_image.cancel') }}
        </v-btn>
        <v-btn color="primary" class="text-base-medium" @click="confirmCrop">
          {{ $t('organiser.data.profile_image.confirm_crop') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <!-- Image Upload Section -->
  <div class="space-y-4 mb-6">
    <input
      id="cover-image-input"
      ref="fileInput"
      type="file"
      accept="image/*"
      hidden
      @change="handleFileSelect"
    >
    <label
      v-if="!localProfileImage.url"
      for="cover-image-input"
      class="block w-full px-4 py-6 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-primary transition-basic text-center"
    >
      <div class="flex flex-col items-center justify-center space-y-2">
        <ImageAdd02Icon :size="48" class="text-slate-400" />
        <span class="text-lg-medium text-slate-600">
          {{ $t('organiser.data.profile_image.profile_image') }}
        </span>
        <span class="text-sm text-slate-400">
          {{ $t('organiser.data.profile_image.profile_image_info') }}
        </span>
      </div>
    </label>

    <!-- Preview -->
    <div v-else>
      <div class="relative aspect-w-16 aspect-h-9 rounded-lg overflow-hidden border border-slate-600">
        <img
          :src="localProfileImage.url || localProfileImage.rawFile"
          alt="Cover preview"
          class="object-cover w-full h-full"
        >
        <!-- Upload Status Overlay -->
        <div
          v-if="!localProfileImage.isLoaded && !localProfileImage.uploadFailed"
          class="z-10 h-full w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        >
          <v-overlay :model-value="true" class="align-center justify-center" contained>
            <v-progress-circular indeterminate color="white" size="120" width="10" />
          </v-overlay>
        </div>
        <!-- Action Buttons -->
        <div class="absolute z-10 top-3 right-3 mx-2 my-2 grid grid-cols-2 gap-2">
          <v-btn
            :icon="getCloudIcon(localProfileImage)"
            size="small"
            :color="localProfileImage.isLoaded ? 'success' : localProfileImage.uploadFailed ? 'error' : 'primary'"
            :disabled="!localProfileImage.uploadFailed"
            @click="retryCoverUpload"
          />
          <v-btn icon color="#dc2626" size="small" @click="removeImage">
            <Delete02Icon />
          </v-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
