GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.0)
      actionpack (= 8.0.0)
      activesupport (= 8.0.0)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.0)
      actionpack (= 8.0.0)
      activejob (= 8.0.0)
      activerecord (= 8.0.0)
      activestorage (= 8.0.0)
      activesupport (= 8.0.0)
      mail (>= 2.8.0)
    actionmailer (8.0.0)
      actionpack (= 8.0.0)
      actionview (= 8.0.0)
      activejob (= 8.0.0)
      activesupport (= 8.0.0)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.0)
      actionview (= 8.0.0)
      activesupport (= 8.0.0)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.0)
      actionpack (= 8.0.0)
      activerecord (= 8.0.0)
      activestorage (= 8.0.0)
      activesupport (= 8.0.0)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.0)
      activesupport (= 8.0.0)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.0)
      activesupport (= 8.0.0)
      globalid (>= 0.3.6)
    activemodel (8.0.0)
      activesupport (= 8.0.0)
    activerecord (8.0.0)
      activemodel (= 8.0.0)
      activesupport (= 8.0.0)
      timeout (>= 0.4.0)
    activerecord-import (2.1.0)
      activerecord (>= 4.2)
    activestorage (8.0.0)
      actionpack (= 8.0.0)
      activejob (= 8.0.0)
      activerecord (= 8.0.0)
      activesupport (= 8.0.0)
      marcel (~> 1.0)
    activesupport (8.0.0)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1084.0)
    aws-sdk-core (3.222.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.99.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.183.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.0)
    bigdecimal (3.1.8)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (6.2.2)
      racc
    builder (3.3.0)
    byebug (11.1.3)
    cancancan (3.6.1)
    chunky_png (1.4.0)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    css_parser (1.21.0)
      addressable
    date (3.4.0)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-jwt (0.12.1)
      devise (~> 4.0)
      warden-jwt_auth (~> 0.10)
    dotenv (3.1.4)
    drb (2.2.1)
    dry-auto_inject (1.0.1)
      dry-core (~> 1.0)
      zeitwerk (~> 2.6)
    dry-configurable (1.2.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.2)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    ed25519 (1.3.0)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    io-console (0.7.2)
    irb (1.14.1)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    json (2.8.2)
    jsonapi-serializer (2.2.0)
      activesupport (>= 4.2)
    jwt (2.9.3)
      base64
    kamal (2.3.0)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.2)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    language_server-protocol (3.17.0.3)
    logger (1.6.1)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mini_mime (1.1.5)
    minitest (5.25.1)
    msgpack (1.7.5)
    net-http (0.6.0)
      uri
    net-imap (0.5.1)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    parallel (1.26.3)
    parser (3.3.6.0)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pg (1.5.9)
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-qrcode (0.5.2)
      prawn (>= 1)
      rqrcode (>= 1.0.0)
    prawn-svg (0.36.2)
      css_parser (~> 1.6)
      matrix (~> 0.4.2)
      prawn (>= 0.11.1, < 3)
      rexml (>= 3.3.9, < 4)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    psych (5.2.0)
      stringio
    public_suffix (6.0.1)
    puma (6.4.3)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.8)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.0)
      actioncable (= 8.0.0)
      actionmailbox (= 8.0.0)
      actionmailer (= 8.0.0)
      actionpack (= 8.0.0)
      actiontext (= 8.0.0)
      actionview (= 8.0.0)
      activejob (= 8.0.0)
      activemodel (= 8.0.0)
      activerecord (= 8.0.0)
      activestorage (= 8.0.0)
      activesupport (= 8.0.0)
      bundler (>= 1.15.0)
      railties (= 8.0.0)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (8.0.0)
      actionpack (= 8.0.0)
      activesupport (= 8.0.0)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.8.0)
      psych (>= 4.0.0)
    receipts (2.4.0)
      prawn (>= 1.3.0, < 3.0.0)
      prawn-table (~> 0.2.1)
    redis-client (0.23.0)
      connection_pool
    regexp_parser (2.9.2)
    reline (0.5.11)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.3.9)
    rollbar (3.6.1)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rubocop (1.68.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rubocop-ast (>= 1.32.2, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.36.1)
      parser (>= *******)
    rubocop-factory_bot (2.26.1)
      rubocop (~> 1.61)
    rubocop-i18n (3.1.0)
      rubocop (~> 1.0)
    rubocop-minitest (0.36.0)
      rubocop (>= 1.61, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-performance (1.23.0)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.27.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails-omakase (1.0.0)
      rubocop
      rubocop-minitest
      rubocop-performance
      rubocop-rails
    rubocop-thread_safety (0.6.0)
      rubocop (>= 1.48.1)
    ruby-progressbar (1.13.0)
    securerandom (0.3.2)
    sidekiq (7.3.7)
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-cron (2.0.1)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    solid_cable (3.0.2)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.6)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.0.2)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sshkit (1.23.2)
      base64
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stringio (3.1.2)
    stripe (13.4.1)
    thor (1.3.2)
    thruster (0.1.9-x86_64-linux)
    timeout (0.4.2)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode (*******)
    unicode-display_width (2.6.0)
    uri (1.0.2)
    useragent (0.16.10)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-jwt_auth (0.10.0)
      dry-auto_inject (>= 0.8, < 2)
      dry-configurable (>= 0.13, < 2)
      jwt (~> 2.1)
      warden (~> 1.2)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.1)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  activerecord-import
  aws-sdk-s3
  bootsnap
  brakeman
  byebug
  cancancan
  devise
  devise-jwt
  factory_bot_rails
  faker
  faraday
  jsonapi-serializer
  kamal
  pg (~> 1.1)
  prawn
  prawn-qrcode
  prawn-svg (~> 0.36.2)
  puma (>= 5.0)
  rack-cors
  rails (~> 8.0.0)
  receipts
  rollbar (~> 3.6)
  rqrcode (~> 2.0)
  rubocop-factory_bot
  rubocop-i18n
  rubocop-performance
  rubocop-rails
  rubocop-rails-omakase
  rubocop-thread_safety
  sidekiq
  sidekiq-cron
  solid_cable
  solid_cache
  solid_queue
  stripe
  thruster
  tzinfo-data

BUNDLED WITH
   2.5.16
