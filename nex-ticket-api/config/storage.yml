test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

# Use bin/rails credentials:edit to set the AWS secrets (as aws:access_key_id|secret_access_key)
hetzner:
  service: S3
  endpoint: <%= ENV['HETZNER_S3_ENDPOINT'] || "https://fsn1.your-objectstorage.com"  %>
  access_key_id: <%= ENV['HETZNER_S3_ACCESS_KEY_ID'] %>
  secret_access_key: <%= ENV['HETZNER_S3_SECRET_ACCESS_KEY'] %>
  region: <%= ENV['HETZNER_S3_REGION'] || 'fsn1' %>
  bucket: <%= ENV['HETZNER_S3_BUCKET'] || 'test-ticketpie' %>

# Remember not to checkin your GCS keyfile to a repository
# google:
#   service: GCS
#   project: your_project
#   credentials: <%= Rails.root.join("path/to/gcs.keyfile") %>
#   bucket: your_own_bucket-<%= Rails.env %>

# Use bin/rails credentials:edit to set the Azure Storage secret (as azure_storage:storage_access_key)
# microsoft:
#   service: AzureStorage
#   storage_account_name: your_account_name
#   storage_access_key: <%= Rails.application.credentials.dig(:azure_storage, :storage_access_key) %>
#   container: your_container_name-<%= Rails.env %>

# mirror:
#   service: Mirror
#   primary: local
#   mirrors: [ amazon, google, microsoft ]
