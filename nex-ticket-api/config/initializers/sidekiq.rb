require "sidekiq"
require "sidekiq/web"
require "sidekiq/cron/web"
require "rack/session/cookie"

# Standard Sidekiq client/server Redis config (as you likely already have)
Sidekiq.configure_client do |config|
  config.redis = { url: ENV.fetch("REDIS_URL", "redis://localhost:6379/0") }
end

Sidekiq.configure_server do |config|
  config.redis = { url: ENV.fetch("REDIS_URL", "redis://localhost:6379/0") }
end

schedule_file = Rails.root.join "config/schedule.yml"

if File.exist?(schedule_file) && Sidekiq.server?
  Sidekiq::Cron::Job.load_from_hash! YAML.load_file(schedule_file)
end

Sidekiq::Web.use Rack::Session::Cookie, secret: Rails.application.secret_key_base || ENV["SECRET_KEY_BASE"],
                                       same_site: :lax,
                                       max_age: 86400
