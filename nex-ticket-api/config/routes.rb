Rails.application.routes.draw do
  namespace :organisers, path: :organiser do
    # Events
    resources :events

    # Tags
    resources :tags

    # Ticket Types
    resources :ticket_types

    # Events and Ticket Types Promo Codes
    scope :event_tickets_promo_code do
      get "events", to: "event_tickets_promo_code#events"
      get "tickets", to: "event_tickets_promo_code#tickets"
    end

    # Images
    scope :images do
      post :upload, to: "images#upload"
      delete "delete/:key", to: "images#delete"
    end

    # Organiser
    put :edit, to: "organisers#update"
    get :show, to: "organisers#show"

    # Reporting
    scope :reporting do
      get "events/:id", to: "reporting#event"
      get "organiser", to: "reporting#organiser"
    end

    # Promo Codes
    resources :promo_codes
  end

  namespace :public do
    # SearchBar
    get "/search", to: "search#search"

    # Events
    resources :events, only: [ :index, :show ] do
      member do
        post "save", to: "events#save_event"
      end
    end

    # Basket
    resource :basket, only: [ :show ] do
      delete "remove_item/:id", to: "baskets#remove_item", as: "remove_item"
      post "update_item", to: "baskets#update_item", as: "update_item"
    end

    # Promo codes
    resources :orders do
      member do
        patch :apply_promo_code
        patch :remove_promo_code
      end
    end

    # Grouped Events
    get "grouped_events", to: "grouped_events#index"

    # Contact Event Promoter
    post "contact_form", to: "contact_form#send_message_to_promoter"

    # Contact TicketPie Support
    post "contact_support", to: "contact_support#send_message_to_support"

    # Saved Events
    post "save_event", to: "saved_events#save_event"
    get "saved_events", to: "saved_events#index"
    delete "saved_event/:id", to: "saved_events#destroy"

    # Ticket_types
    resources :ticket_types, only: [ :index ]

    # Stripe Payments
    post "create_stripe_checkout_session", to: "stripe_payments#create_stripe_checkout_session"
    post "stripe_webhook", to: "stripe_payments#stripe_webhook"

    # Tag Categories
    resources :tag_categories, only: [ :index, :show ] do
      member do
        post "show_tags", to: "tag_categories#show_tags"
      end
    end
    get "tag_categories/by_name/:name", to: "tag_categories#by_name"
  end

  namespace :admin do
    # Cities
    resources :cities, controller: "city_search"

    # Tag Categories
    resources :tag_categories

    # Tags
    resources :tags

    mount Sidekiq::Web => "/sidekiq"
    # Mail Subscriptions
    resources :mail_subscriptions
  end

  # What is this for?
  get "/current_user", to: "current_user#index"

  # Global Public

  # Cities
  get "cities/current", to: "city_search#get_current"
  get "cities", to: "city_search#index"
  get "cities/:id", to: "city_search#show"

  # Countries
  resources :states, only: [ :index, :show ]

  # Currencies
  resources :currencies



  devise_for :users, path: "", path_names: {
    sign_in: "login",
    sign_out: "logout",
    registration: "signup"
  },
  controllers: {
    sessions: "users/sessions",
    registrations: "users/registrations"
  }
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html
  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"

  get "preview_email", to: "user_mailer#preview"
end
