update_ticket_prices:
  cron: "*/10 * * * *"
  class: "UpdateTicketTypeEffectivePricesJob" 
  queue: default
  description: "Updates the effective prices for all ticket types every 10 minutes."

delete_expired_baskets:
  cron: "*/10 * * * *"           
  class: "DeleteExpiredBasketsJob"
  queue: default               
  description: "Deletes baskets whose expires_at timestamp is in the past."

cancel_abandoned_orders:
  cron: "*/10 * * * *"             
  class: "CancelAbandonedOrdersJob"
  queue: default               
  description: "Cancels orders that have been abandoned for more than 2 hours."  