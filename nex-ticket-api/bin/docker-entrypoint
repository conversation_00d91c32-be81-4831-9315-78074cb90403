#!/bin/bash -e
set -e

SECRET_ENV_FILE="/run/secrets/container_secrets.env"
CONFIG_ENV_FILE="/run/configs/container_configs.env"

load_env_file() {
  local env_file="$1"
  if [ -f "$env_file" ]; then
    echo "Sourcing environment variables from: $env_file"
    set -a
    . "$env_file"
    set +a
  else
    echo "Environment file not found, skipping: $env_file"
  fi
}

load_env_file "$CONFIG_ENV_FILE"
load_env_file "$SECRET_ENV_FILE"

echo "Environment variables sourced. Starting application..."

# Enable jemalloc for reduced memory usage and latency.
if [ -z "${LD_PRELOAD+x}" ]; then
    LD_PRELOAD=$(find /usr/lib -name libjemalloc.so.2 -print -quit)
    export LD_PRELOAD
fi

# If running the rails server then create or migrate existing database
if [ "${@: -2:1}" == "./bin/rails" ] && [ "${@: -1:1}" == "server" ]; then
  echo "Running migrations..."
  ./bin/rails db:prepare
  echo "Migrations completed."
fi

exec "${@}"
