# README

## Quick settup

Server should be running and you can find it after that on [localhost:3000/api/](localhost:3000/api/). But in case you need to run it manually use this command or alias
```
railss
# or
rails server -b 0.0.0.0
```

## First startup

You need to create the databases rails requires and run migrations
```
rails db:create
rails db:migrate
```


## Default readme
This README would normally document whatever steps are necessary to get the
application up and running.

Things you may want to cover:

* Ruby version

* System dependencies

* Configuration

* Database creation

* Database initialization

* How to run the test suite

* Services (job queues, cache servers, search engines, etc.)

* Deployment instructions

* ...
