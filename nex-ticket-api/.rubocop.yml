require:
  - rubocop-rails
  - rubocop-performance
  - rubocop-factory_bot
  - rubocop-i18n
  - rubocop-thread_safety

inherit_gem:
  rubocop-rails-omakase: rubocop.yml
  

inherit_from: .rubocop_todo.yml


AllCops:
  TargetRubyVersion: 3.3
  TargetRailsVersion: 8.0
  NewCops: enable
  SuggestExtensions: false

  Exclude:
    - 'db/schema.rb'
    - 'db/migrate/*'
    - 'bin/*'
    - 'node_modules/**/*'
    - 'vendor/**/*'
    - 'tmp/**/*'
    - 'log/**/*'
    - 'public/**/*'
    - 'config/initializers/devise.rb'
    - 'config/environments/*'
    - 'config/application.rb'
    - 'config/puma.rb'
    - 'config/cable.yml'
    - 'config/storage.yml'
    - 'config/database.yml'


Layout/LineLength:
  Max: 120

Layout/IndentationWidth:
  Enabled: true

Layout/IndentationConsistency:
  Enabled: true

Security/Eval:
  Enabled: true
Security/MarshalLoad:
  Enabled: true
Security/YAMLLoad:
  Enabled: true

Rails:
  Enabled: true

FactoryBot:
  Enabled: true

I18n:
  Enabled: true
I18n/GetText/DecorateFunctionMessage:
  Enabled: false
I18n/GetText/DecorateStringFormattingUsingInterpolation:
  Enabled: false
I18n/GetText/DecorateStringFormattingUsingPercent:
  Enabled: false
I18n/GetText/DecorateString:
  Enabled: false

Performance:
  Enabled: true

ThreadSafety:
  Enabled: true

Rails/UnknownEnv:
  Environments:
    - development
    - test
    - production
    - staging    


