require "test_helper"

class PromoCodeTest < ActiveSupport::TestCase
  self.use_transactional_tests = false

  def setup
    # Create organiser without using fixtures
    @organiser = Organiser.create!(
      name: "Test Organiser",
      contact_email: "<EMAIL>",
      contact_mobile: "123456789"
    )

    @promo_code = PromoCode.new(
      code: "TEST10",
      discount_type: :percentage_total,
      discount_value: 10,
      organiser: @organiser,
      active: true,
      applicable_event_ids: [],
      applicable_ticket_type_ids: []
    )
  end

  def teardown
    PromoCode.delete_all
    Organiser.delete_all
  end

  test "should validate with translated error messages in English" do
    I18n.with_locale(:en) do
      # Test max_uses validation
      @promo_code.max_uses = 5
      @promo_code.uses_count = 10
      @promo_code.save

      # Trigger the validation
      @promo_code.valid?

      # Check that the error message is translated
      assert @promo_code.errors[:max_uses].any?
      error_message = @promo_code.errors[:max_uses].first
      assert_includes error_message, "cannot be set below current usage"
    end
  end

  test "should validate with translated error messages in Slovak" do
    I18n.with_locale(:sk) do
      # Test max_uses validation
      @promo_code.max_uses = 5
      @promo_code.uses_count = 10
      @promo_code.save

      # Trigger the validation
      @promo_code.valid?

      # Check that the error message is translated to Slovak
      assert @promo_code.errors[:max_uses].any?
      error_message = @promo_code.errors[:max_uses].first
      assert_includes error_message, "nemožno nastaviť pod aktuálne použitie"
    end
  end

  test "should validate discount value with translated error messages" do
    I18n.with_locale(:en) do
      @promo_code.discount_value = 150  # Over 100%
      @promo_code.valid?

      assert @promo_code.errors[:discount_value].any?
      error_message = @promo_code.errors[:discount_value].first
      assert_includes error_message, "cannot exceed 100% for percentage discounts"
    end

    I18n.with_locale(:sk) do
      @promo_code.discount_value = 150  # Over 100%
      @promo_code.valid?

      assert @promo_code.errors[:discount_value].any?
      error_message = @promo_code.errors[:discount_value].first
      assert_includes error_message, "nemôže presiahnuť 100% pre percentuálne zľavy"
    end
  end

  test "should validate date range with translated error messages" do
    I18n.with_locale(:en) do
      @promo_code.valid_from = 1.day.from_now
      @promo_code.valid_until = 1.day.ago
      @promo_code.valid?

      assert @promo_code.errors[:valid_until].any?
      error_message = @promo_code.errors[:valid_until].first
      assert_includes error_message, "must be after valid_from"
    end

    I18n.with_locale(:sk) do
      @promo_code.valid_from = 1.day.from_now
      @promo_code.valid_until = 1.day.ago
      @promo_code.valid?

      assert @promo_code.errors[:valid_until].any?
      error_message = @promo_code.errors[:valid_until].first
      assert_includes error_message, "musí byť po valid_from"
    end
  end
end
