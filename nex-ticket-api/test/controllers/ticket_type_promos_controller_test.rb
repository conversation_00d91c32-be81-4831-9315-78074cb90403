require "test_helper"

class TicketTypePromosControllerTest < ActionDispatch::IntegrationTest
  setup do
    @ticket_type_promo = ticket_type_promos(:one)
  end

  test "should get index" do
    get ticket_type_promos_url, as: :json
    assert_response :success
  end

  test "should create ticket_type_promo" do
    assert_difference("TicketTypePromo.count") do
      post ticket_type_promos_url, params: { ticket_type_promo: {} }, as: :json
    end

    assert_response :created
  end

  test "should show ticket_type_promo" do
    get ticket_type_promo_url(@ticket_type_promo), as: :json
    assert_response :success
  end

  test "should update ticket_type_promo" do
    patch ticket_type_promo_url(@ticket_type_promo), params: { ticket_type_promo: {} }, as: :json
    assert_response :success
  end

  test "should destroy ticket_type_promo" do
    assert_difference("TicketTypePromo.count", -1) do
      delete ticket_type_promo_url(@ticket_type_promo), as: :json
    end

    assert_response :no_content
  end
end
