FactoryBot.define do
  factory :organiser do
    name { Faker::Company.name }
    contact_email { Faker::Internet.email }
    contact_mobile { Faker::PhoneNumber.phone_number }
    reg_number { Faker::Company.czech_organisation_number }
    vat_number { Faker::Company.swedish_organisation_number }
    state_id { State.pluck(:id).sample }
    profit_share { Faker::Number.decimal(l_digits: 2) }
    default_currency { Faker::Currency.code }
  end
end
