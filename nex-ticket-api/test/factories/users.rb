FactoryBot.define do
  factory :user do
    email { Faker::Internet.email }
    password { Faker::Internet.password(min_length: 8) }
    type { "Customer" }
    confirmed_at { Time.zone.now }

    initialize_with { type.constantize.new }

    trait :customer do
      type { "Customer" }
    end

    trait :organiser_account do
      type { "OrganiserAccount" }
      organiser { { strategy: :create } }
    end
  end
end
