class AddTypeToUsersAndRemoveOrgAcc < ActiveRecord::Migration[8.0]
  def up
    add_column :users, :user_type, :integer, default: 0, null: true
    add_reference :users, :organiser, null: true, foreign_key: true
    drop_table :organiser_accounts
  end

  def down
    remove_column :users, :user_type
    remove_reference :users, :organiser, null: true, foreign_key: true
    create_table :organiser_accounts do |t|
      t.references :user, null: false, foreign_key: true
      t.references :organiser, null: false, foreign_key: true
      t.timestamps
    end
  end
end
