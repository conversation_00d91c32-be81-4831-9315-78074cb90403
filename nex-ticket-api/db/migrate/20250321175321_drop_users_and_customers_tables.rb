# db/migrate/[timestamp]_drop_users_and_customers_tables.rb

class DropUsersAndCustomersTables < ActiveRecord::Migration[7.0]
  def up
    # Drop foreign key constraints *explicitly*.  This is the key fix.

    # Constraints referencing *customers* table.
    remove_foreign_key :baskets, :customers
    remove_foreign_key :orders, :customers
    remove_foreign_key :saved_events, :customers

    # Constraints referencing the *users* table.
    remove_foreign_key :customers, :users

    # Drop the tables themselves, now that dependencies are gone.
    drop_table :users
    drop_table :customers
  end

  def down
    # Recreate the tables (as before).
    create_table "users", force: :cascade do |t|
      t.string "email", default: "", null: false
      t.string "encrypted_password", default: "", null: false
      t.string "reset_password_token"
      t.datetime "reset_password_sent_at"
      t.datetime "remember_created_at"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.string "jti", null: false
      t.string "first_name", null: false
      t.string "last_name", null: false
      t.integer "user_type", default: 0
      t.bigint "organiser_id"
      t.index [ "email" ], name: "index_users_on_email", unique: true
      t.index [ "jti" ], name: "index_users_on_jti", unique: true
      t.index [ "organiser_id" ], name: "index_users_on_organiser_id"
      t.index [ "reset_password_token" ], name: "index_users_on_reset_password_token", unique: true
    end

    create_table "customers", force: :cascade do |t|
      t.string "mobile", default: "", null: false
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.bigint "user_id"
      t.string "visitor_token"
      t.index [ "user_id" ], name: "index_customers_on_user_id"
      t.index [ "visitor_token" ], name: "index_customers_on_visitor_token", unique: true
    end

    # Re-add the foreign keys *after* recreating the tables.
    add_foreign_key "baskets", "customers"
    add_foreign_key "orders", "customers"
    add_foreign_key "saved_events", "customers"
    add_foreign_key "customers", "users"
    add_foreign_key "users", "organisers" # from previous answer
  end
end
