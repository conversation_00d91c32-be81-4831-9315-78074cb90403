class CreateOrderIdSequence < ActiveRecord::Migration[8.0]
  def up
    # Create the sequence with 12-digit constraints
    execute <<~SQL
      CREATE SEQUENCE order_id_seq
      START WITH 100000000000   -- Smallest 12-digit number (no leading zero)
      INCREMENT BY 1
      MINVALUE 100000000000     -- Prevent going below 12 digits
      MAXVALUE 999999999999     -- Largest 12-digit number
      NO CYCLE;                 -- Stop at MAXVALUE (don't restart)
    SQL
  end

  def down
    execute "DROP SEQUENCE IF EXISTS order_id_seq"
  end
end
