class CreatePromoCodes < ActiveRecord::Migration[8.0]
  def change
    create_table :promo_codes do |t|
      # Core Fields
      t.string :code, null: false
      t.string :description
      t.integer :discount_type, null: false
      t.decimal :discount_value, precision: 10, scale: 2, null: false

      # Validity & Usage Limits
      t.datetime :valid_from
      t.datetime :valid_until
      t.integer :max_uses
      t.integer :uses_count, null: false, default: 0
      t.integer :max_uses_per_user

      # Conditions
      t.decimal :min_order_amount, precision: 10, scale: 2

      t.bigint :applicable_event_ids, array: true, default: []
      t.bigint :applicable_ticket_type_ids, array: true, default: []

      # Status
      t.boolean :active, null: false, default: true

      t.timestamps
    end

    add_index :promo_codes, :code, unique: true
    add_index :promo_codes, :applicable_event_ids, using: :gin
    add_index :promo_codes, :applicable_ticket_type_ids, using: :gin
    add_index :promo_codes, :active
  end
end
