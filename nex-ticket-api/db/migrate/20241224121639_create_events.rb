class CreateEvents < ActiveRecord::Migration[8.0]
  def change
    create_table :events do |t|
       # Basic information
       t.string :name, null: false
       t.text :description, null: false

       # Date and time (From-To)
       t.datetime :start_time, null: false
       t.datetime :end_time, null: false

       # Organiser/Promoter
       t.references :organiser, null: false, foreign_key: true

       # Venue
       t.string :venue_name, null: false
       t.text :venue_address, null: false
       t.decimal :latitude, precision: 10, scale: 6, null: false
       t.decimal :longitude, precision: 10, scale: 6, null: false

       # Social media links: store them as JSON or simple text
       t.jsonb :social_media_links, array: true, default: []

       t.jsonb :policies, array: true, default: []

       # Gallery/Photos
       t.text :photo_gallery, array: true, default: []

       # Main photo/logo for the event
       t.text :main_photo_url, null: false

       t.text :google_maps_link

       t.timestamps
    end
  end
end
