class CreateTicketTypes < ActiveRecord::Migration[8.0]
  def change
    create_table :ticket_types do |t|
      t.references :event, null: false, foreign_key: true
      t.integer :max_amount, null: false
      t.integer :available_amount

      # Using decimal for currency/prices:
      t.decimal :price, precision: 10, scale: 2, null: false
      # Array of JSON structures for multiple discount periods
      # Each structure can have { "percentage": X, "start_date": DateTime, "end_date": DateTime }
      t.jsonb :discounts, default: [], null: false
      t.jsonb :features, default: [], null: false

      # Renamed from 'Type' to 'ticket_category' to avoid STI conflicts
      t.string :ticket_category, null: false

      # Name of the ticket
      t.string :name, null: false

      t.timestamps
    end
  end
end
