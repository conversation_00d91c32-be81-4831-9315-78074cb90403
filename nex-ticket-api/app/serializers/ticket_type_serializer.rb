class TicketTypeSerializer
  include JSONAPI::Serializer

  attributes :price, :name, :description, :available_amount

  attribute :disabled, if: Proc.new { |record, params|params && params[:organiser] == true }

  attribute :max_amount, if: Proc.new { |record, params| params[:admin] || params[:organiser] }
  # attribute :available_amount, if: Proc.new { |record, params| params[:admin] || params[:organiser] }
  attribute :discounts, if: Proc.new { |record, params| params[:admin] || params[:organiser] }
  attribute :paid_for_amount, if: Proc.new { |record, params| params[:admin] || params[:organiser] }

  attribute :discounted_price do |object|
    object.discounted_price.to_d
  end

  attribute :availability_status do |object|
    object.availability_status
  end

  set_key_transform :camel_lower
end
