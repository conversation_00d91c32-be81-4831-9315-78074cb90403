class OrderItemSerializer
  include JSONAPI::Serializer

  attributes :order_itemable_id, :order_itemable_type, :quantity, :price_per_piece, :total_price
  attribute :organiser_price_per_piece, if: Proc.new { |record, params| params[:admin] || params[:organiser] }
  attribute :platform_fee_per_piece, if: Proc.new { |record, params| params[:admin] || params[:organiser] }

  attribute :name do |object|
    object.itemable_name
  end

  belongs_to :order_itemable, polymorphic: true
end
