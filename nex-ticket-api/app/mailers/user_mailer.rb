class UserMailer < ApplicationMailer
  default from: "<EMAIL>"

  def send_welcome_mail
    @user = params[:user]
    mail(to: @user.email, subject: "TicketPie Registration")
  end

  def send_ticket_mail
    @order = params[:order]
    @event = @order.event

    pdf_data = TicketsPdfService.new.create_pdf(@order)
    attachments["ticket_#{@order.id}.pdf"] = pdf_data

    mail(to: @order.email, subject: "Your Ticket for #{@event.name}")
  end
end
