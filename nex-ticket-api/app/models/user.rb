class User < ApplicationRecord
  include Devise::JWT::RevocationStrategies::JTIMatcher

  devise :database_authenticatable, :registerable, :validatable,
         :recoverable, :validatable, :confirmable, :lockable, :trackable,
         :jwt_authenticatable, jwt_revocation_strategy: self

  def is_organiser?
    type == "OrganiserAccount"
  end

  def is_customer?
    type == "Customer"
  end

  def is_scanner?
    type == "ScannerAccount"
  end

  def is_admin?
    false
  end

  def user_type
    case type
    when "OrganiserAccount"
      "organiser"
    when "Customer"
      "customer"
    when "ScannerAccount"
      "scanner"
    end
  end
end
