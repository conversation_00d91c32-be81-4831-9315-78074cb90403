class TagCategory < ApplicationRecord
  has_and_belongs_to_many :tags

  # Ensure the name is always present
  validates :name, presence: true

  # Ensure the name is unique (case-insensitive)
  validates :name, uniqueness: { case_sensitive: false }

  # Ensure the name has a reasonable length
  validates :name, length: { minimum: 2, maximum: 50 }

  # Ensure the name does not contain leading or trailing spaces
  validates :name, format: { without: /\A\s|\s\z/, message: "cannot have leading or trailing spaces" }

  # Ensure the name is not a reserved word
  validates :name, exclusion: { in: %w[admin superuser], message: "%{value} is reserved." }
end
