# app/models/promo_code.rb
class PromoCode < ApplicationRecord
  has_many :orders
  belongs_to :organiser

  enum :discount_type, {
    percentage_total: 0,
    fixed_amount_total: 1
  }

  # --- Validations ---
  validates :code, presence: true, uniqueness: { scope: :organiser_id, case_sensitive: true }
  validates :discount_type, presence: true
  validates :discount_value, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :uses_count, numericality: { greater_than_or_equal_to: 0 }
  validates :max_uses, numericality: { greater_than: 0 }, allow_nil: true
  validates :max_uses_per_user, numericality: { greater_than: 0 }, allow_nil: true
  validates :min_order_amount, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :active, inclusion: [ true, false ]

  validate :valid_dates
  validate :valid_until_cannot_be_in_past, on: [ :create, :update ]
  validate :discount_value_appropriate
  validate :applicability_ids_are_valid
  validate :check_applicability_ids_belong_to_organiser
  validate :max_uses_not_less_than_uses_count, on: :update

  scope :active, -> { where(active: true) }
  scope :available, ->(time = Time.current) {
    active
      .where("valid_from IS NULL OR valid_from <= ?", time)
      .where("valid_until IS NULL OR valid_until >= ?", time)
      .where("max_uses IS NULL OR uses_count < max_uses")
  }

  def strategy
    case discount_type.to_sym
    when :percentage_total then PromoStrategies::PercentageTotal
    when :fixed_amount_total then PromoStrategies::FixedAmountTotal
    else PromoStrategies::NullStrategy
    end
  end

  def applicable_to?(order)
    return false unless is_valid?

    base_subtotal = order.subtotal_without_vat
    if min_order_amount.present? && base_subtotal < min_order_amount
      return false
    end

    if applicable_event_ids.present? && applicable_event_ids.any?
      order_event_ids = order.order_items.filter_map(&:event_id).uniq
      unless (order_event_ids & applicable_event_ids).any?
        return false
      end
    end

    if applicable_ticket_type_ids.present? && applicable_ticket_type_ids.any?
      order_ticket_type_ids = order.order_items.filter_map(&:ticket_type_id).uniq
      unless (order_ticket_type_ids & applicable_ticket_type_ids).any?
        return false
      end
    end

    if max_uses_per_user.present? && order.customer.present?
      used_count = Order.where(promo_code_id: self.id, user_id: order.user_id)
                        .where(status: :success)
                        .count
      return false if used_count >= max_uses_per_user
    end

    true
  end

  def calculate_discount(order)
    discount = strategy.calculate(self, order)
    base_subtotal = order.base_subtotal_amount || order.calculate_items_sum
    [ discount.to_d, base_subtotal.to_d ].min.round(2)
  end

  def is_valid?(time = Time.current)
    is_active? && !is_expired?(time) && !is_usage_exceeded?
  end

  def is_active?
    active?
  end

  def is_expired?(time = Time.current)
    (valid_from.present? && valid_from > time) ||
    (valid_until.present? && valid_until < time)
  end

  def is_usage_exceeded?
    max_uses.present? && uses_count >= max_uses
  end

  def increment_usage!
    # rubocop:disable Rails/SkipsModelValidations
    PromoCode.update_counters(id, uses_count: 1)
  end

  private

    def max_uses_not_less_than_uses_count
      if max_uses.present? && max_uses_changed? && max_uses < uses_count
        errors.add(:max_uses, I18n.t("models.promo_code.max_uses_below_current", count: uses_count))
      end
    end

    def valid_until_cannot_be_in_past
      if valid_until.present? && valid_until_changed? && valid_until < Time.current
        errors.add(:valid_until, I18n.t("models.promo_code.valid_until_in_past"))
      end
    end

    def check_applicability_ids_belong_to_organiser
      return if organiser.blank?

      if applicable_event_ids.present? && (new_record? || applicable_event_ids_changed?)
        valid_event_ids = organiser.events.where(id: applicable_event_ids).pluck(:id)
        invalid_event_ids = applicable_event_ids - valid_event_ids
        if invalid_event_ids.any?
          errors.add(:applicable_event_ids,
I18n.t("models.promo_code.invalid_event_ids", ids: invalid_event_ids.join(", ")))
        end
      end

      if applicable_ticket_type_ids.present? && (new_record? || applicable_ticket_type_ids_changed?)
        valid_ticket_type_ids = TicketType.joins(:event)
                                          .where(events: { organiser_id: self.organiser_id })
                                          .where(id: applicable_ticket_type_ids)
                                          .pluck(:id)
        invalid_ticket_type_ids = applicable_ticket_type_ids - valid_ticket_type_ids
        if invalid_ticket_type_ids.any?
          errors.add(:applicable_ticket_type_ids,
            I18n.t("models.promo_code.invalid_ticket_type_ids", ids: invalid_ticket_type_ids.join(", ")))
        end
      end
    end

    def valid_dates
      if valid_from.present? && valid_until.present? && valid_from > valid_until
        errors.add(:valid_until, I18n.t("models.promo_code.valid_until_before_from"))
      end
    end

    def discount_value_appropriate
      if percentage_total? && discount_value&.>(100)
        errors.add(:discount_value, I18n.t("models.promo_code.discount_value_exceeds_100"))
      end
    end

    def applicability_ids_are_valid
      errors.add(:applicable_event_ids,
I18n.t("models.promo_code.event_ids_not_array")) unless applicable_event_ids.is_a?(Array)
      errors.add(:applicable_ticket_type_ids,
I18n.t("models.promo_code.ticket_type_ids_not_array")) unless applicable_ticket_type_ids.is_a?(Array)
    end
end
