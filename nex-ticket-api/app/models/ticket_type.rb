class TicketType < ApplicationRecord
  # Associations
  belongs_to :event, inverse_of: :ticket_types
  has_many :basket_items, as: :itemable
  has_many :order_items, as: :order_itemable

  # Validations
  validates :event, presence: true
  validates :description, presence: true
  validates :max_amount, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  #  validates :available_amount, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validate :validate_discounts_format
  validates :name, presence: true, uniqueness: { scope: :event_id }, length: { maximum: 255 }
  validates :paid_for_amount, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :availability_status, presence: true

  before_create :set_price_at_creation!

  def discounted_price
    return price if discounts.blank?

    active_discount = discounts.find do |discount|
      start_date = Time.zone.parse(discount["start_date"])
      end_date = Time.zone.parse(discount["end_date"])
      start_date <= Time.zone.now && end_date >= Time.zone.now
    end

    return price unless active_discount

    price - (price * (active_discount["percentage"].to_f / 100))
  end

  def paid_for_amount
    order_items.joins(:order).where(orders: { status: "success" }).sum(:quantity)
  end

  def availability_status
    paid_for_amount >= max_amount ? "sold_out" : "available"
  end

   private

     def set_price_at_creation!
       self.current_effective_price = discounted_price
     end

     # Custom validation for discounts structure
     def validate_discounts_format
       return if discounts.blank?

       unless discounts.is_a?(Array)
         errors.add(:discounts, "must be an array of JSON objects")
         return
       end

       discounts.each do |discount|
         unless discount.is_a?(Hash) && discount.key?("percentage") && discount.key?("start_date") && discount.key?("end_date")
           errors.add(:discounts, "each discount must have 'percentage', 'start_date', and 'end_date' keys")
           next
         end

         # Validate percentage
         if discount["percentage"].to_f <= 0 || discount["percentage"].to_f > 100
           errors.add(:discounts, "'percentage' must be greater than 0 and less than or equal to 100")
         end

         # Validate start_date and end_date
         if !valid_datetime?(discount["start_date"]) || !valid_datetime?(discount["end_date"])
           errors.add(:discounts, "'start_date' and 'end_date' must be valid ISO8601 datetime strings")
         elsif DateTime.parse(discount["start_date"]) >= DateTime.parse(discount["end_date"])
           errors.add(:discounts, "'start_date' must be before 'end_date'")
         end
       end
     end

     # Helper method to validate datetime
     def valid_datetime?(datetime_string)
       DateTime.iso8601(datetime_string)
       true
     rescue ArgumentError
       false
     end

     def paid_for_amount
       order_items.joins(:order).where(orders: { status: "success" }).sum(:quantity)
     end

     def availability_status
       paid_for_amount >= max_amount ? "sold_out" : "available"
     end
end
