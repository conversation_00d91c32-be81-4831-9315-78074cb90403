class BasketItem < ApplicationRecord
  belongs_to :basket
  belongs_to :itemable, polymorphic: true

  validates :quantity, presence: true, numericality: { greater_than: 0 }

  after_create :renew_basket_lifetime, :update_itemable_available_amount_on_save
  before_update :update_itemable_available_amount_on_update
  after_destroy :renew_basket_lifetime, :update_itemable_available_amount_on_destroy

  private

    def renew_basket_lifetime
      basket.renew_lifetime
    end

    def update_itemable_available_amount_on_save
      if itemable.respond_to?(:available_amount)
        new_amount = itemable.available_amount - quantity
        if new_amount < 0
          raise InsufficientAvailableAmountError, _("Insufficient available amount")
        end
        itemable.update(available_amount: new_amount)
      end
    end

    def update_itemable_available_amount_on_destroy
      if itemable.respond_to?(:available_amount)
        itemable.update(available_amount: itemable.available_amount + quantity)
      end
    end

    def update_itemable_available_amount_on_update
      if itemable.respond_to?(:available_amount)
        new_amount = itemable.available_amount + quantity_was - quantity
        if new_amount < 0
          raise InsufficientAvailableAmountError, _("Insufficient available amount")
        end
        itemable.update(available_amount: new_amount)
      end
    end
end
