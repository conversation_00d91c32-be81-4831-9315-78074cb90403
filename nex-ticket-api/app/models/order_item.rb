class OrderItem < ApplicationRecord
  belongs_to :order
  belongs_to :order_itemable, polymorphic: true
  has_many :tickets, dependent: :destroy

  validates :quantity, presence: true, numericality: { greater_than: 0 }
  validates :organiser_price_per_piece, :platform_fee_per_piece, presence: true,
numericality: { greater_than_or_equal_to: 0 }

  def update_itemable_available_amount_on_save
    if order_itemable.respond_to?(:available_amount)
      order_itemable.update(available_amount: order_itemable.available_amount - quantity)
    end
  end

  def update_itemable_available_amount_on_release
    if order_itemable.respond_to?(:available_amount)
      order_itemable.update(available_amount: order_itemable.available_amount + quantity)
    end
  end

  def itemable_name
    case order_itemable_type
    when "TicketType"
      order_itemable.name
    when "OtherType"
      order_itemable.other_name
    else
      "Unknown"
    end
  end

  def calculate_price_per_piece!
    self.price_per_piece = organiser_price_per_piece + platform_fee_per_piece
  end

  def calculate_total_price!
    self.total_price = price_per_piece * quantity
  end
end
