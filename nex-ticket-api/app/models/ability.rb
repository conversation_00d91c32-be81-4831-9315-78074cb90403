# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    if user.is_a?(Customer)
      can :manage, :all if user.super_admin?
      can :read, Article
      can :create, Article
      can :update, Article, author_id: user.id
    elsif user.is_a?(OrganiserAccount)
      can :read, Product
      can :create, Order
      can :manage, Order, user_id: user.id
    elsif user.is_a?(ScannerAccount)
      can :manage, Product, vendor_id: user.id
      can :read, Order
    else
      can :read, :all
    end
  end
end
