class ApplicationRecord < ActiveRecord::Base
  primary_abstract_class

  # DEFAULT_FILTERED_ROWS = [ :updated_at, :created_at ].freeze

  # def as_json(options = {})
  #   # Merge options to allow easy addition or removal of fields
  #   default_options = { except: DEFAULT_FILTERED_ROWS }
  #   super_result = super(default_options.deep_merge(options))
  #   if options[:include].is_a?(Array)
  #     options[:include].each do |include|
  #       recursive_options = {}
  #       if options[:except].is_a?(Hash) && options[:except][include.to_sym] != nil
  #         recursive_options[:except] = options[:except][include.to_sym]
  #       end
  #       super_result[include] = self.send(include).as_json recursive_options
  #     end
  #   elsif options[:include].is_a?(Symbol)
  #     recursive_options = {}
  #     if options[:except].is_a?(Hash) && options[:except][options[:include].to_sym] != nil
  #       recursive_options[:except] = options[:except][options[:include].to_sym]
  #     end
  #     super_result[options[:include]] = self.send(options[:include]).as_json recursive_options
  #   end

  #   super_result
  # end
end
