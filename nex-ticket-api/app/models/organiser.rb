class Organiser < ApplicationRecord
  has_many :organiser_accounts, dependent: :destroy
  has_many :scanner_accounts, dependent: :destroy
  has_many :events
  belongs_to :state
  has_many :promo_codes
  has_one_attached :profile_picture

  before_validation :set_default_currency

  validates :name, presence: true, length: { maximum: 255 }
  validates :contact_email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :contact_mobile, presence: true, length: { maximum: 15 }
  validates :reg_number, presence: true, length: { maximum: 50 }
  validates :vat_number, presence: true, length: { maximum: 50 }
  validates :default_currency, presence: true, length: { maximum: 3 }
  validates :profit_share, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }

  def owns_event?(event)
    events.exists?(id: event.id)
  end

  def owns_ticket_type?(ticket_type)
    events.exists?(id: ticket_type.event_id)
  end

  def set_default_currency
    self.default_currency ||= state.currency_code
  end
end
