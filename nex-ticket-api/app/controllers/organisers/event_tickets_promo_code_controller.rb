module Organisers
  class EventTicketsPromoCodeController < ApplicationController
    before_action :authenticate_organiser!

    def events
      events = current_user.organiser.events.select(:id, :name)
      render json: events
    end

    def tickets
      ticket_types = TicketType.joins(:event)
                               .where(events: { organiser_id: current_user.organiser.id })
                               .select("ticket_types.id, ticket_types.name")
      render json: ticket_types
    end
  end
end
