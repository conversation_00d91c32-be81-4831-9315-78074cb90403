module Organisers
  class OrganisersController < ApplicationController
    before_action :authenticate_organiser!
    before_action :set_organiser, only: [ :show, :update ]

    # GET /organiser
    def show
      render json: OrganiserSerializer.new(@organiser, { params: serializer_params }).serializable_hash, status: :ok
    end

    # PATCH/PUT /organiser
    def update
      if @organiser.update(organiser_params)
        attach_files(@organiser)
        render json: OrganiserSerializer.new(@organiser, { params: serializer_params }).serializable_hash, status: :ok
      else
        render json: @organiser.errors, status: :unprocessable_entity
      end
    end

    private

      def set_organiser
        @organiser = current_user.organiser
        render json: { error: I18n.t("controllers.errors.unauthorized") }, status: :unauthorized unless @organiser
      end

      def organiser_params
        params.require(:organiser).permit(*ORGANISER_PARAMS, :id)
      end

      def attach_files(organiser)
        if params[:profile_picture].present?
          profile_blob = ActiveStorage::Blob.find_by(key: params[:profile_picture][:key])
          if profile_blob&.image? && profile_blob.metadata["uploaded_by"]["user_id"] == current_user.id
            organiser.profile_picture.attach(profile_blob)
          else
            organiser.errors.add(:profile_picture, I18n.t("model.errors.profile_picture_invalid"))
          end
        end
      end
  end
end
