module Organisers
  class TagsController < ApplicationController
    before_action :set_tag, only: %i[ show ]
    before_action :authenticate_organiser!

    # GET /tags
    def index
      search_term = params[:q]

      if search_term.present?
        escaped_term = Tag.sanitize_sql_like(search_term)

        starts_with_pattern = "#{escaped_term}%"
        contains_pattern = "%#{escaped_term}%"

        @tags = Tag
          .where("name LIKE ?", contains_pattern)
          .order(Arel.sql("CASE WHEN name LIKE '#{starts_with_pattern}' THEN 0 ELSE 1 END, name ASC"))
      else
        @tags = Tag.order(name: :asc)
      end

      render json: @tags
    end

    # GET /tags/1
    def show
      render json: @tag
    end

    private
      def set_tag
        @tag = Tag.find(params[:id])
      end

      def tag_params
        params.require(:tag).permit(:name)
      end
  end
end
