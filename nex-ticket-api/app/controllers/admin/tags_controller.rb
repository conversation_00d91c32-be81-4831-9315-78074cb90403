module Admin
  class TagsController < ApplicationController
    before_action :set_tag, only: %i[ show update destroy ]
    before_action :authenticate_admin!

    # GET /tags
    def index
      if params[:search].present?
        @tags = Tag.where("name LIKE ?", "%#{params[:q]}%")
      else
        @tags = Tag.all
      end
      serializer = TagSerializer.new(@tags)
      render json: serializer.serializable_hash
    end

    # GET /tags/1
    def show
      serializer = TagSerializer.new(@tag)
      render json: serializer.serializable_hash
    end

    # POST /tags
    def create
      @tag = Tag.new(tag_params)

      if @tag.save
        serializer = TagSerializer.new(@tag)
        render json: serializer.serializable_hash, status: :created
      else
        render json: @tag.errors, status: :unprocessable_entity
      end
    end

    # PATCH/PUT /tags/1
    def update
      if @tag.update(tag_params)
        serializer = TagSerializer.new(@tag)
        render json: serializer.serializable_hash
      else
        render json: @tag.errors, status: :unprocessable_entity
      end
    end

    # DELETE /tags/1
    def destroy
      @tag.destroy!
    end

    private
      # Use callbacks to share common setup or constraints between actions.
      def set_tag
        @tag = Tag.find(params[:id])
      end

      # Only allow a list of trusted parameters through.
      def tag_params
        params.require(:tag).permit(:name)
      end
  end
end
