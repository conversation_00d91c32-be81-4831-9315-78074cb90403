module Admin
  class TagCategoriesController < ApplicationController
    before_action :set_tag_category, only: %i[ show update destroy ]
    before_action :authenticate_admin!

    SERIALIZER_INCLUDES = [ :tags ]

    # GET /tag_categories
    def index
      if params[:search].present?
        @tag_categories = TagCategory.where("name LIKE ?", "%#{params[:q]}%")
      else
        @tag_categories = TagCategory.all
      end
      serializer = TagCategoriesSerializer.new(@tag_categories, include: SERIALIZER_INCLUDES)
      render json: serializer.serializable_hash
    end

    # GET /tag_categories/1
    def show
      serializer = TagCategoriesSerializer.new(@tag_category, include: SERIALIZER_INCLUDES)
      render json: serializer.serializable_hash
    end

    # POST /tag_categories
    def create
      @tag_category = TagCategory.new(tag_category_params)

      if @tag_category.save
        serializer = TagCategoriesSerializer.new(@tag_categories, include: SERIALIZER_INCLUDES)
        render json: serializer.serializable_hash, status: :created
      else
        render json: @tag_category.errors, status: :unprocessable_entity
      end
    end

    # PATCH/PUT /tag_categories/1
    def update
      if @tag_category.update(tag_category_params)
        serializer = TagCategoriesSerializer.new(@tag_category, include: SERIALIZER_INCLUDES)
        render json: serializer.serializable_hash
      else
        render json: @tag_category.errors, status: :unprocessable_entity
      end
    end

    # DELETE /tag_categories/1
    def destroy
      @tag_category.destroy!
    end

    private
      # Use callbacks to share common setup or constraints between actions.
      def set_tag_category
        @tag_category = TagCategory.find(params[:id])
      end

      # Only allow a list of trusted parameters through.
      def tag_category_params
        params.require(:tag_category).permit(:name)
      end
  end
end
