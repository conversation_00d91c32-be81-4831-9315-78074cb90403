module Public
  class GroupedEventsController < ApplicationController
    include Customerable

    ALLOWED_INCLUDES = %w[ticket_types organiser tags].freeze

    before_action -> { filter_and_set_includes params[:includes] }

    EVENT_GROUPS = [
  { group_key: "events", name: "Events", method: :basic_events },
  { group_key: "recommended", name: "Recommended", method: :recommended_events },
  { group_key: "tomorrow", name: "Tomorrow", method: :events_tomorrow },
  { group_key: "next_week", name: "Next week", method: :events_next_week },
  { group_key: "saved_events", name: "Saved events", method: :saved_events }
].freeze

    def index
      groups = determine_groups_for_user

      response = { groups: [] }
      groups.each do |group|
        events = send(group[:method]).order(start_time: params[:start_time_sort] || :asc)
        events = filter_active_events(events)
        response[:groups] << {
          G_KEY: group[:group_key],
          Name: group[:name].humanize,
          Events: EventSerializer.new(events,
{ include: @includes, params: { current_user: current_customer } }).serializable_hash
        }
      end

      render json: response
    end

    private

      def basic_events
        events = events_with_basic_filter
        events
      end

      def recommended_events
        events = events_with_basic_filter
        events
      end

      def determine_groups_for_user
        EVENT_GROUPS
      end

      def events_near_you
        # Logic to fetch events based on user's location (longitude and latitude)
        events = events_with_basic_filter
        events.where("longitude BETWEEN ? AND ? AND latitude BETWEEN ? AND ?",
                    user_longitude - 0.1, user_longitude + 0.1,
                    user_latitude - 0.1, user_latitude + 0.1)
      end

      def events_tomorrow
        tomorrow_start = Time.zone.tomorrow.beginning_of_day
        tomorrow_end = Time.zone.tomorrow.end_of_day
        events = events_with_basic_filter
        events.where("start_time <= ? AND end_time >= ?", tomorrow_end, tomorrow_start).limit(7)
      end

      def events_next_week
        next_week_start = Time.zone.now
        next_week_end = 7.days.from_now
        events = events_with_basic_filter
        events.where("start_time <= ? AND end_time >= ?", next_week_end, next_week_start).limit(7)
      end

      def saved_events
        # Logic to fetch events saved by the user
        current_customer.all_saved_events
      end

      def user_longitude
        # Placeholder for user's loncitygitude
        # This should be replaced with actual logic to get user's longitude
        0.0
      end

      def user_latitude
        # Placeholder for user's latitude
        # This should be replaced with actual logic to get user's latitude
        0.0
      end

      def events_with_basic_filter
        Event.by_city(params[:city]) if params[:city].present? || Event.all
      end

      def filter_and_set_includes(includes_param)
        # Split includes by comma and select only allowed associations
        requested_includes = includes_param.to_s.split(",").map(&:strip)
        @includes = requested_includes.select { |association| ALLOWED_INCLUDES.include?(association) }
      end

      def filter_active_events(events)
        events.where(disabled: false)
      end
  end
end
