# app/controllers/public/orders_controller.rb
module Public
  class OrdersController < ApplicationController
    include Customerable
    before_action :set_order, only: [ :apply_promo_code, :remove_promo_code, :update ]

    SERIALIZER_INCLUDES = %w[order_items].freeze

    def create
      customer = find_or_create_customer
      service = OrderCreationService.new(customer, order_params)

      @order = service.call
      if service.success?
        subscription = SubscriptionCreationService.new(customer, subscribe_params, :basic)
        subscription.call
        render_order_created
      else
        render_order_errors(@order)
      end
    end

    # PATCH/PUT /orders/:id
    def update
      if @order.update(order_params)
        customer = find_or_create_customer
        subscription = SubscriptionCreationService.new(customer, subscribe_params, :basic)
        subscription.call
        render_order_updated
      else
        render_order_errors(@order)
      end
    end

    # PATCH /orders/:id/apply_promo_code
    def apply_promo_code
      promo_code_string = params.require(:promo_code).strip

      if promo_code_string.blank?
        return render_error_message("controllers.errors.promo_code_cant_be_blank", :unprocessable_entity)
      end

      begin
        @order.apply_promo_code!(promo_code_string)
        render_order_updated
      rescue Order::PromoCodeError => e
        render_error_message(e.message, :unprocessable_entity)
      rescue => e
        Rails.logger.error "Unexpected error applying promo code: #{e.message} #{e.backtrace.join("\n")}" if defined?(Rails)
        render_error_message("controllers.errors.promo_code_error_unexpected_apply", :internal_server_error)

      end
    end

    # PATCH /orders/:id/remove_promo_code
    def remove_promo_code
      begin
        @order.remove_promo_code!
        render_order_updated
      rescue Order::PromoCodeError => e
        render_error_message(e.message, :unprocessable_entity)
      rescue => e
        Rails.logger.error "Unexpected error removing promo code: #{e.message} #{e.backtrace.join("\n")}" if defined?(Rails)
        render_error_message("controllers.errors.promo_code_error_unexpected_remove", :internal_server_error)
      end
    end

    private

      def order_params
        params.require(:order).permit(
          :first_name,
          :last_name,
          :email,
          :agreed_to_terms_and_cond,
          :event_id
        )
      end

      def subscribe_params
        params.permit(:subscribe, :email)
      end

      def set_order
        @order = Order.find(params[:id])
        if @order.customer != current_customer
          render_error_message("controllers.errors.unauthorized", :unauthorized)
          nil
        end
      rescue ActiveRecord::RecordNotFound
        render_error_message("controllers.errors.no_order_found", :not_found)
      end

      def render_order_created
        render_order(@order, :created)
      end

      def render_order_updated
        render_order(@order, :ok)
      end

      def render_orders_list(orders)
        render_order(orders)
      end

      def render_order_errors(order)
        render json: { errors: order.errors.full_messages }, status: :unprocessable_entity
      end

      def render_error_message(message, status = :unprocessable_entity)
        render json: { errors: I18n.t(message) }, status: status
      end

      def render_order(resource, status = :ok)
        if resource
          render json: OrderSerializer.new(resource, { include: SERIALIZER_INCLUDES, params: serializer_params }).serializable_hash,
  status: status
        else
          render json: { errors: I18n.t("controllers.errors.no_order_found") }, status: :not_found
        end
      end
  end
end
