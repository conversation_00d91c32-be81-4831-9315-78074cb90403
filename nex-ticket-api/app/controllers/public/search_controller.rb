module Public
  class SearchController < ApplicationController
    def search
      query = params[:q]
      results = Event.search_with_relevance(query).limit(5)

      render json: { Events: format_event_results(results) }
    end

    private

      def format_event_results(events)
        events.map do |event|
          {
            id: event.id,
            name: event.name,
            description: event.description,
            start_time: event.start_time,
            end_time: event.end_time,
            venue_name: event.venue_name,
            main_photo: event.main_photo.attached? ? Rails.application.routes.url_helpers.rails_blob_url(event.main_photo,
    only_path: true) : nil,
            relevance: event.relevance.to_i
          }
        end
      end
  end
end
