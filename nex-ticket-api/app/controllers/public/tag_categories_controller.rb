module Public
  class TagCategoriesController < ApplicationController
    before_action :set_tag_category, only: %i[ show ]

    SERIALIZER_INCLUDES = [ :tags ]


    # GET /tag_categories
    def index
      if params[:search].present?
        @tag_categories = TagCategory.where("name LIKE ?", "%#{params[:q]}%")
      else
        @tag_categories = TagCategory.all
      end
      serializer = TagCategoriesSerializer.new(@tag_categories, include: SERIALIZER_INCLUDES)
      render json: serializer.serializable_hash
    end

    # GET /tag_categories/1
    def show
      serializer = TagCategoriesSerializer.new(@tag_category, include: SERIALIZER_INCLUDES)
      render json: serializer.serializable_hash
    end

    # GET /tag_category/tags/1
    def show_tags
      if params[:id].present?
        @tag_category = TagCategory.find(params[:id])
        @tags = @tag_category.tags
        serializer = TagSerializer.new(@tags)
        render json: serializer.serializable_hash
      else
        render json: { error: "Tag Category ID Was Not Provided" }, status: :bad_request
      end
    end

    # GET /tag_categories/by_name/:name
    def by_name
      tag_categories_map = {
        "music_gen" => "Music Genre"
        # Add other categories maps here if needed
      }
      @tag_category = TagCategory.find_by(name: tag_categories_map[params[:name]])

      if @tag_category
        serializer = TagCategoriesSerializer.new(@tag_category, include: SERIALIZER_INCLUDES)
        render json: serializer.serializable_hash
      else
        render json: { error: "Tag category not found for name '#{params[:name]}'" }, status: :not_found
      end
    end

    private
      # Use callbacks to share common setup or constraints between actions.
      def set_tag_category
        @tag_category = TagCategory.find(params[:id])
      end

      # Only allow a list of trusted parameters through.
      def tag_category_params
        params.require(:tag_category).permit(:name)
      end
  end
end
