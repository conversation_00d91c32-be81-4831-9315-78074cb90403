module Customerable
  extend ActiveSupport::Concern

  def find_or_create_customer
    if current_user&.is_customer?
      current_user
    else
      visitor_token = request.headers["visitor-token"] || generate_unique_visitor_token
      response.headers["visitor-token"] = visitor_token
      Customer.find_or_create_by(visitor_token: visitor_token)
    end
  end

  def current_customer
    @current_customer ||= find_or_create_customer
  end

  def find_customer
    if current_user&.is_customer?
      current_user
    else
      visitor_token = request.headers["visitor-token"]
      Customer.find_by(visitor_token: visitor_token) if visitor_token
    end
  end

  private

    def generate_unique_visitor_token
      loop do
        token = SecureRandom.uuid
        break token unless Customer.exists?(visitor_token: token)
      end
    end
end
