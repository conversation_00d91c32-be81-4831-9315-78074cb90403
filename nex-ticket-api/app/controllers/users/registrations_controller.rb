class Users::RegistrationsController < Devise::RegistrationsController
  respond_to :json
  before_action :configure_sign_up_params, only: [ :create ]

  # POST /resource
  # def create
  # end

  protected

    # If you have extra params to permit, append them to the sanitizer.
    def configure_sign_up_params
      devise_parameter_sanitizer.permit(:sign_up, keys: USER_PARAMS)
    end

  private

    def respond_with(resource, _opts = {})
      if resource.persisted? && !resource.errors.any?
        UserMailer.with(user: @user).send_welcome_mail.deliver_now
        render json: {
          status: { code: 200, message: I18n.t("auth.signup_success") },
          data: { user: UserSerializer.new(resource).serializable_hash[:data][:attributes] }
        }, status: :ok
      else
        render json: {
          status: { code: 422, message: I18n.t("auth.user_create_error", errors: resource.errors.full_messages.to_sentence) }
        }, status: :unprocessable_entity
      end
    end
end
