class GeoapifyReverseGeocoding
  def initialize(lat, lon)
    @lat = lat
    @lon = lon
  end

  def fetch_address
    rest_client = Faraday.new(
      url: "https://api.geoapify.com/v1/geocode/reverse",
      params: { lat: @lat, lon: @lon, lang: "en", apiKey: ENV["GEOAPIFY_API_KEY"] }
    )

    response = rest_client.get
    if response.success?
      JSON.parse(response.body)
    else
      Rails.logger.error("Failed to fetch address info: #{response.status} #{response.body}")
      {}
    end
  rescue Faraday::Error => e
    Rails.logger.error("Failed to fetch address info: #{e.message}")
    {}
  end
end
