class SubscriptionCreationService
  attr_reader :customer, :subscribe_params, :sub_type

  def initialize(customer, subscribe_params, sub_type)
    @customer = customer
    @subscribe_params = subscribe_params
    @sub_type = sub_type
  end

  def call
    if @subscribe_params.key?(:subscribe)
      subscription = MailSubscription.find_or_initialize_by(
        user_id: @customer.id,
        email: @subscribe_params[:email],
        subscription_type: @sub_type
      )
      subscription.subscribed = subscribe_params[:subscribe]
      subscription.save
    end
  end
end
