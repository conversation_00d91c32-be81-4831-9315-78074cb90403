class UpdateTicketTypeEffectivePricesJob < ApplicationJob
  # rubocop:disable I18n/RailsI18n/DecorateString
  sidekiq_options queue: :default, retry: 1

  def execute
    records_to_update = []
    processed_count = 0
    calculation_error_count = 0
    import_error_count = 0
    updated_count = 0

    logger.info "Starting bulk effective price update process..."

    batch_size = 1000

    TicketType.find_in_batches(batch_size: batch_size) do |ticket_types_batch|
      current_batch_size = ticket_types_batch.size
      logger.info "Processing batch of #{current_batch_size} ticket types..."

      ticket_types_batch.each do |ticket_type|
        processed_count += 1
        begin
          effective_price = ticket_type.discounted_price

          if ticket_type.current_effective_price != effective_price
            ticket_type.current_effective_price = effective_price
            records_to_update << ticket_type
          end
        rescue StandardError => e
          logger.error "Error calculating price for TicketType ##{ticket_type.id}: #{e.message}"
          calculation_error_count += 1
        end
      end
      logger.info "Batch processed. Found #{records_to_update.size} total updates needed so far. Total processed: #{processed_count}."
    end

    if records_to_update.any?
      logger.info "Attempting bulk update of #{records_to_update.size} ticket types..."
      begin

        result = TicketType.import(
          records_to_update,
          on_duplicate_key_update: {
              conflict_target: :id,
              columns: [ :current_effective_price ]
          },
          validate: false,
          batch_size: batch_size
        )

        updated_count = result.num_inserts if result.respond_to?(:num_inserts)

        if result.respond_to?(:failed_instances) && result.failed_instances.any?
          import_error_count = result.failed_instances.size
          logger.error "Bulk import failed for #{import_error_count} records during the import process."
          result.failed_instances.each_with_index do |failed_instance, index|
            logger.error "Failed Instance ##{index + 1}: ID=#{failed_instance.id rescue 'N/A'}"
            if failed_instance.respond_to?(:errors) && failed_instance.errors.any?
              logger.error "  Validation Errors: #{failed_instance.errors.full_messages.join(', ')}"
            else
              logger.error "  No validation errors found on object. Failure might be DB constraint or trigger."
            end
          end
        end
        logger.info "Bulk update command finished. Affected rows (approx): #{updated_count}. Import process errors: #{import_error_count}."

      rescue StandardError => e
        logger.error "Bulk import failed entirely: #{e.message}"
        logger.error e.backtrace.join("\n")
        import_error_count = records_to_update.size
        updated_count = 0
      end
    else
      logger.info "No ticket types required effective price updates after processing."
    end

    logger.info "#{self.class.name} Summary: " \
                "Processed: #{processed_count}, " \
                "Calculation Errors: #{calculation_error_count}, " \
                "Updates Queued: #{records_to_update.size}, " \
                "Successfully Updated (approx): #{updated_count}, " \
                "Import Process Errors: #{import_error_count}."
  end
  # rubocop:enable I18n/RailsI18n/DecorateString
end
