class CancelAbandonedOrdersJob < ApplicationJob
  # rubocop:disable I18n/RailsI18n/DecorateString
  ABANDONMENT_THRESHOLD = 2.hours
  BATCH_SIZE = 1000

  def execute
    logger.info "Starting CancelAbandonedOrdersJob ..."

    threshold_time = Time.current - ABANDONMENT_THRESHOLD

    scope = Order.where(status: [ :created, :processing ])
                 .where(updated_at: ...threshold_time)

    total_orders = scope.count

    if total_orders == 0
      logger.info "No abandoned orders found for cancellation."
      logger.info "Finished CancelAbandonedOrdersJob."
      return
    end

    logger.info "Found #{total_orders} abandoned orders to cancel..."

    total_processed = 0
    total_cancelled = 0
    total_errors = 0

    scope.find_in_batches(batch_size: BATCH_SIZE) do |orders_batch|
      logger.info "Processing batch of #{orders_batch.size} orders..."

      order_ids = orders_batch.map(&:id)
      total_processed += order_ids.size

      begin
        # rubocop:disable Rails/SkipsModelValidations
        updated_rows = Order.where(id: order_ids)
                            .update_all(status: Order.statuses[:cancelled], updated_at: Time.current)
        total_cancelled += updated_rows
        logger.info "Successfully cancelled #{updated_rows} orders in this batch."
      rescue => e
        logger.error "Failed to cancel orders: #{order_ids.join(', ')}"
        logger.error e.message
        logger.error e.backtrace.join("\n")
        total_errors += order_ids.size
      end
    end

    logger.info "CancelAbandonedOrdersJob Summary:"
    logger.info "Total Found: #{total_orders}"
    logger.info "Total Processed: #{total_processed}"
    logger.info "Total Cancelled: #{total_cancelled}"
    logger.info "Total Errors: #{total_errors}"
    logger.info "Finished CancelAbandonedOrdersJob."
  end
end
