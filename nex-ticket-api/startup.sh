#!/bin/zsh
echo "Started startup.sh" &&
cd /workspaces/nex-ticket-api &&
su vscode -c "bundle install" &&
su vscode -c "rails db:create" &&
# valiant attempt, but does not work. User needs to start a new terminal because one is created before this command :(
echo "alias railss=\"rails server -b 0.0.0.0\"" >> /home/<USER>/.zshrc &&
su vscode -c "source /home/<USER>/.zshrc" &&
echo "Setup in startup.sh ended, starting rails server in background"

# Run rails server in the background
# su vscode -c "rails server -b 0.0.0.0 &" 
