meta {
  name: Organiser
  type: http
  seq: 1
}

get {
  url: http://localhost:3000/api/organiser/reporting/organiser
  body: none
  auth: bearer
}

auth:bearer {
  token: 
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
}
