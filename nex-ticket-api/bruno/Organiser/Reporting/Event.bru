meta {
  name: Event
  type: http
  seq: 2
}

get {
  url: http://localhost:3000/api/organiser/reporting/event/1
  body: none
  auth: bearer
}

auth:bearer {
  token: 
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
  nex-ticket
  Safe Mode
  http://localhost:3000/api/organiser/events
  Token
  {{token}}
}
