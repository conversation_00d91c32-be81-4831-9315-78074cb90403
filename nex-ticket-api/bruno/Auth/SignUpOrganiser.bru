meta {
  name: SignUpOrganiser
  type: http
  seq: 1
}

post {
  url: http://localhost:3000/api/signup
  body: json
  auth: none
}

headers {
  Accept: application/json
  Content-Type`: application/json
}
>
body:json {
  {
    "user": {
      "email": "<EMAIL>",
      "password": "password",
      "first_name": "<PERSON><PERSON>",
      "last_name": "Zliatina",
      "type": "OrganiserAccount",
      "organiser_attributes": {
        "name": "NiceTimes",
        "contact_email": "<EMAIL>",
        "contact_mobile": "+************",
        "reg_number": "SK30314123",
        "vat_number": "SK30241312312",
        "state_id": "1",
        "default_currency": "EUR"
      }
    }
  }
}

script:post-response {
  if(res.status === 200 ){
    bru.setVar("token", res.headers.authorization?.replace('Bearer ', ''));
  }
  
}
