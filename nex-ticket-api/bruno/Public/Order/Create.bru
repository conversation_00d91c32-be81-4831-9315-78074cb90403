meta {
  name: Create
  type: http
  seq: 1
}

post {
  url: http://localhost:3000/api/public/orders
  body: json
  auth: none
}

headers {
  visitor-token: 5bd8af05-d5ab-40de-8a4c-cbcbe1dff020
}

body:json {
  {
      "order": {
        "external_payment_gate_id": "12345",
        "external_payment_gate_type": "Stripe",
        "price": 100.0,
        "order_items_attributes": [
          {
            "order_itemable_id": 75,
            "order_itemable_type": "TicketType",
            "quantity": 2,
            "price_per_piece": 50.0
          }
        ]
      }
    }
}
