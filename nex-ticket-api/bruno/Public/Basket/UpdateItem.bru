meta {
  name: UpdateItem
  type: http
  seq: 4
}

post {
  url: http://localhost:3000/api/public/basket/update_item
  body: json
  auth: bearer
}

headers {
  visitor-token: {{visitor_token}}
}

auth:bearer {
  token: {{token}}
}

body:json {
  {
    "itemable_type": "ticket_type",
    "itemable_id": 56,
    "quantity": 5
  }
  
}

script:post-response {
  if(res.status >= 200 && res.status < 300 ){
    bru.setVar("visitor_token", res.headers['visitor-token']);
  }
}
