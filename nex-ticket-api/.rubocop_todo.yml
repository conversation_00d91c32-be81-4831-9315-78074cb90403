# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2025-05-28 12:10:52 UTC using RuboCop version 1.68.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 2
I18n/RailsI18n/DecorateString:
  Exclude:
    - 'app/models/event.rb'
    - 'app/services/tickets_pdf_service.rb'

# Offense count: 24
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 205

# Offense count: 4
# Configuration parameters: Include.
# Include: app/models/**/*.rb
Rails/HasAndBelongsToMany:
  Exclude:
    - 'app/models/event.rb'
    - 'app/models/tag.rb'
    - 'app/models/tag_category.rb'

# Offense count: 12
# Configuration parameters: Include.
# Include: app/models/**/*.rb
Rails/HasManyOrHasOneDependent:
  Exclude:
    - 'app/models/city.rb'
    - 'app/models/customer.rb'
    - 'app/models/event.rb'
    - 'app/models/organiser.rb'
    - 'app/models/promo_code.rb'
    - 'app/models/state.rb'
    - 'app/models/ticket_type.rb'

# Offense count: 5
Rails/I18nLocaleTexts:
  Exclude:
    - 'app/mailers/user_mailer.rb'
    - 'app/models/tag.rb'
    - 'app/models/tag_category.rb'

# Offense count: 1
# Configuration parameters: Include.
# Include: app/controllers/**/*.rb, app/mailers/**/*.rb
Rails/LexicallyScopedActionFilter:
  Exclude:
    - 'app/controllers/users/registrations_controller.rb'

# Offense count: 24
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 205
