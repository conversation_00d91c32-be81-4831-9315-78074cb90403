export default {
  organiser: {
    reporting: {
      organiser: {
        event_revenue_breakdown: 'Podział przychodów z wydarzeń',
        events_breakdown: 'Podział wydarzeń',
        event: 'Wydarzenie',
        total_revenue: 'Całkowity przychód',
        organiser_report: 'Raport organizatora',
        show_all_data: 'Pokaż wszystkie dane',
        start_date: 'Data rozpoczęcia',
        end_date: 'Data zakończenia',
        loading_data: 'Ładowanie danych...',
        sales_trend: 'Trend sprzedaży',
        daily_sales: 'Sprzedaż dzienna',
        event_revenue: 'Przychód z wydarzenia',
        trend: 'Trend',
        no_data: 'Brak dostępnych danych',
      },
      event: {
        event_report: 'Raport wydarzenia',
        show_all_data: 'Pokaż wszystkie dane',
        start_date: 'Data rozpoczęcia',
        end_date: 'Data zakończenia',
        loading_event_data: 'Ładowanie danych wydarzenia...',
        sales_trend_by_ticket_type: 'Trend sprzedaży według typu biletu',
        ticket_types_distribution: 'Rozkład typów biletów',
        ticket_type_performance: 'Wydajność typu biletu',
        ticket_type: 'Typ biletu',
        sold: 'Sprzedane',
        revenue: 'Przychód',
        total_revenue: 'Całkowity przychód',
        tickets_sold: 'Sprzedane bilety',
        ticket_types: 'Typy biletów',
        no_data_loaded_info: 'Ups, wygląda na to, że nie znaleziono danych dla Twoich statystyk. Odśwież stronę lub skontaktuj się z administratorem strony.',
      },
    },
    data: {
      save_changes: 'Zapisz zmiany',
      saving_changes: 'Zapisywanie zmian...',
      edit_info: 'Edytuj informacje organizatora',
      contact_mail: 'Email kontaktowy',
      name: 'Nazwa organizatora',
      contact_mobile: 'Telefon kontaktowy',
      reg_number: 'Numer rejestracyjny',
      vat_number: 'Numer VAT',
      country: 'Kraj',
      default_currency: 'Domyślna waluta',
      error_creating_event: 'Błąd podczas tworzenia wydarzenia',
      profile_image: {
        profile_image: 'Prześlij zdjęcie profilowe',
        profile_image_info: 'Zalecane proporcje: 16:9 (np. 1280×720)',
        crop: 'Przytnij obraz',
        confirm_crop: 'Potwierdź przycięcie',
        cancel: 'Anuluj',
      },
    },
    messages: {
      update_successful: 'Organizator został pomyślnie zaktualizowany!',
      update_failed: 'Aktualizacja organizatora nie powiodła się',
    },
    show_details: 'Pokaż szczegóły',
    delete_event: {
      text: {
        confirm_del: 'Potwierdź usunięcie',
        sec_confirm_del: 'Czy na pewno chcesz usunąć to wydarzenie? Ta akcja nie może zostać cofnięta.',
      },
      buttons: {
        cancel: 'Anuluj',
        delete: 'Usuń',
        delete_event: 'Usuń wydarzenie',
      },
    },
    create_event: {
      next: 'Dalej',
      prev: 'Wstecz',
    },
    edit_event: {
      data_invalid: 'Wprowadzone przez Ciebie zmiany są nieprawidłowe!',
      event_editing: 'Edycja wydarzenia',
      buttons: {
        edit_event: 'Edytuj wydarzenie',
        basic_setting: 'Ustawienia podstawowe',
        location: 'Lokalizacja',
        additional_sett: 'Ustawienia dodatkowe',
        gallery: 'Galeria',
        tickets: 'Bilety',
        save_changes: 'Zapisz zmiany',
      },
      basic_settings_comp: {
        event_creation: 'Tworzenie wydarzenia',
        basic_settings: 'Ustawienia podstawowe',
        cover_image: {
          cover_image: 'Prześlij zdjęcie okładki',
          cover_image_info: 'Zalecane proporcje: 16:9 (np. 1280×720)',
          crop: 'Przytnij obraz',
          confirm_crop: 'Potwierdź przycięcie',
          cancel: 'Anuluj',
        },
        labels: {
          event_name: 'Nazwa wydarzenia',
          cover_image: 'Zdjęcie okładki',
          description: 'Opis wydarzenia',
          start_time: 'Czas rozpoczęcia',
          end_time: 'Czas zakończenia',
        },
        place_holders: {
          event_name: 'Impreza Noworoczna w Warszawie',
          description: 'Impreza Noworoczna w Warszawie to najlepsza impreza na świecie',
        },
        yup_texts: {
          event_name_required: 'Nazwa wydarzenia jest wymagana',
          event_name_min: 'Nazwa wydarzenia musi mieć co najmniej 3 znaki',
          event_name_max: 'Nazwa wydarzenia może mieć maksymalnie 50 znaków',
          cover_image_required: 'Zdjęcie okładki jest wymagane',
          description_required: 'Opis jest wymagany',
          description_min: 'Opis musi mieć co najmniej 10 znaków',
          description_max: 'Opis może mieć maksymalnie 2500 znaków',
          start_time_required: 'Czas rozpoczęcia jest wymagany',
          start_time_min: 'Czas rozpoczęcia musi być w przyszłości',
          end_time_required: 'Czas zakończenia jest wymagany',
          end_time_min: 'Czas zakończenia musi być po czasie rozpoczęcia',
        },
      },
      addit_sett_comp: {
        titel: 'Ustawienia dodatkowe',
        links: 'Linki',
        policies: 'Zasady',
        add_social: 'Dodaj link do mediów społecznościowych',
        add_policy: 'Dodaj zasadę',
        tags: 'Tagi',
        labels: {
          platform: 'Platforma',
          link: 'Link',
          socials: 'Linki do mediów społecznościowych',
          policy_type: 'Typ zasady',
          policy_details: 'Szczegóły zasady',
          policies: 'Zasady',
          tag_ids: 'ID tagów',
        },
        place_holders: {
          platform: 'Wybierz platformę',
          policy_type: 'Typ np. Wiek +18',
          policy_details: 'Szczegóły np. Wstęp dla nieletnich niedozwolony',
        },
        yup_texts: {
          platform_required: 'Platforma jest wymagana',
          invalid_platform: 'Nieprawidłowa platforma',
          invalid_url_format: 'Nieprawidłowy format URL',
          link_required: 'Link jest wymagany',
          socials_required: 'Link do mediów społecznościowych jest wymagany',
          policy_type_required: 'Typ zasady jest wymagany',
          policy_type_min: 'Typ zasady musi mieć co najmniej 3 znaki',
          policy_type_max: 'Typ zasady może mieć maksymalnie 50 znaków',
          policy_details_required: 'Szczegóły zasady są wymagane',
          policy_details_min: 'Szczegóły zasady muszą mieć co najmniej 5 znaków',
          policy_details_max: 'Szczegóły zasady mogą mieć maksymalnie 500 znaków',
          tag_id_required: 'ID tagu jest wymagane',
          tag_id_positive: 'ID tagu musi być liczbą dodatnią',
        },
      },
      gallery_comp: {
        labels: {
          images_gallery: 'Galeria zdjęć',
          loaded_image: 'Obraz',
          upload_failed: 'Przesyłanie nie powiodło się',
          raw_file: 'Plik surowy',
          file: 'Plik',
          key: 'Klucz',
          event_data: 'Dane wydarzenia',
          photo_gallery: 'Galeria zdjęć',
        },
        yup_texts: {
          invalid_url_format: 'Nieprawidłowy format URL',
          valid_image_url: 'Galeria zdjęć musi zawierać prawidłowe URL-e obrazów',
        },
      },
      tickets_comp: {
        tickets_attrb: 'Atrybuty biletów',
        discounts: 'Zniżki',
        add_discount: 'Dodaj zniżkę',
        description: 'Opis biletu',
        add_feature: 'Dodaj funkcję',
        add_ticket_type: 'Dodaj typ biletu',
        labels: {
          ticket_name: 'Nazwa typu biletu',
          max_amount: 'Maksymalna liczba biletów',
          available_amount: 'Dostępna liczba',
          price: 'Cena',
          ticket_category: 'Kategoria biletu',
          percentage: 'Procent',
          start_date: 'Data rozpoczęcia',
          end_date: 'Data zakończenia',
          feature_name: 'Nazwa funkcji',
          description: 'Opis',
        },
        place_holders: {
          ticket_type_name: 'VIP, Zwykły, itp.',
          description: 'Energetyczny koncert z muzyką pop i...',
        },
        yup_texts: {
          name_req: 'Nazwa biletu jest wymagana',
          max_amount_req: 'Maksymalna liczba jest wymagana',
          max_amount_min: 'Maksymalna liczba musi wynosić co najmniej 1',
          available_amnt_req: 'Dostępna liczba jest wymagana',
          available_amnt_min: 'Dostępna liczba musi wynosić co najmniej 1',
          price_req: 'Cena jest wymagana',
          price_min: 'Cena musi wynosić co najmniej 0',
          percentage_req: 'Procent jest wymagany',
          percentage_min: 'Procent musi wynosić co najmniej 1%',
          percentage_max: 'Procent może wynosić maksymalnie 100%',
          start_date_req: 'Data rozpoczęcia jest wymagana',
          end_date_req: 'Data zakończenia jest wymagana',
          end_date_min: 'Data zakończenia musi być po dacie rozpoczęcia',
          description_req: 'Opis jest wymagany',
          description_min: 'Opis musi mieć co najmniej 10 znaków',
          description_max: 'Opis może mieć maksymalnie 2500 znaków',
        },
      },
      location_comp: {
        titel: 'Lokalizacja',
        labels: {
          venue_name: 'Nazwa miejsca',
          latitude: 'Szerokość geograficzna',
          longitude: 'Długość geograficzna',
        },
        place_holders: {
          venue_name: 'Klub Ministry',
        },
        yup_texts: {
          venue_name_required: 'Nazwa miejsca jest wymagana',
          venue_name_min: 'Nazwa miejsca musi mieć co najmniej 3 znaki',
          venue_name_max: 'Nazwa miejsca może mieć maksymalnie 50 znaków',
          latitude_required: 'Szerokość geograficzna jest wymagana',
          latitude_range: 'Szerokość geograficzna musi być między -90 a 90',
          longitude_required: 'Długość geograficzna jest wymagana',
          longitude_range: 'Długość geograficzna musi być między -180 a 180',
        },
      },
    },
  },
}