import type { I18nTranslation } from '@/utils/constants'

export default {
  auth: {
    fields: {
      // Step 1
      email: '<PERSON><PERSON>',
      first_name: 'First name',
      last_name: 'Last name',
      password: 'Password',
      password_confirmation: 'Password confirmation',

      // Step 2
      organiser_name: 'Organisation name',
      contact_email: 'Contact email',
      contact_mobile: 'Contact mobile',
      reg_number: 'Company registration number',
      vat_number: 'VAT number',
      country: 'Country',

      mobile: 'Mobile number',

      name: 'Name',
      subject: 'Subject',
      contact_message: 'Contact message',
      contact_message_placeholder: 'Enter your message',

      label_with_optional: ({ linked, named }: I18nTranslation) => `${linked(`auth.fields.${named('fieldName')}`)} (Optional)`,
    },
    buttons: {
      login: 'Login',
      submit: 'Submit',
      forgot_password: 'Forgot password?',
      register: 'Register',
      not_registered: 'Not registered?',
      has_account: 'Already have an account?',
      next: 'Next',
      prev: 'Previous',
      show_password: 'Show password',
    },
    organiser_login: {
      title: 'Organiser login',
    },
    login: {
      welcome: '🙌  Welcome back!',
      title: 'Login',
      subtitle: 'Ready to create unforgettable events?',
      dont_have_account: 'Don\'t have an account? ',
      sign_up: 'Sign up.',
    },
    registration: {
      welcome: '🙌  Welcome!',
      title: 'Register',
      subtitle: 'To start publishing events',
      have_account: 'Already have an account? ',
      sign_in: 'Sign in.',
      step_1: 'Basic information',
      step_2: 'Organisation information',
    },
    organiser_registration: {
      title: 'Organiser registration',
      step_1: 'Basic information',
      step_2: 'Organisation information',
      password_mismatch: 'Passwords do not match',
    },
    payment: {
      continue: 'Continue to Payment',
      processing: 'Processing...',
      pay_now: 'Pay Now',
      canceled: 'Payment Canceled',
      success: 'Payment Successful!',
      success_confirm_1: 'Your tickets for',
      success_confirm_2: 'have been booked successfully.',
      canceled_confirm: 'Your payment was canceled. You can safely return to our website.',
      buttons: {
        home: 'Back to Home',
      },
    },
    forgotten_pswd: {
      change: 'Change password',
      your_email: 'Your email',
      send: 'Send',
      email: 'E-mail',
      sent_email: 'Email will be sent to your e-mail address, there you can change your password.',
    },
  },
}
