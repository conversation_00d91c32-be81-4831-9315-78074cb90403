export default {
  promo_codes: {
    attributes: {
      code: 'Code',
      description: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      discount_type: '<PERSON><PERSON><PERSON><PERSON>',
      discount_value: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      discount: '<PERSON><PERSON><PERSON>',
      valid_from: '<PERSON><PERSON><PERSON><PERSON> ab',
      valid_until: '<PERSON><PERSON>lt<PERSON> bis',
      validity: 'Gültigkeitsdaten',
      max_uses: 'Max<PERSON> Verwendungen (Gesamt)',
      uses_count: 'Verwendungsanzahl',
      usage: 'Verwendung',
      max_uses_per_user: 'Max. Verwendungen pro Kunde',
      min_order_amount: 'Mindestbestellwert',
      active: 'Aktiv',
      status: 'Status',
      applicable_event_ids: 'Anwendbare Veranstaltungs-IDs',
      applicable_ticket_type_ids: 'Anwendbare Ticket-Typ-IDs',
    },
    discount_types: {
      percentage_total: 'Prozentrabatt auf Gesamtbestellung',
      fixed_amount_total: 'Fester Betrag von Gesamtbestellung',
    },
    statuses: {
      active: 'Aktiv',
      inactive: 'Inaktiv',
      expired: 'Abgel<PERSON>fen',
      fully_used: 'Vollständig verwendet',
      unknown: 'Unbekannt',
    },
    hints: {
      percentage_value: 'Wert als Prozentsatz eingeben (z.B. 10 für 10%)',
      fixed_value: 'Festen Betrag eingeben (z.B. 5,00)',
      unlimited: 'Leer lassen für unbegrenzt',
      comma_separated: 'Mehrere IDs mit Komma trennen.',
      max_uses_context: 'Aktuelle Verwendung: {count}. Kann nicht niedriger gesetzt werden.',
      description_placeholder: 'Optional: Interne Notiz zu diesem Code hinzufügen',
      comma_ids_placeholder: 'Kommagetrennte IDs',
      blank_for_all: 'Leer lassen, um auf alle Veranstaltungen anzuwenden.',
      blank_for_all_types: 'Leer lassen, um auf alle Ticket-Typen anzuwenden.',
      id_input_suggestion_title: 'Vorschlag:',
      id_input_suggestion_text: 'Erwägen Sie, diese Texteingabe durch ein durchsuchbares Multi-Select-Dropdown für bessere Benutzerfreundlichkeit zu ersetzen.',
      min_amount_placeholder: 'Leer lassen für kein Minimum',
    },
    index: {
      title: 'Ihre Promo-Codes',
      create_new: 'Neuen Promo-Code erstellen',
      no_codes: 'Sie haben noch keine Promo-Codes erstellt.',
      create_first: 'Erstellen Sie Ihren ersten Promo-Code',
    },
    new: {
      title: 'Neuen Promo-Code erstellen',
      submit_button: 'Promo-Code erstellen',
    },
    show: {
      title: 'Promo-Code-Details',
      loading: 'Promo-Code-Details werden geladen...',
      editing_mode: 'Bearbeitungsmodus',
      data_missing_fallback: 'Promo-Code-Daten konnten nicht angezeigt werden.',
      return_to_list: 'Zurück zur Liste',
    },
    create: {
      success: 'Promo-Code erfolgreich erstellt!',
    },
    update: {
      success: 'Promo-Code erfolgreich aktualisiert!',
      no_changes: 'Keine Änderungen erkannt.',
    },
    errors: {
      load_list_failed: 'Promo-Codes konnten nicht geladen werden.',
      load_detail_failed: 'Promo-Code-Details konnten nicht geladen werden.',
      create_failed: 'Promo-Code konnte nicht erstellt werden.',
      update_failed: 'Promo-Code konnte nicht aktualisiert werden.',
      validation: 'Validierung fehlgeschlagen.',
      correct_errors: 'Bitte korrigieren Sie die unten stehenden Fehler.',
      not_found: 'Promo-Code nicht gefunden.',
      unauthorized: 'Sie haben keine Berechtigung, diesen Promo-Code anzuzeigen.',
      unauthorized_update: 'Berechtigung verweigert, diesen Promo-Code zu aktualisieren.',
      delete_used: 'Ein verwendeter Promo-Code kann nicht gelöscht werden ({count}). Erwägen Sie stattdessen eine Deaktivierung.',
      delete_failed: 'Promo-Code konnte nicht gelöscht werden.',
      unexpected: 'Ein unerwarteter Fehler ist aufgetreten.',
      id_missing: 'Promo-Code-ID fehlt.',
      blank_code: 'Promo-Code darf nicht leer sein.',
      no_access_or_not_found: 'Promo-Code nicht gefunden oder Sie haben keinen Zugriff.',
      process_failed: 'Promo-Code-Details aus der Server-Antwort konnten nicht verarbeitet werden.',
    },
  },
}