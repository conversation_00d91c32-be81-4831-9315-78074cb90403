export default {
  promo_codes: {
    attributes: {
      code: 'Kod',
      description: 'Opis',
      discount_type: 'Typ zniżki',
      discount_value: 'Wartość zniżki',
      discount: '<PERSON>ni<PERSON><PERSON>',
      valid_from: 'Ważny od',
      valid_until: 'Ważny do',
      validity: '<PERSON><PERSON>',
      max_uses: '<PERSON><PERSON>. u<PERSON> (Ogółem)',
      uses_count: '<PERSON>cz<PERSON> uż<PERSON>ć',
      usage: 'U<PERSON><PERSON><PERSON>',
      max_uses_per_user: 'Maks. użyć na klienta',
      min_order_amount: 'Minimalna kwota zamówienia',
      active: 'Aktywny',
      status: 'Status',
      applicable_event_ids: 'Odpowiednie ID wydarzeń',
      applicable_ticket_type_ids: 'Odpowiednie ID typów biletów',
    },
    discount_types: {
      percentage_total: 'Zniżka procentowa od całego zamówienia',
      fixed_amount_total: 'Stała kwota od całego zamówienia',
    },
    statuses: {
      active: 'Aktywny',
      inactive: 'Nieaktywny',
      expired: 'Wygasły',
      fully_used: '<PERSON><PERSON>ł<PERSON>wicie wykorzystany',
      unknown: 'Nieznany',
    },
    hints: {
      percentage_value: 'Wprowadź wartość jako procent (np. 10 dla 10%)',
      fixed_value: 'Wprowadź stałą kwotę (np. 5,00)',
      unlimited: 'Pozostaw puste dla nieograniczonego',
      comma_separated: 'Oddziel wiele ID przecinkami.',
      max_uses_context: 'Aktualne użycie: {count}. Nie można ustawić niżej.',
      description_placeholder: 'Opcjonalnie: Dodaj wewnętrzną notatkę o tym kodzie',
      comma_ids_placeholder: 'ID oddzielone przecinkami',
      blank_for_all: 'Pozostaw puste, aby zastosować do wszystkich wydarzeń.',
      blank_for_all_types: 'Pozostaw puste, aby zastosować do wszystkich typów biletów.',
      id_input_suggestion_title: 'Sugestia:',
      id_input_suggestion_text: 'Rozważ zastąpienie tego pola tekstowego przeszukiwalną listą wielokrotnego wyboru dla lepszej użyteczności.',
      min_amount_placeholder: 'Pozostaw puste dla braku minimum',
    },
    index: {
      title: 'Twoje kody promocyjne',
      create_new: 'Utwórz nowy kod promocyjny',
      no_codes: 'Nie utworzyłeś jeszcze żadnych kodów promocyjnych.',
      create_first: 'Utwórz swój pierwszy kod promocyjny',
    },
    new: {
      title: 'Utwórz nowy kod promocyjny',
      submit_button: 'Utwórz kod promocyjny',
    },
    show: {
      title: 'Szczegóły kodu promocyjnego',
      loading: 'Ładowanie szczegółów kodu promocyjnego...',
      editing_mode: 'Tryb edycji',
      data_missing_fallback: 'Dane kodu promocyjnego nie mogły zostać wyświetlone.',
      return_to_list: 'Powrót do listy',
    },
    create: {
      success: 'Kod promocyjny został pomyślnie utworzony!',
    },
    update: {
      success: 'Kod promocyjny został pomyślnie zaktualizowany!',
      no_changes: 'Nie wykryto zmian.',
    },
    errors: {
      load_list_failed: 'Nie udało się załadować kodów promocyjnych.',
      load_detail_failed: 'Nie udało się załadować szczegółów kodu promocyjnego.',
      create_failed: 'Nie udało się utworzyć kodu promocyjnego.',
      update_failed: 'Nie udało się zaktualizować kodu promocyjnego.',
      validation: 'Walidacja nie powiodła się.',
      correct_errors: 'Proszę poprawić błędy poniżej.',
      not_found: 'Kod promocyjny nie został znaleziony.',
      unauthorized: 'Nie masz uprawnień do wyświetlenia tego kodu promocyjnego.',
      unauthorized_update: 'Brak uprawnień do aktualizacji tego kodu promocyjnego.',
      delete_used: 'Nie można usunąć kodu promocyjnego, który został użyty ({count}). Rozważ jego dezaktywację.',
      delete_failed: 'Kod promocyjny nie mógł zostać usunięty.',
      unexpected: 'Wystąpił nieoczekiwany błąd.',
      id_missing: 'Brakuje ID kodu promocyjnego.',
      blank_code: 'Kod promocyjny nie może być pusty.',
      no_access_or_not_found: 'Kod promocyjny nie został znaleziony lub nie masz dostępu.',
      process_failed: 'Nie udało się przetworzyć szczegółów kodu promocyjnego z odpowiedzi serwera.',
    },
  },
}