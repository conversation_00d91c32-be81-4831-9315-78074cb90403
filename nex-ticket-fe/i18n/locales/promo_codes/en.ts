export default {
  promo_codes: {
    attributes: {
      code: 'Code',
      description: 'Description',
      discount_type: 'Discount Type',
      discount_value: 'Discount Value',
      discount: 'Discount',
      valid_from: 'Valid From',
      valid_until: 'Valid Until',
      validity: 'Validity Dates',
      max_uses: 'Max Uses (Overall)',
      uses_count: 'Uses Count',
      usage: 'Usage',
      max_uses_per_user: 'Max Uses Per Customer',
      min_order_amount: 'Minimum Order Amount',
      active: 'Active',
      status: 'Status',
      applicable_event_ids: 'Applicable Event IDs',
      applicable_ticket_type_ids: 'Applicable Ticket Type IDs',
    },
    discount_types: {
      percentage_total: 'Percentage off total order',
      fixed_amount_total: 'Fixed amount off total order',
    },
    statuses: {
      active: 'Active',
      inactive: 'Inactive',
      expired: 'Expired',
      fully_used: 'Fully Used',
      unknown: 'Unknown',
    },
    hints: {
      // Existing hints
      percentage_value: 'Enter value as percentage (e.g., 10 for 10%)',
      fixed_value: 'Enter fixed amount (e.g., 5.00)',
      unlimited: 'Leave blank for unlimited',
      comma_separated: 'Separate multiple IDs with a comma.',
      max_uses_context: 'Current usage: {count}. Cannot be set lower.',
      description_placeholder: 'Optional: Add an internal note about this code',
      comma_ids_placeholder: 'Comma-separated IDs',
      blank_for_all: 'Leave blank to apply to all events.',
      blank_for_all_types: 'Leave blank to apply to all ticket types.',
      id_input_suggestion_title: 'Suggestion:',
      id_input_suggestion_text: 'Consider replacing this text input with a searchable multi-select dropdown for better usability.',
      min_amount_placeholder: 'Leave blank for no minimum',
    },
    index: {
      title: 'Your Promo Codes',
      create_new: 'Create New Promo Code',
      no_codes: 'You have not created any promo codes yet.',
      create_first: 'Create Your First Promo Code',
    },
    new: {
      title: 'Create New Promo Code',
      submit_button: 'Create Promo Code',
    },
    show: {
      title: 'Promo Code Details',
      loading: 'Loading Promo Code Details...',
      editing_mode: 'Editing Mode',
      data_missing_fallback: 'Promo code data could not be displayed.',
      return_to_list: 'Return to list',
    },
    create: {
      success: 'Promo code successfully created!',
    },
    update: {
      success: 'Promo code updated successfully!',
      no_changes: 'No changes detected.',
    },
    errors: {
      load_list_failed: 'Could not load promo codes.',
      load_detail_failed: 'Could not load promo code details.',
      create_failed: 'Failed to create promo code.',
      update_failed: 'Failed to update promo code.',
      validation: 'Validation failed.',
      correct_errors: 'Please correct the errors below.',
      not_found: 'Promo code not found.',
      unauthorized: 'You do not have permission to view this promo code.',
      unauthorized_update: 'Permission denied to update this promo code.',
      delete_used: 'Cannot delete a promo code that has been used ({count}). Consider deactivating it instead.',
      delete_failed: 'Promo code could not be destroyed.',
      unexpected: 'An unexpected error occurred.',
      id_missing: 'Promo Code ID is missing.',
      blank_code: 'Promo code cannot be blank.',
      no_access_or_not_found: 'Promo code not found or you don\'t have access.',
      process_failed: 'Could not process promo code details from the server response.',
    },
  },
}
