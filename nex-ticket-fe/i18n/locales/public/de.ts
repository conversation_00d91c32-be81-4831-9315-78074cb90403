export default {
  public: {
    month_shorts: {
      jan: 'Jan',
      feb: 'Feb',
      mar: '<PERSON><PERSON><PERSON>',
      apr: 'Apr',
      may: '<PERSON>',
      jun: 'Jun',
      jul: 'Jul',
      aug: 'Aug',
      sep: 'Sep',
      oct: 'Okt',
      nov: 'Nov',
      dec: 'Dez',
    },
    today: 'Heute',
    tomorrow: '<PERSON><PERSON>',
    loading: 'Wird geladen...',
    no_image: 'Kein Bild',
    event_info: {
      see_all_events: 'Alle Veranstaltungen anzeigen',
      open_map: 'In Karte öffnen',
      date: 'Datum',
      gallery: 'Galerie',
      description: 'Beschreibung',
      links: 'Veranstaltungslinks',
      promoter: 'Veranstalter',
      upcoming: 'Bevorstehende Veranstaltungen:',
      saved_events: 'Ihre gespeicherten Veranstaltungen',
      popular_events: 'Beliebt',
      similar_events: 'Ähnliche Veranstaltungen',
      recommended_events: 'Empfoh<PERSON>',
      promoter_name: 'Name des Veranstalters',
      gallery_photo: '<PERSON><PERSON><PERSON>',
      price: {
        variant_available: 'Variante verfügbar',
        variants_available: 'Varianten verfügbar',
      },
      venue: {
        venue_text: 'Veranstaltungsort',
        policies: 'Richtlinien des Veranstaltungsortes',
        details: 'Details anzeigen',
      },
      buttons: {
        contact_promoter: 'Kontaktieren Sie den Veranstalter',
      },
      contact_form: {
        popup_text: 'Kontaktieren Sie den Veranstalter',
        user_agreement: 'Ich stimme der Verarbeitung meiner persönlichen Daten zu.',
        buttons: {
          send: 'Nachricht senden',
        },
        messages: {
          success: 'Nachricht erfolgreich gesendet!',
          error: 'Fehler beim Senden der Nachricht',
        },
      },
    },
    ticket: {
      tickets: 'Tickets',
      payment_butt: 'Tickets kaufen',
      get_tickets: 'Tickets erhalten',
      sold_out: 'Ausverkauft',
      total: 'Gesamt',
      tickets_as_object: 'Tickets',
      buttons: {
        checkout: 'Zur Kasse',
      },
      stripeCheckout: {
        pay: 'Bezahlen',
        loading_state: 'Zahlungsformular wird geladen...',
      },
      eventData: {
        date: 'Datum',
        venue: 'Veranstaltungsort',
        open_map: 'In Karten öffnen',
        back_to_event: 'Zurück zur Veranstaltung',
      },
      checkout: {
        summary: 'ZUSAMMENFASSUNG',
        total: 'Gesamt',
        vat: 'MwSt',
        subtotal: 'Zwischensumme',
        ticket: 'Ticket',
        checkoutForm: {
          checkout: 'ZUR KASSE',
          shop_detail: 'Käuferdetails',
        },
        checkBox: {
          agreement: 'Ich stimme den Geschäftsbedingungen gemäß der Benutzervereinbarung zu.',
          news: 'Halten Sie mich über die neuesten Nachrichten, Veranstaltungen und exklusive Angebote von TicketPie auf dem Laufenden.',
          optional: 'Optional',
        },
        buttons: {
          back: 'Zurück',
          continue_pay: 'Zahlung',
        },
        stepper: {
          select: 'Auswählen',
          checkout: 'Zur Kasse',
          payment: 'Zahlung',
        },
        payment: {
          payment: 'Zahlung',
          promo_question: 'Haben Sie einen Promo-Code?',
          promo_code: 'Promo-Code ',
          optional: 'Optional',
          apply: 'Anwenden',
          edit: 'Bearbeiten',
          ticket: 'Ticket',
          quantity: 'Menge',
        },
        customer_details: {
          shopper_details: 'Käuferdetails',
          first_name: 'Vorname',
          last_name: 'Nachname',
          email_address: 'E-Mail-Adresse',
          email: 'E-Mail',
          edit: 'Bearbeiten',
        },
        messages: {
          first_name_required: 'Vorname ist erforderlich',
          last_name_required: 'Nachname ist erforderlich',
          email_required: 'E-Mail ist erforderlich',
          invalid_email: 'Ungültige E-Mail-Adresse',
        },
      },
    },
    index: {
      dropdown: {
        title: 'Veranstaltungen suchen in:',
      },
      filters: {
        filter_X: 'Filter',
        filterNames: {
          date: 'Datum',
          sort: 'Sortieren',
          categories: 'Kategorien',
          price: 'Preis',
        },
        price_filter: {
          free: 'Kostenlos',
          any_price: 'Beliebiger Preis',
        },
        filterTexts: {
          sort: 'sortieren',
          categories: 'kategorien',
          date: 'datum',
          price: 'preis',
        },
        holders: {
          min_price: 'Min. €',
          max_price: 'Max. €',
          categories: 'Kategorie wählen',
        },
        warnings: {
          noValues: 'Wählen Sie zuerst die Filterwerte!',
          minBiggerThanMax: 'Min. Wert kann nicht größer als Max. Wert sein!',
        },
        buttonTexts: {
          reset: 'Zurücksetzen',
          apply: 'Anwenden',
        },
      },
      sorter: {
        sort: 'Sortieren',
        sort_by: 'Sortieren nach:',
        priceOption: 'Preis',
        dateOption: 'Datum',
      },
      buttons: {
        seeAll: 'Alle anzeigen',
      },
    },
    autocomplete: {
      place_holder: 'Stadt eingeben',
      matching_cities: 'Passende Städte',
    },
  },
}