export default {
  public: {
    month_shorts: {
      jan: '<PERSON>y',
      feb: '<PERSON><PERSON>',
      mar: '<PERSON>',
      apr: '<PERSON><PERSON>',
      may: 'Maj',
      jun: '<PERSON><PERSON>',
      jul: 'Lip',
      aug: 'Si<PERSON>',
      sep: 'Wrz',
      oct: '<PERSON><PERSON>',
      nov: 'Lis',
      dec: '<PERSON><PERSON>',
    },
    today: '<PERSON><PERSON><PERSON><PERSON>',
    tomorrow: '<PERSON><PERSON>',
    loading: 'Ładowanie...',
    no_image: '<PERSON><PERSON> zdj<PERSON>cia',
    event_info: {
      see_all_events: 'Zobacz wszystkie wydarzenia',
      open_map: 'Otwórz w mapie',
      date: 'Data',
      gallery: 'Galeria',
      description: 'Opis',
      links: 'Linki do wydarzenia',
      promoter: 'Organizator',
      upcoming: 'Nadchodzące wydarzenia:',
      saved_events: 'Twoje zapisane wydarzenia',
      popular_events: 'Popularne',
      similar_events: 'Podobne wydarzenia',
      recommended_events: 'Polecane wydarzenia',
      promoter_name: 'Nazwa organizatora',
      gallery_photo: 'Zdj<PERSON><PERSON> z galerii',
      price: {
        variant_available: 'dostępny wariant',
        variants_available: 'dostępne warianty',
      },
      venue: {
        venue_text: 'Miej<PERSON>ce',
        policies: 'Zasady miejsca',
        details: 'Zobacz szczegóły',
      },
      buttons: {
        contact_promoter: 'Skontaktuj się z organizatorem',
      },
      contact_form: {
        popup_text: 'Skontaktuj się z organizatorem',
        user_agreement: 'Zgadzam się na przetwarzanie moich danych osobowych.',
        buttons: {
          send: 'Wyślij wiadomość',
        },
        messages: {
          success: 'Wiadomość wysłana pomyślnie!',
          error: 'Błąd podczas wysyłania wiadomości',
        },
      },
    },
    ticket: {
      tickets: 'Bilety',
      payment_butt: 'Kup bilety',
      get_tickets: 'Zdobądź bilety',
      sold_out: 'Wyprzedane',
      total: 'Razem',
      tickets_as_object: 'Bilety',
      buttons: {
        checkout: 'Do kasy',
      },
      stripeCheckout: {
        pay: 'Zapłać',
        loading_state: 'Ładowanie formularza płatności...',
      },
      eventData: {
        date: 'Data',
        venue: 'Miejsce',
        open_map: 'Otwórz w mapach',
        back_to_event: 'Powrót do wydarzenia',
      },
      checkout: {
        summary: 'PODSUMOWANIE',
        total: 'Razem',
        vat: 'VAT',
        subtotal: 'Suma częściowa',
        ticket: 'Bilet',
        checkoutForm: {
          checkout: 'DO KASY',
          shop_detail: 'Dane kupującego',
        },
        checkBox: {
          agreement: 'Zgadzam się z warunkami określonymi w umowie użytkownika.',
          news: 'Informuj mnie o najnowszych wiadomościach, wydarzeniach i ekskluzywnych ofertach od TicketPie.',
          optional: 'Opcjonalne',
        },
        buttons: {
          back: 'Wstecz',
          continue_pay: 'Płatność',
        },
        stepper: {
          select: 'Wybierz',
          checkout: 'Do kasy',
          payment: 'Płatność',
        },
        payment: {
          payment: 'Płatność',
          promo_question: 'Masz kod promocyjny?',
          promo_code: 'Kod promocyjny ',
          optional: 'Opcjonalne',
          apply: 'Zastosuj',
          edit: 'Edytuj',
          ticket: 'Bilet',
          quantity: 'Ilość',
        },
        customer_details: {
          shopper_details: 'Dane kupującego',
          first_name: 'Imię',
          last_name: 'Nazwisko',
          email_address: 'Adres e-mail',
          email: 'E-mail',
          edit: 'Edytuj',
        },
        messages: {
          first_name_required: 'Imię jest wymagane',
          last_name_required: 'Nazwisko jest wymagane',
          email_required: 'E-mail jest wymagany',
          invalid_email: 'Nieprawidłowy adres e-mail',
        },
      },
    },
    index: {
      dropdown: {
        title: 'Szukaj wydarzeń w:',
      },
      filters: {
        filter_X: 'Filtr',
        filterNames: {
          date: 'Data',
          sort: 'Sortuj',
          categories: 'Kategorie',
          price: 'Cena',
        },
        price_filter: {
          free: 'Darmowe',
          any_price: 'Dowolna cena',
        },
        filterTexts: {
          sort: 'sortuj',
          categories: 'kategorie',
          date: 'data',
          price: 'cena',
        },
        holders: {
          min_price: 'Min. €',
          max_price: 'Max. €',
          categories: 'Wybierz kategorię',
        },
        warnings: {
          noValues: 'Najpierw wybierz wartości filtra!',
          minBiggerThanMax: 'Wartość Min. nie może być większa niż wartość Max.!',
        },
        buttonTexts: {
          reset: 'Resetuj',
          apply: 'Zastosuj',
        },
      },
      sorter: {
        sort: 'Sortuj',
        sort_by: 'Sortuj według:',
        priceOption: 'Cena',
        dateOption: 'Data',
      },
      buttons: {
        seeAll: 'Zobacz wszystkie',
      },
    },
    autocomplete: {
      place_holder: 'Wpisz miasto',
      matching_cities: 'Pasujące miasta',
    },
  },
}
