export default {
  public: {
    month_shorts: {
      jan: 'Jan',
      feb: 'Feb',
      mar: 'Mar',
      apr: 'Apr',
      may: 'May',
      jun: 'Jun',
      jul: 'Jul',
      aug: 'Aug',
      sep: 'Sep',
      oct: 'Oct',
      nov: 'Nov',
      dec: 'Dec',
    },
    today: 'Today',
    tomorrow: 'Tomorrow',
    loading: 'Loading...',
    no_image: 'No Image',
    event_info: {
      see_all_events: 'See all events',
      open_map: 'Open in Map',
      date: 'Date',
      gallery: 'Gallery',
      description: 'Description',
      links: 'Event Links',
      promoter: 'Promoter',
      upcoming: 'Upcoming Events:',
      saved_events: 'Your saved events',
      popular_events: 'Popular',
      similar_events: 'Similar events',
      recommended_events: 'Recommended events',
      promoter_name: 'Promoter Name',
      gallery_photo: 'Gallery photo',
      price: {
        variant_available: 'variant available',
        variants_available: 'variants available',
      },
      venue: {
        venue_text: 'Venue',
        policies: 'Venue Policies',
        details: 'View Details',
      },
      buttons: {
        contact_promoter: 'Contact the promoter',
      },
      contact_form: {
        popup_text: 'Contact the promoter',
        user_agreement: 'I agree with the processing of my personal data.',
        buttons: {
          send: 'Send Message',
        },
        messages: {
          success: 'Message sent successfully!',
          error: 'Error sending message',
        },
      },
    },
    ticket: {
      tickets: 'Tickets',
      payment_butt: 'Buy Tickets',
      get_tickets: 'Get Tickets',
      sold_out: 'Sold out',
      total: 'Total',
      tickets_as_object: 'Tickets',
      buttons: {
        checkout: 'Checkout',
      },
      stripeCheckout: {
        pay: 'Pay',
        loading_state: 'Loading payment form...',
      },
      eventData: {
        date: 'Date',
        venue: 'Venue',
        open_map: 'Open in maps',
        back_to_event: 'Back to the event',
      },
      checkout: {
        summary: 'SUMMARY',
        total: 'Total',
        vat: 'VAT',
        subtotal: 'Subtotal',
        ticket: 'Ticket',
        checkoutForm: {
          checkout: 'CHECKOUT',
          shop_detail: 'Shopper details',
        },
        checkBox: {
          agreement: 'I agree to the terms and conditions as set out by the user agreement.',
          news: 'Keep me updated on the latest news, events, and exclusive offers from TicketPie.',
          optional: 'Optional',
        },
        buttons: {
          back: 'Back',
          continue_pay: 'Payment',
        },
        stepper: {
          select: 'Select',
          checkout: 'Checkout',
          payment: 'Payment',
        },
        payment: {
          payment: 'Payment',
          promo_question: 'Do you have a promo code?',
          promo_code: 'Promo code ',
          optional: 'Optional',
          apply: 'Apply',
          edit: 'Edit',
          ticket: 'Ticket',
          quantity: 'Quantity',
        },
        customer_details: {
          shopper_details: 'Shopper details',
          first_name: 'First name',
          last_name: 'Last name',
          email_address: 'Email address',
          email: 'Email',
          edit: 'Edit',
        },
        messages: {
          first_name_required: 'First name is required',
          last_name_required: 'Last name is required',
          email_required: 'Email is required',
          invalid_email: 'Invalid email address',
        },
      },
    },
    index: {
      dropdown: {
        title: 'Search events in:',
      },
      filters: {
        filter_X: 'Filter',
        filterNames: {
          date: 'Date',
          sort: 'Sort',
          categories: 'Categories',
          price: 'Price',
        },
        price_filter: {
          free: 'Free',
          any_price: 'Any price',
        },
        filterTexts: {
          sort: 'sort',
          categories: 'categories',
          date: 'date',
          price: 'price',
        },
        holders: {
          min_price: 'Min. €',
          max_price: 'Max. €',
          categories: 'Choose category',
        },
        warnings: {
          noValues: 'First, choose the filter values!',
          minBiggerThanMax: 'Min. value cannot be bigger than Max. value!',
        },
        buttonTexts: {
          reset: 'Reset',
          apply: 'Apply',
        },
      },
      sorter: {
        sort: 'Sort',
        sort_by: 'Sort by:',
        priceOption: 'Price',
        dateOption: 'Date',
      },
      buttons: {
        seeAll: 'See all',
      },
    },
    autocomplete: {
      place_holder: 'Type a city',
      matching_cities: 'Matching cities',
    },
  },
}
