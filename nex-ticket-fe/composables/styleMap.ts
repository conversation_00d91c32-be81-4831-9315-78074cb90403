export const styleMapStatic: Record<string, string> = {
}

export const styleMapDynamic: { pattern: RegExp, key: string }[] = [
  {
    pattern: /^mobile\.header\.\/events\/\d+$/, // mobile.header./events/{}/
    key: 'box_shadow_none; border_bottom_zero',
  },
  {
    pattern: /^mobile\.logo\.\/events\/\d+$/, // mobile.logo./events/{}/
    key: 'fill_white',
  },
  {
    pattern: /^mobile\.headerbtn\.\/events\/\d+$/, // mobile.headerbtn./events/{}/
    key: 'color_white',
  },
]

export const styleMap: Record<string, string> = {
  box_shadow_none: 'box-shadow: none !important',
  border_bottom_zero: 'border-bottom-width: 0 !important',
  fill_white: 'fill: white !important',
  color_white: 'color: white !important',
}
