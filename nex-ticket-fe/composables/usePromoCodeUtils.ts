import type NexPromoCode from '@/models/NexPromoCode'
import { formatCurrency, formatDate } from '@/utils/helpers'

const discountTypeTranslationKeys: Record<string, string> = {
  percentage_total: 'promo_codes.discount_types.percentage_total',
  fixed_amount_total: 'promo_codes.discount_types.fixed_amount_total',
}

type PromoCodeStatusKey = 'active' | 'inactive' | 'expired' | 'fully_used' | 'unknown'
interface PromoCodeStatusInfo {
  key: PromoCodeStatusKey
  textKey: string
  classes: string
}

const statusInfoMap: Record<PromoCodeStatusKey, { textKey: string, classes: string }> = {
  active: { textKey: 'promo_codes.statuses.active', classes: 'bg-green-100 text-green-700' },
  inactive: { textKey: 'promo_codes.statuses.inactive', classes: 'bg-slate-200 text-slate-700' },
  expired: { textKey: 'promo_codes.statuses.expired', classes: 'bg-pie-100 text-pie-700' },
  fully_used: { textKey: 'promo_codes.statuses.fully_used', classes: 'bg-pie-200 text-pie-800' },
  unknown: { textKey: 'promo_codes.statuses.unknown', classes: 'bg-slate-200 text-slate-700' },
}

export function usePromoCodeUtils(promoCode: Ref<NexPromoCode | null>, defaultCurrency: Ref<string>) {
  const { t, locale } = useI18n()

  const status = computed((): PromoCodeStatusInfo => {
    const promo = promoCode.value
    if (!promo) {
      return { key: 'unknown', ...statusInfoMap.unknown }
    }

    const now = new Date()
    const validUntil = promo.valid_until ? new Date(promo.valid_until) : null
    let statusKey: PromoCodeStatusKey = 'unknown'

    if (!promo.active) {
      statusKey = 'inactive'
    }
    else if (validUntil && validUntil < now) {
      statusKey = 'expired'
    }
    else if (promo.max_uses !== null && promo.uses_count >= promo.max_uses) {
      statusKey = 'fully_used'
    }
    else {
      statusKey = 'active'
    }
    return { key: statusKey, ...statusInfoMap[statusKey] }
  })

  const formattedDiscount = computed(() => {
    const promo = promoCode.value
    if (!promo?.discount_type || promo.discount_value === null || Number.isNaN(promo.discount_value))
      return '-'

    const value = promo.discount_value
    switch (promo.discount_type) {
      case 'percentage_total':
        return `${value}%`
      case 'fixed_amount_total':
        return formatCurrency(value, defaultCurrency.value)
      default:
        return `${value}`
    }
  })

  const formattedMinOrderAmount = computed(() => {
    const promo = promoCode.value
    if (promo?.min_order_amount === null || promo?.min_order_amount === undefined || Number.isNaN(promo.min_order_amount)) {
      return t('common.none')
    }
    return formatCurrency(promo.min_order_amount, defaultCurrency.value)
  })

  const formattedUsage = computed(() => {
    const promo = promoCode.value
    if (!promo)
      return '-/-'
    const max = promo.max_uses === null ? '∞' : promo.max_uses
    return `${promo.uses_count} / ${max}`
  })

  const formattedValidFrom = computed(() => {
    const promo = promoCode.value
    if (!promo?.valid_from)
      return t('common.not_set')
    return formatDate(promo.valid_from, locale.value, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
  })

  const formattedValidUntil = computed(() => {
    const promo = promoCode.value
    if (!promo?.valid_until)
      return t('common.not_set')
    return formatDate(promo.valid_until, locale.value, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
  })

  const formattedCreatedAt = computed(() => {
    const promo = promoCode.value
    if (!promo?.created_at)
      return '-'
    return formatDate(promo.created_at, locale.value, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
  })

  const formattedUpdatedAt = computed(() => {
    const promo = promoCode.value
    if (!promo?.updated_at)
      return '-'
    return formatDate(promo.updated_at, locale.value, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
  })

  const discountTypeDisplay = computed(() => {
    const promo = promoCode.value
    if (!promo?.discount_type)
      return '-'
    const translationKey = discountTypeTranslationKeys[promo.discount_type]
    return translationKey ? t(translationKey) : promo.discount_type
  })

  const displayIds = (ids: number[] | null | undefined): string => {
    if (!ids || ids.length === 0)
      return t('common.none')
    return ids.join(', ')
  }

  return {
    status,
    formattedDiscount,
    formattedMinOrderAmount,
    formattedUsage,
    formattedValidFrom,
    formattedValidUntil,
    formattedCreatedAt,
    formattedUpdatedAt,
    discountTypeDisplay,
    displayIds,
  }
}
