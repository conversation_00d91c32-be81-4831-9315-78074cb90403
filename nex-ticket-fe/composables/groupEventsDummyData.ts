export function useGroupedEventsDummyData() {
  const groupedEventsDummyData: deSerGroupedEventData = [
    {
      G_KEY: 'dummy_events',
      Name: 'Dummy Events',
      Events: [
        {
          id: 1,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 2,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 3,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 4,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 5,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
      ],
    },
    {
      G_KEY: 'dummy_events_2',
      Name: 'Dummy Events 2',
      Events: [
        {
          id: 1,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 2,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 3,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 4,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
        {
          id: 5,
          name: 'Dummy Event 1',
          description: 'This is a dummy event description.',
          start_time: new Date(),
          end_time: new Date(),
          organiser_id: 1,
          venue_name: 'Venue 1',
          venue_address: 'Address 1',
          main_photo: { url: '', key: '' },
          latitude: 0,
          longitude: 0,
          city: 'Dummy City',
          currency: 'USD',
          saved: true,
          tags: [],
          social_media_links: [],
          policies: [],
          photo_gallery: [],
          ticket_types: [],
          dateFromTo: 'Dummy Date Range',
          timeFromTo: 'Dummy Time Range',
          ids: 'dummy_event_ids',
          organiser: {
            id: 1,
            ids: 'dummy_ids',
            name: 'Dummy Organiser',
            contact_email: '<EMAIL>',
            contact_mobile: '1234567890',
            reg_number: '123456789',
            vat_number: '987654321',
            state_id: '1',
            default_currency: 'USD',
          },
        },
      ],
    },
  ]

  const groupedEventsDummyData1 = groupedEventsDummyData.filter(event => event.Events.length > 0 && event.G_KEY === 'dummy_events')
  const groupedEventsDummyData2 = groupedEventsDummyData.filter(event => event.Events.length > 0 && event.G_KEY === 'dummy_events_2')
  return {
    groupedEventsDummyData1,
    groupedEventsDummyData2,
  }
}
