import NexCity from '@/models/NexCity'

export function useCityInfo() {
  async function loadInfo(city: number | null | undefined): Promise<NexCity | null> {
    if (city === null || city === undefined) {
      return null
    }

    try {
      const { data: searchData, error: apiError } = await useAPI(`/api/cities/${city}`)
      if (apiError.value) {
        console.error('API Error:', apiError.value)
        return null
      }

      if (searchData.value) {
        const cityInfo = NexCity.create_from_request(searchData.value)
        if (!Array.isArray(cityInfo))
          return cityInfo
        if (cityInfo.length >= 1)
          return cityInfo[0]
        return null
      }

      console.error('No data returned for the city.')
      return null
    }
    catch (err) {
      console.error('Unexpected error:', err)
      return null
    }
  }

  return { loadInfo }
}
