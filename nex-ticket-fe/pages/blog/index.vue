<script lang="ts" setup>
import { Search01Icon } from 'hugeicons-vue'
import { blogPosts as allBlogPosts } from '~/data/blogPosts'

// SEO and meta
useHead({
  title: 'Blog - TicketPie',
  meta: [
    { name: 'description', content: 'Stay updated with the latest news, tips, and insights from TicketPie - your gateway to unforgettable events.' },
  ],
})

// Reactive state
const searchQuery = ref('')
const selectedCategory = ref('all')
const currentPage = ref(1)
const postsPerPage = 9

// Categories for filtering
const categories = [
  { key: 'all', labelKey: 'blog.categories.all' },
  { key: 'events', labelKey: 'blog.categories.events' },
  { key: 'tips', labelKey: 'blog.categories.tips' },
  { key: 'updates', labelKey: 'blog.categories.updates' },
  { key: 'guides', labelKey: 'blog.categories.guides' },
]

// Use the blog posts from the data file
const blogPosts = ref(allBlogPosts.filter(post => post.published))

// Computed properties
const filteredPosts = computed(() => {
  let posts = blogPosts.value

  // Filter by category
  if (selectedCategory.value !== 'all') {
    posts = posts.filter(post => post.category === selectedCategory.value)
  }

  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    posts = posts.filter(post =>
      post.title.toLowerCase().includes(query)
      || post.excerpt.toLowerCase().includes(query)
      || post.tags.some(tag => tag.toLowerCase().includes(query)),
    )
  }

  // Sort by published date (newest first)
  return posts.sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime())
})

const totalPages = computed(() => Math.ceil(filteredPosts.value.length / postsPerPage))

const paginatedPosts = computed(() => {
  const start = (currentPage.value - 1) * postsPerPage
  const end = start + postsPerPage
  return filteredPosts.value.slice(start, end)
})

// Methods
function selectCategory(category: string) {
  selectedCategory.value = category
  currentPage.value = 1 // Reset to first page when filtering
}

function handleSearch() {
  currentPage.value = 1 // Reset to first page when searching
}

// Watch for category/search changes to reset pagination
watch([selectedCategory, searchQuery], () => {
  currentPage.value = 1
})
</script>

<template>
  <div class="min-h-screen bg-pie-25 w-full">
    <!-- Hero Section -->
    <div class="w-full px-4 py-12 md:py-24">
      <div class="flex flex-col justify-center items-center gap-6 border border-slate-700 bg-white lg:rounded-[3rem] md:rounded-[2rem] rounded-3xl shadow-lg lg:py-24 md:py-20 py-12 px-4 md:px-6 lg:px-8 w-full max-w-[95vw] lg:max-w-[90rem] mx-auto">
        <div class="flex flex-col gap-6 justify-center items-center text-center max-w-4xl">
          <h1 class="md:text-8xl text-4xl font-sofia font-[800] text-pie-700 spacing-desktop-5 spacing-mobile-3">
            {{ $t('blog.hero.title') }}
          </h1>
          <p class="md:text-xl-medium text-sm-medium text-slate-700 max-w-2xl">
            {{ $t('blog.hero.description') }}
          </p>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="w-full px-4 pb-8">
      <div class="max-w-[95vw] lg:max-w-[90rem] mx-auto">
        <div class="flex flex-col md:flex-row gap-4 md:gap-6 items-center justify-between">
          <!-- Search Bar -->
          <div class="w-full md:w-auto md:flex-1 max-w-md">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                :placeholder="$t('blog.search.placeholder')"
                class="w-full px-4 py-3 pl-12 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pie-500 focus:border-transparent bg-white text-slate-900 placeholder-slate-500"
                @input="handleSearch"
              >
              <Search01Icon class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
            </div>
          </div>

          <!-- Category Filter -->
          <div class="flex flex-wrap gap-2 md:gap-3">
            <button
              v-for="category in categories"
              :key="category.key"
              class="px-4 py-2 rounded-lg text-sm-medium transition-all duration-200 border"
              :class="selectedCategory === category.key
                ? 'bg-pie-600 text-white border-pie-600 shadow-sm'
                : 'bg-white text-slate-700 border-slate-300 hover:bg-pie-50 hover:border-pie-300'"
              @click="selectCategory(category.key)"
            >
              {{ $t(category.labelKey) }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Blog Posts Grid -->
    <div class="w-full px-4 pb-12 md:pb-24">
      <div class="max-w-[95vw] lg:max-w-[90rem] mx-auto">
        <div v-if="filteredPosts.length === 0" class="text-center py-16">
          <p class="text-xl-medium text-slate-500">
            {{ $t('blog.no_posts_found') }}
          </p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          <BlogCard
            v-for="post in paginatedPosts"
            :key="post.id"
            :post="post"
          />
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-12">
          <div class="flex gap-2">
            <button
              v-for="page in totalPages"
              :key="page"
              class="px-4 py-2 rounded-lg text-sm-medium transition-all duration-200 border"
              :class="currentPage === page
                ? 'bg-pie-600 text-white border-pie-600'
                : 'bg-white text-slate-700 border-slate-300 hover:bg-pie-50 hover:border-pie-300'"
              @click="currentPage = page"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom spacing classes for consistent typography */
.spacing-desktop-5 {
  letter-spacing: -0.05em;
}

.spacing-mobile-3 {
  letter-spacing: -0.03em;
}

@media (max-width: 768px) {
  .spacing-desktop-5 {
    letter-spacing: -0.03em;
  }
}
</style>
