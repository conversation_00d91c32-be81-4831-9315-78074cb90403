<script lang="ts" setup>
import RegImage from '@/assets/images/reg_image.png'

const { t } = useI18n()

const authStore = useAuthStore()

const email = ref<string>('')
const password = ref<string>('')

async function login() {
  await authStore.login(email.value, password.value, '/organiser')
}

const showPassword = ref(false)

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}
</script>

<template>
  <div class="grid grid-cols-1 xl:grid-cols-2 items-center justify-center md:px-16 xl:pt-16 md:pt-12 pt-8 ">
    <img class="hidden xl:block ml-[-4rem]" :src="RegImage" alt="Example party photos">

    <div class="w-full justify-center px-6">
      <div class="flex flex-col gap-12 lg:gap-6 mx-auto md:max-w-[48rem]">
        <div class="flex flex-col gap-6 w-fit">
          <div
            class="rounded-lg border py-3 px-4 bg-pie-100 border-pie-300 text-xl-normal text-pie-950 w-fit"
          >
            {{ $t('auth.login.welcome') }}
          </div>
          <div class="flex flex-col gap-2 md:gap-0">
            <h1 class="text-6xl md:text-8xl font-extrabold text-pie-700 font-sofia uppercase tracking-[-2%]">
              {{ $t('auth.login.title') }}
            </h1>
            <p class="text-base-normal md:text-xl-normal text-slate-500">
              {{ $t('auth.login.subtitle') }}
            </p>
          </div>
        </div>
        <div class="md:rounded-2xl md:border md:p-12 md:bg-pie-25 md:border-slate-400">
          <div class="flex flex-col gap-8">
            <form @submit.prevent="login">
              <div
                class="flex flex-col gap-3
                     [&_.v-field]:rounded-lg
                     [&_.v-field]:shadow-sm
                     [&_.v-field]:bg-white"
              >
                <v-text-field v-model="email" variant="outlined" :label="t('auth.fields.email')" append-inner-icon="mdi-email" />
                <v-text-field v-model="password" variant="outlined" :label="t('auth.fields.password')" :type="showPassword ? 'text' : 'password'" :append-inner-icon="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'" @click:append-inner="togglePasswordVisibility" />
              </div>
              <button type="submit" class="hidden transition-basic" />
            </form>
            <div
              class="bg-pie-700 rounded-lg p-1 hover:bg-pie-600 transition-basic"
              @click="login"
            >
              <div
                class="rounded-md border py-3 px-6 border-pie-300 text-lg-bold text-pie-50 flex flex-row items-center justify-center gap-1"
              >
                {{ $t('auth.buttons.login') }}
              </div>
            </div>
            <div class="text-base-normal text-center text-slate-800">
              {{ $t('auth.login.dont_have_account') }}
              <NuxtLinkLocale
                to="/registration"
                class="text-base-bold text-pie-700 hover:underline transition-basic"
              >
                {{ $t('auth.login.sign_up') }}
              </NuxtLinkLocale>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(input:-webkit-autofill),
:deep(input:-webkit-autofill:hover),
:deep(input:-webkit-autofill:focus),
:deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #000 !important;
  caret-color: #000;
  transition: background-color 9999s ease-in-out 0s;
}

:deep(.v-field__overlay) {
  background-color: white !important;
}
</style>
