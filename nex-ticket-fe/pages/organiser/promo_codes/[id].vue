<script lang="ts" setup>
import NexPromoCode from '@/models/NexPromoCode'
import { ArrowLeft01Icon, Edit02Icon } from 'hugeicons-vue'

interface FieldConfig {
  key: keyof NexPromoCode | 'usage'
  labelKey: string
  isEditable: boolean
  editComponent?: 'textarea' | 'checkbox' | 'number' | 'datetime-local' | 'text' | 'multiselect'
  editProps?: Record<string, any>
  displayFormatter?: (promo: NexPromoCode) => string
  condition?: (promo: NexPromoCode | null) => boolean
}

interface PromoCodeUpdatePayload {
  description?: string | null
  valid_from?: string | null
  valid_until?: string | null
  max_uses?: number | null
  max_uses_per_user?: number | null
  min_order_amount?: number | null
  active?: boolean
  applicable_event_ids?: number[]
  applicable_ticket_type_ids?: number[]
  disabled?: boolean
}

const feedback = useFeedback()

const { t } = useI18n()
const route = useRoute()
const organiserStore = useOrganiserStore()

const originalPromoCode = ref<NexPromoCode | null>(null)
const editablePromoCode = ref<NexPromoCode | null>(null)

const { pageLoading } = storeToRefs(useGeneralStore())
const isSaving = ref(false)
const error = ref<string>('')
const successMessage = ref<string>('')
const formErrors = ref<Record<string, string>>({})
const isEditing = ref(false)

const events = ref<{ id: number, name: string }[]>([])
const ticketTypes = ref<{ id: number, name: string }[]>([])
const loadingEvents = ref(false)
const loadingTickets = ref(false)

const promoCodeId = computed(() => route.params.id as string)
const defaultCurrency = computed(() => organiserStore.organiser?.default_currency || 'EUR')

const utils = usePromoCodeUtils(editablePromoCode, defaultCurrency)

const fieldConfig = computed((): FieldConfig[] => [
  { key: 'code', labelKey: 'promo_codes.attributes.code', isEditable: false },
  { key: 'description', labelKey: 'promo_codes.attributes.description', isEditable: true, editComponent: 'textarea', editProps: { rows: 2, placeholder: t('promo_codes.hints.description_placeholder') } },
  { key: 'active', labelKey: 'promo_codes.attributes.active', isEditable: true, editComponent: 'checkbox' },
  { key: 'discount_type', labelKey: 'promo_codes.attributes.discount_type', isEditable: false, displayFormatter: () => utils.discountTypeDisplay.value },
  { key: 'discount_value', labelKey: 'promo_codes.attributes.discount_value', isEditable: false, displayFormatter: () => utils.formattedDiscount.value },
  { key: 'usage', labelKey: 'promo_codes.attributes.usage', isEditable: false, displayFormatter: () => utils.formattedUsage.value },
  { key: 'max_uses', labelKey: 'promo_codes.attributes.max_uses', isEditable: true, editComponent: 'number', editProps: { min: 0, step: 1, placeholder: t('common.unlimited') } },
  { key: 'max_uses_per_user', labelKey: 'promo_codes.attributes.max_uses_per_user', isEditable: true, editComponent: 'number', editProps: { min: 1, step: 1, placeholder: t('common.unlimited') } },
  { key: 'min_order_amount', labelKey: 'promo_codes.attributes.min_order_amount', isEditable: true, editComponent: 'number', editProps: { min: 0, step: 0.01, placeholder: t('promo_codes.hints.min_amount_placeholder') } },
  { key: 'valid_from', labelKey: 'promo_codes.attributes.valid_from', isEditable: true, editComponent: 'datetime-local' },
  { key: 'valid_until', labelKey: 'promo_codes.attributes.valid_until', isEditable: true, editComponent: 'datetime-local' },
  { key: 'applicable_event_ids', labelKey: 'promo_codes.attributes.applicable_event_ids', isEditable: true, editComponent: 'multiselect' },
  { key: 'applicable_ticket_type_ids', labelKey: 'promo_codes.attributes.applicable_ticket_type_ids', isEditable: true, editComponent: 'multiselect' },
  { key: 'created_at', labelKey: 'promo_codes.attributes.created_at', isEditable: false },
  { key: 'updated_at', labelKey: 'promo_codes.attributes.updated_at', isEditable: false },
])

function getDisplayValue(field: FieldConfig): string {
  const promo = editablePromoCode.value
  if (!promo)
    return '-'

  if (field.displayFormatter) {
    return field.displayFormatter(promo)
  }

  switch (field.key) {
    case 'active':
      return utils.status.value.key !== 'unknown' ? t(utils.status.value.textKey) : '-'
    case 'valid_from':
      return utils.formattedValidFrom.value
    case 'valid_until':
      return utils.formattedValidUntil.value
    case 'created_at':
      return utils.formattedCreatedAt.value
    case 'updated_at':
      return utils.formattedUpdatedAt.value
    case 'applicable_event_ids':
      return utils.displayIds(promo.applicable_event_ids)
    case 'applicable_ticket_type_ids':
      return utils.displayIds(promo.applicable_ticket_type_ids)
    case 'max_uses':
      return promo.max_uses === null ? t('common.unlimited') : String(promo.max_uses)
    case 'max_uses_per_user':
      return promo.max_uses_per_user === null ? t('common.unlimited') : String(promo.max_uses_per_user)
    case 'min_order_amount':
      return utils.formattedMinOrderAmount.value
    case 'description':
      return promo.description || '-'
    default: {
      if (field.key in promo) {
        const value = promo[field.key as keyof NexPromoCode]
        return value === null || value === undefined ? '-' : String(value)
      }
      return '-'
    }
  }
}

onMounted(async () => {
  if (promoCodeId.value) {
    await loadPromoCode(promoCodeId.value)
  }
  else {
    error.value = t('promo_codes.errors.id_missing')
  }
})

async function loadPromoCode(id: string | number, resetEditState = true) {
  pageLoading.value = true
  error.value = ''
  if (resetEditState) {
    isEditing.value = false
    successMessage.value = ''
    formErrors.value = {}
  }

  try {
    const { data, error: apiError } = await useAPI<{ data: any }>(`/api/organiser/promo_codes/${id}`)

    if (apiError.value) {
      const message = apiError.value.data?.errors?.[0]?.detail || apiError.value.data?.error || apiError.value.message
      if (apiError.value.status === 404) {
        throw new Error(t('promo_codes.errors.not_found'))
      }
      if (apiError.value.status === 403) {
        throw new Error(t('promo_codes.errors.unauthorized'))
      }
      throw new Error(message || t('promo_codes.errors.load_detail_failed'))
    }

    const createdModel = NexPromoCode.create_from_request(data.value)

    if (!createdModel || Array.isArray(createdModel)) {
      throw new Error(t('promo_codes.errors.process_failed'))
    }

    originalPromoCode.value = createdModel
    editablePromoCode.value = structuredClone(createdModel)
  }
  catch (err: any) {
    feedback.error('Failed to load promo code:', { level: 'error', rollbar: true, extras: err })
    error.value = err.message || t('promo_codes.errors.load_detail_failed')
    originalPromoCode.value = null
    editablePromoCode.value = null
  }
  finally {
    pageLoading.value = false
  }
}

async function toggleEnable() {
  if (!editablePromoCode.value)
    return

  editablePromoCode.value.disabled = !editablePromoCode.value.disabled
  editablePromoCode.value.active = !editablePromoCode.value.active
  await saveChanges()

  updateEditableField('active', !originalPromoCode.value?.active)
  await loadPromoCode(promoCodeId.value)
}

function toggleEdit() {
  if (!originalPromoCode.value)
    return

  isEditing.value = !isEditing.value

  if (isEditing.value) {
    editablePromoCode.value = structuredClone(originalPromoCode.value)
    formErrors.value = {}
    error.value = ''
    successMessage.value = ''
  }
  else {
    editablePromoCode.value = structuredClone(originalPromoCode.value)
    formErrors.value = {}
    error.value = ''
  }
}

function updateEditableField(key: string, value: any) {
  if (!editablePromoCode.value)
    return

  let processedValue = value
  const fieldConf = fieldConfig.value.find(f => f.key === key)

  if (fieldConf?.editComponent === 'number') {
    processedValue = (value === '' || value === null) ? null : Number(value)
  }
  else if (fieldConf?.editComponent === 'checkbox') {
    processedValue = Boolean(value)
  }
  else if (fieldConf?.editComponent === 'datetime-local') {
    processedValue = value || null
  }
  else if (fieldConf?.editComponent === 'textarea') {
    processedValue = String(value)
  }

  if (key in editablePromoCode.value) {
    (editablePromoCode.value as any)[key] = processedValue
  }
}

async function saveChanges() {
  if (!editablePromoCode.value || !originalPromoCode.value) {
    error.value = t('promo_codes.errors.unexpected')
    return
  }

  isSaving.value = true
  formErrors.value = {}
  error.value = ''
  successMessage.value = ''

  try {
    let validFromISO: string | null = null
    let validUntilISO: string | null = null

    if (editablePromoCode.value.valid_from && typeof editablePromoCode.value.valid_from === 'string') {
      validFromISO = new Date(editablePromoCode.value.valid_from).toISOString()
    }
    if (editablePromoCode.value.valid_until && typeof editablePromoCode.value.valid_until === 'string') {
      validUntilISO = new Date(editablePromoCode.value.valid_until).toISOString()
    }

    const potentialPayload: PromoCodeUpdatePayload = {
      description: editablePromoCode.value.description,
      valid_from: validFromISO,
      valid_until: validUntilISO,
      max_uses: editablePromoCode.value.max_uses === null ? null : Number(editablePromoCode.value.max_uses),
      max_uses_per_user: editablePromoCode.value.max_uses_per_user === null ? null : Number(editablePromoCode.value.max_uses_per_user),
      min_order_amount: editablePromoCode.value.min_order_amount === null ? null : Number(editablePromoCode.value.min_order_amount),
      active: editablePromoCode.value.active,
      applicable_event_ids: editablePromoCode.value.applicable_event_ids || [],
      applicable_ticket_type_ids: editablePromoCode.value.applicable_ticket_type_ids || [],
      disabled: editablePromoCode.value.disabled,
    }

    const finalPayload: Partial<PromoCodeUpdatePayload> = {}
    let hasChanges = false
    const keys = Object.keys(potentialPayload) as Array<keyof PromoCodeUpdatePayload>

    for (const key of keys) {
      const currentValue = potentialPayload[key]
      const originalValue = originalPromoCode.value![key]

      let changed = false
      if (Array.isArray(currentValue) && Array.isArray(originalValue)) {
        const sortedCurrent = [...currentValue].sort((a, b) => a - b).join(',')
        const sortedOriginal = [...originalValue].sort((a, b) => a - b).join(',')
        if (sortedCurrent !== sortedOriginal) {
          changed = true
        }
      }
      else if (key === 'valid_from' || key === 'valid_until') {
        const currentISO = currentValue as string | null
        const originalDate = originalValue ? new Date(originalValue as string | Date) : null
        const originalISO = originalDate && !Number.isNaN(originalDate.getTime()) ? originalDate.toISOString() : null

        if (currentISO !== originalISO) {
          changed = true
        }
      }
      else if (currentValue !== originalValue) {
        if (currentValue !== null || originalValue !== null) {
          changed = true
        }
      }

      if (changed) {
        (finalPayload as any)[key] = currentValue
        hasChanges = true
      }
    }

    if (!hasChanges) {
      successMessage.value = t('promo_codes.update.no_changes')
      isEditing.value = false
      return
    }

    const { error: apiError } = await useAPI(`/api/organiser/promo_codes/${promoCodeId.value}`, {
      method: 'PATCH',
      body: { promo_code: finalPayload },
    })

    if (apiError.value) {
      if (apiError.value.status === 422 && apiError.value.data?.errors) {
        const errors = apiError.value.data.errors
        if (typeof errors === 'object' && !Array.isArray(errors)) {
          formErrors.value = Object.entries(errors).reduce(
            (acc, [field, messages]) => {
              const formField = field.replace(/^promo_code\./, '')
              const baseField = formField.replace(/\.\d+$/, '')
              acc[baseField] = Array.isArray(messages) ? messages.join(', ') : String(messages)
              return acc
            },
            {} as Record<string, string>,
          )
          error.value = t('promo_codes.errors.correct_errors')
        }
        else {
          error.value = Array.isArray(errors) ? errors.join(', ') : String(errors) || t('promo_codes.errors.validation')
        }
      }
      else if (apiError.value.status === 403) {
        error.value = t('promo_codes.errors.unauthorized_update')
      }
      else if (apiError.value.data?.error) {
        error.value = apiError.value.data.error
      }
      else {
        error.value = apiError.value.message || t('promo_codes.errors.update_failed')
      }
      return
    }

    successMessage.value = t('promo_codes.update.success')
    isEditing.value = false
    await loadPromoCode(promoCodeId.value, false)
  }
  catch (err: unknown) {
    feedback.error('Unexpected error saving promo code:', { level: 'error', rollbar: true, extras: err })
    error.value = err instanceof Error ? err.message : t('promo_codes.errors.unexpected')
  }
  finally {
    isSaving.value = false
  }
}

function goToList() {
  navigateToWLocale('/organiser/promo_codes')
}

async function loadEvents() {
  try {
    loadingEvents.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/event_tickets_promo_code/events', {})

    if (fetchError.value) {
      console.error(t('errors.fetch_events_error'), fetchError.value)
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      events.value = data.value as { id: number, name: string }[]
    }
  }
  catch (err: any) {
    console.error(t('errors.unexpected_error'), err)
    error.value = err.message
  }
  finally {
    loadingEvents.value = false
  }
}

async function loadTicketTypes() {
  try {
    loadingTickets.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/event_tickets_promo_code/tickets', {})

    if (fetchError.value) {
      console.error(t('errors.fetch_ticket_types_error'), fetchError.value)
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      ticketTypes.value = data.value as { id: number, name: string }[]
    }
  }
  catch (err: any) {
    console.error(t('errors.unexpected_error'), err)
    error.value = err.message
  }
  finally {
    loadingTickets.value = false
  }
}

onMounted(() => {
  loadEvents()
  loadTicketTypes()
})
</script>

<template>
  <div class="container mx-auto px-4 py-4 md:py-8 lg:max-w-5xl">
    <button
      class="flex mb-4 md:mb-6 items-center px-3 py-1 border border-slate-300 shadow-sm rounded-md text-slate-900 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pie-500 text-sm-medium transition duration-150 ease-in-out"
      @click="goToList"
    >
      <ArrowLeft01Icon class="w-4 h-4 mr-1.5" />
      <p>{{ t('common.back_to_list') }}</p>
    </button>

    <!-- placeholders while loading data -->
    <div v-if="pageLoading">
      <form>
        <div class="bg-white shadow-md md:shadow-lg overflow-hidden rounded-lg border border-slate-200">
          <div class="px-4 py-4 md:px-6 md:py-5 border-b border-slate-200 flex flex-col md:flex-row justify-between md:items-center gap-3">
            <div>
              <div class=" text-lg-medium md:text-xl-medium text-slate-900">
                {{ t('promo_codes.show.title') }}
              </div>
              <p class="mt-1 max-w-2xl text-sm-normal text-slate-500 flex items-center flex-wrap gap-x-2 gap-y-1">
                <span class="inline-flex text-xs-bold leading-4 rounded-full">
                  <div class="placeholder animate-pulse h-5 w-14" />
                </span>
              </p>
            </div>
            <div class="flex flex-shrink-0 gap-2 mt-3 md:mt-0">
              <button
                type="button"
                class="flex items-center justify-center gap-2 px-3 py-1.5 md:px-4 md:py-2 border border-transparent rounded-md shadow-sm text-white bg-pie-600 hover:bg-pie-700 text-base-medium transition-basic"
              >
                <Edit02Icon class="text-white w-6" />
                <p class="text-white">
                  {{ t('common.edit') }}
                </p>
              </button>

              <button
                type="button"
                class="flex items-center justify-center gap-2 px-3 py-1.5 md:px-4 md:py-2 border border-transparent rounded-md shadow-sm text-white bg-pie-600 hover:bg-pie-700 text-base-medium transition-basic"
              >
                <p class="text-white">
                  {{ !originalPromoCode?.disabled ? t('common.disable') : t('common.enable') }}
                </p>
              </button>
            </div>
          </div>

          <div class="border-t border-slate-200">
            <dl>
              <template v-for="index in 15" :key="index">
                <div
                  class="px-4 py-3 md:px-6 md:py-4 grid grid-cols-1 md:grid-cols-3 gap-1 md:gap-4"
                  :class="index % 2 === 0 ? ' bg-slate-200' : 'bg-white'"
                >
                  <div class="placeholder animate-pulse h-4" :class="index % 2 === 0 ? 'w-1/3' : 'w-1/2'" />

                  <dd class="mt-1 text-sm-normal text-slate-900 md:mt-0 md:col-span-2">
                    <span>
                      <span class="px-2 inline-flex text-xs-bold leading-4 rounded-full">
                        <div class="placeholder animate-pulse h-4" :class="index % 2 === 0 ? 'w-40' : 'w-32'" />
                      </span>
                    </span>
                  </dd>
                </div>
              </template>
            </dl>
          </div>
        </div>
      </form>
    </div>

    <div v-else-if="error && !isEditing && !originalPromoCode" class="border border-pie-300 bg-pie-50 text-pie-800 px-4 py-3 rounded-md relative mb-4 md:mb-6" role="alert">
      <strong class="text-sm-bold">{{ t('common.error') }}: </strong>
      <span class="block sm:inline text-sm-normal">{{ error }}</span>
    </div>

    <form v-else-if="editablePromoCode" @submit.prevent="saveChanges">
      <div class="bg-white shadow-md md:shadow-lg overflow-hidden rounded-lg border border-slate-200">
        <div class="px-4 py-4 md:px-6 md:py-5 border-b border-slate-200 flex flex-col md:flex-row justify-between md:items-center gap-3">
          <div>
            <div class=" text-lg-medium md:text-xl-medium text-slate-900">
              {{ t('promo_codes.show.title') }}
            </div>
            <p class="mt-1 max-w-2xl text-sm-normal text-slate-500 flex items-center flex-wrap gap-x-2 gap-y-1">
              <span
                v-if="utils.status.value.key !== 'unknown'"
                :class="utils.status.value.classes"
                class="px-2 py-0.5 inline-flex text-xs-bold leading-4 rounded-full"
              >
                {{ t(utils.status.value.textKey) }}
              </span>
            </p>
            <div v-if="isEditing" class="flex gap-2 mt-2 text-sm-medium text-pie-700">
              <Edit02Icon />
              <p>{{ t('promo_codes.show.editing_mode') }}</p>
            </div>
          </div>
          <div class="flex flex-shrink-0 gap-2 mt-3 md:mt-0">
            <button
              v-if="isEditing"
              type="button"
              :disabled="isSaving"
              class="px-3 py-1.5 md:px-4 md:py-2 border border-slate-300 rounded-md shadow-sm text-slate-700 bg-white hover:bg-slate-50 text-sm-medium transition-basic"
              :class="{ 'opacity-70 cursor-not-allowed': isSaving }"
              @click="toggleEdit"
            >
              {{ t('common.cancel') }}
            </button>
            <button
              v-if="isEditing"
              type="submit"
              :disabled="isSaving"
              class="px-3 py-1.5 md:px-4 md:py-2 border border-transparent rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700  text-sm-medium transition-basic"
              :class="{ 'opacity-50 cursor-not-allowed': isSaving }"
            >
              <span v-if="isSaving" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                {{ t('common.saving') }}
              </span>
              <span v-else>{{ t('common.save_changes') }}</span>
            </button>
            <button
              v-if="!isEditing"
              type="button"
              class="flex items-center justify-center gap-2 px-3 py-1.5 md:px-4 md:py-2 border border-transparent rounded-md shadow-sm text-white bg-pie-600 hover:bg-pie-700 text-base-medium transition-basic"
              @click="toggleEdit"
            >
              <Edit02Icon class="text-white w-6" />
              <p class="text-white">
                {{ t('common.edit') }}
              </p>
            </button>

            <button
              type="button"
              class="flex items-center justify-center gap-2 px-3 py-1.5 md:px-4 md:py-2 border border-transparent rounded-md shadow-sm text-white bg-pie-600 hover:bg-pie-700 text-base-medium transition-basic"
              @click="toggleEnable"
            >
              <p class="text-white">
                {{ !originalPromoCode?.disabled ? t('common.disable') : t('common.enable') }}
              </p>
            </button>
          </div>
        </div>

        <div v-if="(formErrors.base || error) && isEditing" class="px-4 py-3 md:px-6 bg-pie-50 border-y border-pie-200 text-pie-800 text-sm-normal" role="alert">
          {{ formErrors.base || error }}
        </div>
        <div v-if="successMessage && !isEditing" class="px-4 py-3 md:px-6 bg-green-50 border-y border-green-200 text-green-700 text-sm-normal" role="alert">
          {{ successMessage }}
        </div>

        <div class="border-t border-slate-200">
          <dl>
            <template v-for="(field, index) in fieldConfig" :key="field.key">
              <div
                v-if="(!field.condition || field.condition(editablePromoCode)) && editablePromoCode"
                class="px-4 py-3 md:px-6 md:py-4 grid grid-cols-1 md:grid-cols-3 gap-1 md:gap-4"
                :class="index % 2 === 0 ? ' bg-slate-200' : 'bg-white'"
              >
                <dt class="text-sm-medium text-slate-600">
                  {{ t(field.labelKey) }}
                </dt>

                <dd class="mt-1 text-sm-normal text-slate-900 md:mt-0 md:col-span-2">
                  <span v-if="!isEditing || !field.isEditable">
                    <span v-if="field.key === 'active'" :class="utils.status.value.classes" class="px-2 py-0.5 inline-flex text-xs-bold leading-4 rounded-full">
                      {{ getDisplayValue(field) }}
                    </span>
                    <span v-else>
                      {{ getDisplayValue(field) }}
                    </span>
                  </span>

                  <div v-else>
                    <input
                      v-if="!field.editComponent || field.editComponent === 'text'"
                      :value="editablePromoCode[field.key as keyof NexPromoCode]"
                      type="text"
                      v-bind="field.editProps"
                      class="block w-full px-3 py-1.5 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-pie-500 focus:border-pie-500 text-sm-normal"
                      :class="{ 'border-pie-500 ring-1 ring-pie-300': formErrors[field.key] }"
                      @input="updateEditableField(field.key, ($event.target as HTMLInputElement).value)"
                    >
                    <textarea
                      v-if="field.editComponent === 'textarea'"
                      :value="String(editablePromoCode[field.key as keyof NexPromoCode] ?? '')"
                      v-bind="field.editProps"
                      class="block w-full px-3 py-1.5 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-pie-500 focus:border-pie-500 text-sm-normal"
                      :class="{ 'border-pie-500 ring-1 ring-pie-300': formErrors[field.key] }"
                      @input="updateEditableField(field.key, ($event.target as HTMLTextAreaElement).value)"
                    />
                    <input
                      v-if="field.editComponent === 'checkbox'"
                      :checked="Boolean(editablePromoCode[field.key as keyof NexPromoCode])"
                      type="checkbox"
                      class="h-4 w-4 text-pie-600 focus:ring-pie-500 border-slate-300 rounded shadow-sm"
                      :class="{ 'border-pie-500 ring-1 ring-pie-300': formErrors[field.key] }"
                      @change="updateEditableField(field.key, ($event.target as HTMLInputElement).checked)"
                    >
                    <input
                      v-if="field.editComponent === 'number' && field.key !== 'min_order_amount'"
                      :value="editablePromoCode[field.key as keyof NexPromoCode]"
                      type="number"
                      v-bind="field.editProps"
                      class="block w-full md:w-auto md:max-w-xs px-3 py-1.5 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-pie-500 focus:border-pie-500 text-sm-normal"
                      :class="{ 'border-pie-500 ring-1 ring-pie-300': formErrors[field.key] }"
                      @input="updateEditableField(field.key, ($event.target as HTMLInputElement).value)"
                    >
                    <div v-if="field.editComponent === 'number' && field.key === 'min_order_amount'" class="relative md:max-w-xs">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-slate-500 text-sm-normal"> {{ defaultCurrency }} </span>
                      </div>
                      <input
                        :value="editablePromoCode[field.key as keyof NexPromoCode]"
                        type="number"
                        v-bind="field.editProps"
                        class="block w-full pl-10 pr-3 py-1.5 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-pie-500 focus:border-pie-500 text-sm-normal"
                        :class="{ 'border-pie-500 ring-1 ring-pie-300': formErrors[field.key] }"
                        @input="updateEditableField(field.key, ($event.target as HTMLInputElement).value)"
                      >
                    </div>
                    <input
                      v-if="field.editComponent === 'datetime-local'"
                      :value="editablePromoCode[field.key as keyof NexPromoCode]"
                      type="datetime-local"
                      class="block w-full md:w-auto md:max-w-xs px-3 py-1.5 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-pie-500 focus:border-pie-500 text-sm-normal"
                      :class="{ 'border-pie-500 ring-1 ring-pie-300': formErrors[field.key] }"
                      @input="updateEditableField(field.key, ($event.target as HTMLInputElement).value)"
                    >
                    <div v-if="field.editComponent === 'multiselect'">
                      <div class="p-3 border border-dashed border-slate-300 rounded-md bg-slate-50 text-slate-500 text-sm-normal">
                        {{ t('promo_codes.hints.multiselect_placeholder_title') }}
                        <p class="text-xs-normal mt-1">
                          {{ t('promo_codes.hints.multiselect_placeholder_text', { field: t(field.labelKey) }) }}
                        </p>
                      </div>

                      <!-- Autocomplete for EVENTS -->
                      <div v-if="field.key === 'applicable_event_ids'" class="mt-3">
                        <v-autocomplete
                          v-model="editablePromoCode.applicable_event_ids"
                          class="text-base-normal"
                          :items="events"
                          :loading="loadingEvents"
                          :label="t('promo_codes.attributes.applicable_event_ids')"
                          placeholder="Events Name"
                          multiple
                          chips
                          closable-chips
                          item-title="name"
                          item-value="id"
                          :error-messages="formErrors.applicable_event_ids"
                        >
                          <template #chip="{ props, item }">
                            <v-chip class="text-base-normal" v-bind="props" :text="item.raw.name" />
                          </template>
                          <template #item="{ props, item }">
                            <v-list-item class="text-sm-normal" v-bind="props" :title="item.raw.name" />
                          </template>
                        </v-autocomplete>

                        <p id="event-ids-hint" class="mt-1 text-xs-normal text-slate-500">
                          {{ $t('promo_codes.hints.blank_for_all') }}
                        </p>
                      </div>

                      <!-- Autocomplete for TICKET TYPES -->
                      <div v-if="field.key === 'applicable_ticket_type_ids'" class="mt-3">
                        <v-autocomplete
                          v-model="editablePromoCode.applicable_ticket_type_ids"
                          class="text-base-normal"
                          :items="ticketTypes"
                          :loading="loadingTickets"
                          :label="t('promo_codes.attributes.applicable_ticket_type_ids')"
                          placeholder="Tickets types"
                          multiple
                          chips
                          closable-chips
                          item-title="name"
                          item-value="id"
                          :error-messages="formErrors.applicable_ticket_type_ids"
                        >
                          <template #chip="{ props, item }">
                            <v-chip class="text-base-normal" v-bind="props" :text="item.raw.name" />
                          </template>
                          <template #item="{ props, item }">
                            <v-list-item class="text-sm-normal" v-bind="props" :title="item.raw.name" />
                          </template>
                        </v-autocomplete>

                        <p id="ticket-type-ids-hint" class="mt-1 text-xs-normal text-slate-500">
                          {{ $t('promo_codes.hints.blank_for_all_types') }}
                        </p>
                      </div>

                      <!-- Hint -->
                      <p class="mt-2 p-2 text-xs-normal bg-pie-50 border border-pie-200 text-pie-700 rounded">
                        <strong>{{ $t('promo_codes.hints.id_input_suggestion_title') }}</strong>
                        {{ $t('promo_codes.hints.id_input_suggestion_text') }}
                      </p>
                    </div>

                    <p v-if="isEditing && field.key === 'max_uses' && editablePromoCode?.uses_count > 0" class="mt-1 text-xs-normal text-slate-500">
                      {{ t('promo_codes.hints.max_uses_context', { count: editablePromoCode.uses_count }) }}
                    </p>

                    <p v-if="formErrors[field.key]" class="mt-1 text-xs-normal text-pie-700">
                      {{ formErrors[field.key] }}
                    </p>
                  </div>
                </dd>
              </div>
            </template>
          </dl>
        </div>
      </div>
    </form>

    <div v-else class="text-center py-10 text-slate-600">
      <p class="text-base-normal">
        {{ t('promo_codes.show.data_missing_fallback') }}
      </p>
      <button class="mt-4 text-sm-medium text-pie-600 hover:text-pie-800" @click="goToList">
        {{ t('promo_codes.show.return_to_list') }}
      </button>
    </div>
  </div>
</template>
