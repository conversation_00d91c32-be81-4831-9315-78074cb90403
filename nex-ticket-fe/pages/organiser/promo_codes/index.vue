<script lang="ts" setup>
import { useFeedback } from '~/composables/useFeedback'

interface PromoCodeSummary {
  id: number
  code: string
  discount_type: string
  discount_value: number
  uses_count: number
  max_uses: number | null
  active: boolean
  valid_from: string | null
  valid_until: string | null
  description?: string
}

const promoCodes = ref<PromoCodeSummary[]>([])
const { pageLoading } = storeToRefs(useGeneralStore())
const error = ref<string>('')
const organiserStore = useOrganiserStore()
const feedback = useFeedback()

onMounted(async () => {
  await loadPromoCodes()
})

async function loadPromoCodes() {
  pageLoading.value = true
  error.value = ''
  promoCodes.value = []

  try {
    const { data, error: apiError } = await useAPI<{ data: any[] }>('/api/organiser/promo_codes')

    if (apiError.value) {
      feedback.error(`Failed to load promo codes: ${apiError.value}`, { level: 'error', rollbar: true })
    }

    if (data.value?.data) {
      promoCodes.value = data.value.data.map((item: any) => ({
        id: item.id,
        ...item.attributes,
      })) as PromoCodeSummary[]
    }
    else {
      feedback.error('Unexpected API response structure for promo codes list:', { level: 'warning', rollbar: true, extras: data.value })
      promoCodes.value = []
    }
  }
  catch (err: any) {
    feedback.error(`Failed to load promo codes: ${err}`, { level: 'error', rollbar: true })
    error.value = err?.data?.errors?.[0]?.detail || err?.message || 'Could not load promo codes.'
  }
  finally {
    pageLoading.value = false
  }
}

function formatDiscount(promo: PromoCodeSummary): string {
  if (!promo.discount_type || promo.discount_value === null)
    return '-'

  switch (promo.discount_type) {
    case 'percentage_total':
      return `${promo.discount_value}%`
    case 'fixed_amount_total':

      return formatCurrency(promo.discount_value, organiserStore.organiser?.default_currency)
    default:
      feedback.error(`Unknown discount_type: ${promo.discount_type}`, { level: 'warning', rollbar: true })
      return `${promo.discount_value}`
  }
}

function formatUsage(promo: PromoCodeSummary): string {
  const max = promo.max_uses === null ? '∞' : promo.max_uses
  return `${promo.uses_count} / ${max}`
}

function getStatus(promo: PromoCodeSummary): { text: string, classes: string } {
  const validUntil = promo.valid_until ? new Date(promo.valid_until) : null
  const now = new Date()

  if (!promo.active) {
    return { text: 'Inactive', classes: 'bg-slate-200 text-slate-700' }
  }
  if (validUntil && validUntil < now) {
    return { text: 'Expired', classes: 'bg-orange-100 text-orange-700' }
  }
  if (promo.max_uses !== null && promo.uses_count >= promo.max_uses) {
    return { text: 'Fully Used', classes: 'bg-pie-200 text-pie-800' }
  }
  return { text: 'Active', classes: 'bg-green-100 text-green-700' }
}

function viewPromoCode(id: number | string) {
  navigateToWLocale(`/organiser/promo_codes/${id}`)
}

function createNewPromoCode() {
  navigateToWLocale('/organiser/promo_codes/create')
}
</script>

<template>
  <div class="container mx-auto px-4 py-8 md:w-5/6 lg:w-4/6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <h1 class="text-xl-bold md:text-2xl-bold text-slate-900">
        {{ $t('promo_codes.index.title') }}
      </h1>
      <button
        class="px-4 py-3 bg-pie-700 text-white rounded-md hover:bg-pie-500 text-base-medium transition-basic"
        @click="createNewPromoCode"
      >
        {{ $t('promo_codes.index.create_new') }}
      </button>
    </div>

    <!-- <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
      <strong class="font-bold">Error: </strong>
      <span class="block sm:inline">{{ error }}</span>
    </div> -->
    <ErrorOrganiser v-if="error" class="mt-4" :error="error" />

    <div v-else-if="pageLoading" class="overflow-x-auto shadow border border-slate-200 sm:rounded-lg">
      <table class="min-w-full divide-y divide-slate-200">
        <thead class="bg-slate-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.code') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.discount') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.usage') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.validity') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.status') }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-slate-200">
          <tr
            v-for="index in 15"
            :key="index"
            class="hover:bg-slate-50 cursor-pointer transition duration-150 ease-in-out"
          >
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="placeholder animate-pulse h-4" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="placeholder animate-pulse h-4" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="placeholder animate-pulse h-4" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="placeholder animate-pulse h-4" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="placeholder animate-pulse h-5" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div v-else-if="!promoCodes.length" class="text-center py-10 border border-slate-200 rounded-md bg-slate-50">
      <p class="text-slate-600 mb-4 text-base-normal">
        {{ $t('promo_codes.index.no_codes') }}
      </p>
      <button
        class="px-4 py-2 bg-pie-600 text-white rounded-md hover:bg-pie-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pie-500 text-sm-medium transition ease-in-out duration-150"
        @click="createNewPromoCode"
      >
        {{ $t('promo_codes.index.create_first') }}
      </button>
    </div>

    <div v-else class="overflow-x-auto shadow border border-slate-200 sm:rounded-lg">
      <table class="min-w-full divide-y divide-slate-200">
        <thead class="bg-slate-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.code') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.discount') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.usage') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.validity') }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs-medium text-slate-500 uppercase tracking-wider">
              {{ $t('promo_codes.attributes.status') }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-slate-200">
          <tr
            v-for="promo in promoCodes"
            :key="promo.id"
            class="hover:bg-slate-50 cursor-pointer transition duration-150 ease-in-out"
            @click="viewPromoCode(promo.id)"
          >
            <td class="px-6 py-4 whitespace-nowrap text-sm-medium text-slate-900">
              {{ promo.code }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm-normal text-slate-600">
              {{ formatDiscount(promo) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm-normal text-slate-600">
              {{ formatUsage(promo) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm-normal text-slate-600">
              {{ formatDate(promo.valid_from) }} - {{ formatDate(promo.valid_until) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm-normal text-slate-600">
              <span :class="getStatus(promo).classes" class="px-2 inline-flex text-xs-medium leading-5 rounded-full">
                {{ getStatus(promo).text }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
