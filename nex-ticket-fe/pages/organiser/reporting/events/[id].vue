<script lang="ts" setup>
import { ArcElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js'
import { Doughnut, Line } from 'vue-chartjs'
import { useI18n } from 'vue-i18n'

ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale, LinearScale, PointElement, LineElement)

const route = useRoute()

interface EventReportData {
  total_revenue: number
  total_tickets_sold: number
  tickettype_breakdown: Array<{ id: number, name: string, tickets_sold: number, total: number }>
  daily_sales_per_ticket_type: Array<{ id: number, name: string, date: string, total: number }>
}

interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string[]
    borderColor?: string
  }>
}

const { t } = useI18n()

const stats = ref<EventReportData | null>(null)
const { pageLoading } = storeToRefs(useGeneralStore())
const error = ref<string | null>(null)
const dateRange = ref({
  start: null as string | null,
  end: null as string | null,
})

const chartColors = [
  '#3B82F6',
  '#10B981',
  '#F59E0B',
  '#EF4444',
  '#8B5CF6',
  '#06B6D4',
  '#EC4899',
  '#F97316',
]

const mainStats = computed(() => stats.value
  ? [
      {
        title: t('organiser.reporting.event.total_revenue'),
        value: stats.value.total_revenue.toLocaleString('en-US', { style: 'currency', currency: 'EUR' }),
        icon: '💰',
      },
      {
        title: t('organiser.reporting.event.tickets_sold'),
        value: stats.value.total_tickets_sold.toLocaleString(),
        icon: '🎫',
      },
    ]
  : [])

const salesChartData = computed<ChartData>(() => {
  const dailyData = stats.value?.daily_sales_per_ticket_type || []
  const ticketTypes = [...new Set(dailyData.map(d => d.name))]

  return {
    labels: [...new Set(dailyData.map(d => new Date(d.date).toLocaleDateString()))],
    datasets: ticketTypes.map((name, idx) => ({
      label: name,
      data: dailyData.filter(d => d.name === name).map(d => d.total),
      borderColor: chartColors[idx % chartColors.length],
      tension: 0.3,
    })),
  }
})

const ticketTypeChartData = computed<ChartData>(() => ({
  labels: stats.value?.tickettype_breakdown.map(ticket => ticket.name) || [],
  datasets: [{
    label: t('organiser.reporting.event.ticket_types'),
    data: stats.value?.tickettype_breakdown.map(ticket => ticket.total) || [],
    backgroundColor: chartColors,
  }],
}))

async function fetchStats() {
  pageLoading.value = true
  error.value = null

  try {
    const query = {
      start_date: dateRange.value.start,
      end_date: dateRange.value.end,
      include_pending: true,
    }

    const { data, error: fetchError } = await useAPI(
      `/api/organiser/reporting/events/${route.params.id}`,
      { query },
    )

    if (fetchError.value) {
      throw new Error(fetchError.value.message)
    }
    else {
      stats.value = data.value as EventReportData
    }
  }
  catch (err: any) {
    error.value = err.message
  }
  finally {
    pageLoading.value = false
  }
}

function clearDateRange() {
  dateRange.value.start = null
  dateRange.value.end = null
}

watch(() => dateRange.value, fetchStats, { deep: true })
watch(() => route.params.id, fetchStats)

onMounted(fetchStats)
</script>

<template>
  <div class="mx-auto px-4 py-4 md:max-w-7xl md:px-4 md:py-8">
    <div class="flex justify-between items-center mb-4 md:mb-8">
      <div class="text-xl-bold text-slate-800 md:text-4xl-bold">
        {{ $t('organiser.reporting.event.event_report') }}
      </div>
      <button
        class="transition-basic bg-pie-800 text-white text-base-medium px-4 py-2 rounded-lg hover:bg-pie-700 md:text-lg-medium md:px-6 md:py-3"
        @click="clearDateRange"
      >
        {{ $t('organiser.reporting.event.show_all_data') }}
      </button>
    </div>

    <div class="bg-slate-100 shadow-pie-950/50 shadow-sm p-4 rounded-lg mb-4 border border-slate-200 md:p-6 md:mb-8">
      <div class="grid grid-cols-1 gap-4 md:flex md:gap-4 md:items-center">
        <div class="w-full md:flex-1">
          <label class="block text-base-medium text-slate-600 mb-1 md:text-lg-medium">{{ $t('organiser.reporting.event.start_date') }}</label>
          <input
            v-model="dateRange.start"
            type="date"
            class="w-full p-2 border rounded-md md:border-2"
          >
        </div>
        <div class="w-full md:flex-1">
          <label class="block text-base-medium text-slate-600 mb-1 md:text-lg-medium">{{ $t('organiser.reporting.event.end_date') }}</label>
          <input
            v-model="dateRange.end"
            type="date"
            class="w-full p-2 border rounded-md md:border-2"
          >
        </div>
      </div>
    </div>

    <div v-if="pageLoading" class="space-y-6 md:space-y-8">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3 md:gap-6">
        <div
          v-for="index in 2"
          :key="index"
          class="p-4 rounded-xl shadow-pie-950/20 shadow-md border border-slate-200 bg-slate-100 md:p-6"
        >
          <div class="flex items-center justify-between">
            <div class="flex flex-col gap-3 md:pb-1">
              <div class="placeholder animate-pulse h-5 w-32" />
              <div class="placeholder animate-pulse h-7 w-20" />
            </div>
            <div class="placeholder-circle size-12 animate-pulse" />
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-6 md:lg:grid-cols-2 md:gap-8">
        <div class="p-4 bg-slate-100 rounded-xl shadow-pie-950/20 shadow-md border border-slate-300 md:p-6">
          <div class="text-xl-bold text-slate-900 mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.event.sales_trend_by_ticket_type') }}
          </div>
          <div class="flex first-letter:h-64 md:h-80 h-60 items-center justify-between px-10">
            <div class="placeholder-circle size-24 animate-pulse" />
            <div class="placeholder-circle size-20 animate-pulse" />
            <div class="placeholder-circle size-16 animate-pulse" />
          </div>
        </div>

        <div class="p-4 bg-slate-100 shadow-pie-950/20 shadow-md rounded-xl border border-slate-300 md:p-6">
          <div class="text-xl-bold text-slate-900 mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.event.ticket_types_distribution') }}
          </div>
          <div class="flex h-64 md:h-80 items-center justify-center">
            <div class="flex placeholder-circle animate-pulse md:size-72 size-56 items-center justify-center">
              <div class="z-10 placeholder-circle-slate-100 md:size-36 size-28 animate-pulse bg-slate-100" />
            </div>
          </div>
        </div>
      </div>

      <div class="bg-slate-300 rounded-xl shadow-pie-950/20 shadow-2xl border border-slate-300 overflow-x-auto">
        <div class="p-4 border-b border-gray-200 md:p-6">
          <h3 class="text-xl-bold md:text-2xl-bold">
            {{ $t('organiser.reporting.event.ticket_type_performance') }}
          </h3>
        </div>
        <table class="w-full">
          <thead class="bg-slate-200 shadow-pie-700">
            <tr>
              <th class="px-4 py-2 text-left text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.event.ticket_type') }}
              </th>
              <th class="px-4 py-2 text-right text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.event.sold') }}
              </th>
              <th class="px-4 py-2 text-right text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.event.revenue') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="index in 5"
              :key="index"
              class="bg-slate-100 cursor-pointer"
            >
              <td class="px-4 py-2 md:px-6 md:py-4">
                <div class="placeholder animate-pulse h-6 md:h-5 w-32" />
              </td>
              <td class="px-4 py-2 float-right md:px-6 md:py-4">
                <div class="placeholder animate-pulse h-6 md:h-5 w-16" />
              </td>
              <td class="px-4 py-2 md:px-6 md:py-4">
                <div class="placeholder animate-pulse h-6 md:h-5 w-20 float-end" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div v-else-if="stats" class="space-y-6 md:space-y-8">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3 md:gap-6">
        <div
          v-for="stat in mainStats"
          :key="stat.title"
          class="p-4 rounded-xl shadow-pie-950/20 shadow-md border border-slate-200 bg-slate-100 md:p-6"
        >
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base-normal text-slate-400 mb-1 md:text-lg-normal">
                {{ stat.title }}
              </p>
              <p class="text-xl-bold md:text-2xl-bold">
                {{ stat.value }}
              </p>
            </div>
            <span class="text-2xl md:text-3xl">{{ stat.icon }}</span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8">
        <div class="p-4 bg-slate-100 rounded-xl shadow-pie-950/20 shadow-md border border-slate-300 md:p-6">
          <div class="text-xl-bold mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.event.sales_trend_by_ticket_type') }}
          </div>
          <div class="h-64 md:h-80">
            <Line
              :data="salesChartData"
              :options="{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  title: {
                    display: false,
                  },
                  legend: {
                    labels: {
                      font: {
                        family: 'Onest',
                        weight: 'bold',
                      },
                    },
                  },
                  tooltip: {
                    titleFont: {
                      family: 'Onest',
                      weight: 'bold',
                    },
                    bodyFont: {
                      family: 'Onest',
                      weight: 'bold',
                    },
                  },
                },
                scales: {
                  x: {
                    ticks: {
                      font: {
                        family: 'Onest',
                        size: 14,
                        weight: 'normal',
                      },
                    },
                  },
                  y: {
                    ticks: {
                      font: {
                        family: 'Onest',
                        size: 14,
                        weight: 'normal',
                      },
                    },
                  },
                },
              }"
            />
          </div>
        </div>

        <div class="p-4 bg-slate-100 rounded-xl shadow-pie-950/20 shadow-md border border-slate-300 md:p-6">
          <div class="text-xl-bold mb-4 md:text-2xl-bold">
            {{ $t('organiser.reporting.event.ticket_types_distribution') }}
          </div>
          <div class="h-64 md:h-80">
            <Doughnut
              :data="ticketTypeChartData"
              :options="{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  title: {
                    display: false,
                  },
                  legend: {
                    position: 'bottom',
                    labels: {
                      font: {
                        family: 'Onest',
                        size: 14,
                        weight: 'bold',
                      },
                    },
                  },
                  tooltip: {
                    borderWidth: 1,
                    titleFont: {
                      family: 'Onest',
                      size: 16,
                    },
                    bodyFont: {
                      family: 'Onest',
                      size: 14,
                    },
                  },
                },
              }"
            />
          </div>
        </div>
      </div>

      <div class="bg-slate-300 rounded-xl shadow-pie-950/20 shadow-2xl border border-slate-300 overflow-x-auto">
        <div class="p-4 border-b border-gray-200 md:p-6">
          <h3 class="text-xl-bold md:text-2xl-bold">
            {{ $t('organiser.reporting.event.ticket_type_performance') }}
          </h3>
        </div>
        <table class="w-full">
          <thead class="bg-slate-200 shadow-pie-700">
            <tr>
              <th class="px-4 py-2 text-left text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.event.ticket_type') }}
              </th>
              <th class="px-4 py-2 text-right text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.event.sold') }}
              </th>
              <th class="px-4 py-2 text-right text-base-bold text-slate-400 md:px-6 md:py-3 md:text-xl-bold">
                {{ $t('organiser.reporting.event.revenue') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="ticket in stats.tickettype_breakdown"
              :key="ticket.id"
              class="bg-slate-100 cursor-pointer"
            >
              <td class="px-4 py-2 text-base-normal text-slate-800 md:px-6 md:py-4 md:text-base-normal">
                {{ ticket.name }}
              </td>
              <td class="px-4 py-2 text-base-normal text-slate-800 text-right md:px-6 md:py-4 md:text-base-normal">
                {{ ticket.tickets_sold }}
              </td>
              <td class="px-4 py-2 text-base-normal text-slate-800 text-right md:px-6 md:py-4 md:text-base-normal">
                {{ ticket.total.toLocaleString('en-US', { style: 'currency', currency: 'EUR' }) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- insurence of information for user if stats are for some reason empty (should never happen) -->
    <div
      v-else
      class="md:p-4 md:text-2xl-medium
        p-3 text-red-500 text-center text-xl-medium"
    >
      {{ $t('organiser.reporting.event.no_data_loaded_info') }}
    </div>
  </div>
</template>
