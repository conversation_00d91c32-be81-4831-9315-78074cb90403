<script lang="ts" setup>
import { useAPI } from '@/composables/useAPI'
import Nexorganiser from '@/models/NexOrganiser'
import OrganiserProfilePicture from '~/components/OrganiserProfilePicture.vue'

const feedback = useFeedback()

const organiser = ref<Nexorganiser | null>(null)
const formErrors = ref<Record<string, string>>({}) // Changed to handle field-specific errors
const generalError = ref<string>('')
const successMessage = ref<string>('') // New ref for success message
const { pageLoading } = storeToRefs(useGeneralStore())
const savingChanges = ref(false)
const { t } = useI18n()

onMounted(async () => {
  await loadorganiserData()
})

async function loadorganiserData() {
  try {
    pageLoading.value = true
    const { data, error: fetchError } = await useAPI('/api/organiser/show') // Added leading slash for consistency

    if (fetchError.value) {
      feedback.error(t('errors.load_organiser_data_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      generalError.value = fetchError.value.message
      return
    }

    organiser.value = Nexorganiser.create_from_request(data.value) as Nexorganiser
  }
  catch (err: unknown) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    generalError.value = err instanceof Error ? err.message : t('errors.unknown_fail')
  }
  finally {
    pageLoading.value = false
  }
}

async function updateorganiser() {
  if (!organiser.value)
    return

  try {
    savingChanges.value = true
    formErrors.value = {} // Reset form errors
    generalError.value = ''
    successMessage.value = '' // Reset success message
    const { data, error: apiError } = await useAPI('/api/organiser/edit', {
      method: 'PUT',
      body: JSON.stringify(organiser.value), // Convert organiser object to JSON
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (apiError.value) {
      // Handle validation errors (assuming 422 status with { errors: { field: [messages] } } format)
      if (apiError.value.status === 422 && apiError.value.data?.errors) {
        formErrors.value = Object.entries(apiError.value.data.errors).reduce(
          (acc, [field, messages]) => {
            acc[field] = (messages as string[]).join(', ')
            return acc
          },
          {} as Record<string, string>,
        )
      }
      else {
        generalError.value = apiError.value.message || t('organiser.messages.update_failed')
      }
      return
    }

    if (data.value) {
      successMessage.value = t('organiser.messages.update_successful')
    }
  }
  catch (err: unknown) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    generalError.value = err instanceof Error ? err.message : t('errors.unknown_fail')
  }
  finally {
    savingChanges.value = false
  }
}
</script>

<template>
  <div class="md:w-1/2 md:mx-auto md:mt-10 md:p-6" md:rounded="xl">
    <v-container>
      <div class="text-2xl-bold text-center mb-4 md:text-4xl-bold md:text-center md:mb-6">
        {{ $t('organiser.data.edit_info') }}
      </div>

      <v-alert v-if="generalError" type="error" class="mb-2 md:mb-4">
        {{ generalError }}
      </v-alert>

      <v-alert v-if="successMessage" type="success" class="mb-2 md:mb-4">
        {{ successMessage }}
      </v-alert>

      <form v-if="pageLoading" class="space-y-2 flex flex-col gap-[1.375rem]" @submit.prevent="updateorganiser">
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="placeholder animate-pulse w-full h-14" />
        <div class="place-items-center transition-75">
          <button
            class="transition-basic w-2/5 flex justify-center py-2 px-4 rounded-md shadow-sm text-lg-bold bg-pie-700 hover:bg-pie-600 opacity-50 cursor-not-allowed"
            :disabled="true"
          >
            <span class="text-white">{{ $t('organiser.data.save_changes') }}</span>
          </button>
        </div>
      </form>

      <form v-else-if="organiser" class="space-y-2" @submit.prevent="updateorganiser">
        <OrganiserProfilePicture v-model:profile-image-data="organiser.profile_picture" />
        <v-text-field
          v-model="organiser.name"
          :label="$t('organiser.data.name')"
          variant="outlined"
          :error-messages="formErrors.name"
        />
        <v-text-field
          v-model="organiser.contact_email"
          :label="$t('organiser.data.contact_mail')"
          variant="outlined"
          :error-messages="formErrors.contact_email"
        />
        <v-text-field
          v-model="organiser.contact_mobile"
          :label="$t('organiser.data.contact_mobile')"
          variant="outlined"
          :error-messages="formErrors.contact_mobile"
        />
        <v-text-field
          v-model="organiser.reg_number"
          :label="$t('organiser.data.reg_number')"
          variant="outlined"
          :error-messages="formErrors.reg_number"
        />
        <v-text-field
          v-model="organiser.vat_number"
          :label="$t('organiser.data.vat_number')"
          variant="outlined"
          :error-messages="formErrors.vat_number"
        />
        <Autocomplete
          v-model="organiser.state_id"
          label-key="organiser.data.country"
          search-endpoint="/api/states"
          variant="outlined"
          field-width="100%"
          :error-messages="formErrors.country"
        />
        <Autocomplete
          v-model="organiser.default_currency"
          label-key="organiser.data.default_currency"
          search-endpoint="/api/currencies"
          variant="outlined"
          field-width="100%"
          :error-messages="formErrors.default_currency"
        />
        <div class="place-items-center transition-75">
          <button
            type="submit"
            :class="{ 'opacity-50 cursor-not-allowed': savingChanges }"
            class="transition-basic w-2/5 flex justify-center py-2 px-4 rounded-md shadow-sm text-lg-bold bg-pie-700 hover:bg-pie-600"
            :disabled="savingChanges"
          >
            <span v-if="savingChanges" class="text-slate-50">{{ $t('organiser.data.saving_changes') }}</span>
            <span v-else class="text-white">{{ $t('organiser.data.save_changes') }}</span>
          </button>
        </div>
      </form>
    </v-container>
  </div>
</template>
