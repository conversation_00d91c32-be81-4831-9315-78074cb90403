<script lang="ts" setup>
import type { SelectedTicket } from '@/models/NexTicketType'
import { useAPI } from '@/composables/useAPI'
import NexOrder from '@/models/NexOrder'
import { useFeedback } from '~/composables/useFeedback'

const { t } = useI18n()

const route = useRoute()
const currentStep = ref(0)
const orderCreated = ref(false)
const error = ref<string | null>(null)
const selectedTickets = ref<Record<number, SelectedTicket>>({})
const feedback = useFeedback()

async function createOrder() {
  orderCreated.value = false
  try {
    const { data: responseData, error: responseError } = await useAPI('/api/public/orders', {
      method: 'POST',
      body: {
        event_id: route.params.id,
      },
    })

    if (responseError.value) {
      error.value = responseError.value.message
      return
    }

    const order = NexOrder.create_from_request(responseData.value) as NexOrder
    const checkoutStore = useCheckoutStore()
    checkoutStore.setOrder(order)

    if (order === null) {
      feedback.error(t('errors.order_errors.create_order_fail'), { level: 'error', rollbar: true })
      return
    }
    orderCreated.value = true
  }
  catch (err: unknown) {
    error.value = err instanceof Error ? err.message : t('errors.event_errors.load_event_fail')
  }
}

async function handleContinueSelector() {
  await createOrder()
  if (orderCreated.value === true) {
    currentStep.value = 1
  }
}

async function handleToPayment() {
  currentStep.value = 2
}

function backToTicketSelector() {
  currentStep.value = 0
}

function toCheckoutInputs() {
  currentStep.value = 1
}

function handleStepClick(step: number) {
  if (currentStep.value > step) {
    currentStep.value = step
  }
}
</script>

<template>
  <div class="md:max-w-[72rem] mx-auto ">
    <div v-if="currentStep > 0" class="w-full flex justify-center md:w-full md:flex md:justify-center md:px-8 px-4">
      <TicketSelectorCheckoutStepper
        :steps="[
          { label: $t('public.ticket.checkout.stepper.select') },
          { label: $t('public.ticket.checkout.stepper.checkout') },
          { label: $t('public.ticket.checkout.stepper.payment') },
        ]"
        :current-step="currentStep"
        @step-click="handleStepClick"
      />
    </div>

    <div v-if="currentStep === 0">
      <TicketSelectorTickets
        @continue-button-clicked="handleContinueSelector"
        @selected-tickets="selectedTickets"
      />
    </div>

    <div v-if="currentStep === 1">
      <TicketSelectorCheckoutInputs
        @payment-button-clicked="handleToPayment"
        @back-button-clicked="backToTicketSelector"
      />
    </div>

    <div v-if="currentStep === 2">
      <TicketSelectorCheckout @step-back-event="toCheckoutInputs" />
    </div>
  </div>
</template>
