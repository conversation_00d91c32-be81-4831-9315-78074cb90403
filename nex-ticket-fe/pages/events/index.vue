<script lang="ts" setup>
import type NexCity from '@/models/NexCity'
import type { FunctionalComponent } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { useCityInfo } from '@/composables/cityInfo'
import NexEvent from '@/models/NexEvent'
import NexTagCategory from '@/models/NexTagCategory'
import { formatISO, isValid, parseISO } from 'date-fns'
import { CoinsEuroIcon, SortByDown02Icon } from 'hugeicons-vue'
import { useFeedback } from '~/composables/useFeedback'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const { city, pageLoading } = storeToRefs(useGeneralStore())
const cityInfo = ref<NexCity | null>(null)
const { loadInfo: loadCityInfo } = useCityInfo()

const events = ref<NexEvent[]>([])
const error = ref<string>('')
const feedback = useFeedback()

// filters
const categFilterVal = ref<string>('')
const dateFilterRange = ref<[Date | null, Date | null] | null>(null)
const priceFilerMin = ref<number | undefined>()
const priceFilerMax = ref<number | undefined>()
const gKeyForFilter = ref<string>('')
const choosenCityValue = ref<number | undefined>()
const upcomingText = ref<string>('Upcoming events:')

interface GenreOption { name: string, id: number }
const selectedGenreIds = ref<number[]>([])
const filterOptions = ref<GenreOption[]>([])

interface SortOption {
  value: string
  direction: 'ASC' | 'DESC' | ''
  icon: FunctionalComponent | null
  sort: string
}
const sortOptions = ref<SortOption>({ value: '', direction: '', icon: null, sort: '' })

interface TempFilterOption {
  option: string
  icon: FunctionalComponent
  sort: string
}
const availableSortOptionsForInit: TempFilterOption[] = [
  { option: 'Newest first', icon: SortByDown02Icon, sort: 'DATE' },
  { option: 'Cheapest first', icon: CoinsEuroIcon, sort: 'PRICE' },
]
const selectedGenresNames = ref<string[]>([])

async function loadGenreFilterOptions() {
  try {
    const { data, error: fetchError } = await useAPI<any>('/api/public/tag_categories/by_name/music_gen')
    if (fetchError.value) {
      feedback.error(`Failed to load genre filter options: %{fetchError.value}`, { level: 'error', rollbar: true })
      filterOptions.value = []
      return
    }

    if (data.value) {
      const tagCategory = NexTagCategory.create_from_request(data.value) as NexTagCategory
      if (tagCategory && tagCategory.tags) {
        filterOptions.value = tagCategory.tags.map(tag => ({
          name: tag.name,
          id: tag.id,
        }))
      }
      else {
        filterOptions.value = []
      }
    }
  }
  catch (err: any) {
    feedback.error('Unexpected error loading genre filter options:', { level: 'error', rollbar: true, extras: err })
    error.value = `Error loading genres: ${err.message}`
    filterOptions.value = []
  }
}

function safeParseISO(dateString: string | null | undefined): Date | null {
  if (!dateString || typeof dateString !== 'string')
    return null
  try {
    const parsed = parseISO(dateString)
    return isValid(parsed) ? parsed : null
  }
  catch (e) {
    feedback.error('Failed to parse date string:', { level: 'error', rollbar: true, extras: [dateString, e] })
    return null
  }
}

function setInitValuesFromRoute(currentRoute: RouteLocationNormalizedLoaded) {
  const query = currentRoute.query
  try {
    selectedGenreIds.value = query.tags ? JSON.parse(query.tags as string) : []
  }
  catch (e) {
    feedback.error(`Failed to parse tags query parameter: ${query.tags}, ${e}`, { level: 'error', rollbar: true })
    selectedGenreIds.value = []
  }

  const minDateQuery = safeParseISO(query.min_date as string | undefined)
  const maxDateQuery = safeParseISO(query.max_date as string | undefined)

  priceFilerMin.value = query.min_price ? Number(query.min_price) : undefined
  priceFilerMax.value = query.max_price ? Number(query.max_price) : undefined
  dateFilterRange.value = [minDateQuery, maxDateQuery]
  choosenCityValue.value = query.city ? Number(query.city) : choosenCityValue.value
  gKeyForFilter.value = query.g_key ? query.g_key as string : ''

  const dateSortDir = query.date_sort_direction as SortOption['direction'] | undefined
  const priceSortDir = query.price_sort_direction as SortOption['direction'] | undefined

  sortOptions.value = { value: '', direction: '', icon: null, sort: '' }

  if (dateSortDir && (dateSortDir === 'ASC' || dateSortDir === 'DESC')) {
    const sortConfig = availableSortOptionsForInit.find(opt => opt.sort === 'DATE')
    if (sortConfig) {
      sortOptions.value = {
        value: sortConfig.option.toUpperCase(),
        direction: dateSortDir,
        icon: sortConfig.icon,
        sort: 'DATE',
      }
    }
  }
  else if (priceSortDir && (priceSortDir === 'ASC' || priceSortDir === 'DESC')) {
    const sortConfig = availableSortOptionsForInit.find(opt => opt.sort === 'PRICE')
    if (sortConfig) {
      sortOptions.value = {
        value: sortConfig.option.toUpperCase(),
        direction: priceSortDir,
        icon: sortConfig.icon,
        sort: 'PRICE',
      }
    }
  }

  if (!sortOptions.value.sort && query.sortField && query.direction) {
    const routeSortField = String(query.sortField).toUpperCase()
    const routeDirection = String(query.direction).toUpperCase() as SortOption['direction']
    const initialSortOption = availableSortOptionsForInit.find(opt => opt.sort.toUpperCase() === routeSortField)

    if (initialSortOption && (routeDirection === 'ASC' || routeDirection === 'DESC')) {
      sortOptions.value = {
        value: initialSortOption.option.toUpperCase(),
        direction: routeDirection,
        icon: initialSortOption.icon,
        sort: initialSortOption.sort,
      }
    }
  }
}

function formatQueryDate(date: Date | undefined | null): string | undefined {
  return date && isValid(date) ? formatISO(date, { representation: 'date' }) : undefined
}

function updateRouteQuery() {
  const newQuery: Record<string, any> = {}

  if (selectedGenreIds.value.length > 0) {
    newQuery.tags = JSON.stringify(selectedGenreIds.value)
  }

  if (dateFilterRange.value) {
    const [minDate, maxDate] = dateFilterRange.value
    const minDateStr = formatQueryDate(minDate)
    const maxDateStr = formatQueryDate(maxDate)

    if (minDateStr)
      newQuery.min_date = minDateStr
    if (maxDateStr)
      newQuery.max_date = maxDateStr
  }

  if (priceFilerMin.value !== undefined && priceFilerMin.value !== null) {
    newQuery.min_price = String(priceFilerMin.value)
  }
  if (priceFilerMax.value !== undefined && priceFilerMax.value !== null) {
    newQuery.max_price = String(priceFilerMax.value)
  }

  if (sortOptions.value.sort && sortOptions.value.direction) {
    if (sortOptions.value.sort === 'DATE') {
      newQuery.date_sort_direction = sortOptions.value.direction
      delete newQuery.price_sort_direction
    }
    else if (sortOptions.value.sort === 'PRICE') {
      newQuery.price_sort_direction = sortOptions.value.direction
      delete newQuery.date_sort_direction
    }
  }
  else {
    delete newQuery.date_sort_direction
    delete newQuery.price_sort_direction
  }

  if (choosenCityValue.value !== undefined && choosenCityValue.value !== null) {
    newQuery.city = String(choosenCityValue.value)
  }

  const managedKeys = ['tags', 'min_date', 'max_date', 'min_price', 'max_price', 'date_sort_direction', 'price_sort_direction', 'city', 'g_key']
  const currentManagedQuery: Record<string, any> = {}
  managedKeys.forEach((key) => {
    if (route.query[key] !== undefined) {
      currentManagedQuery[key] = route.query[key]
    }
  })

  if (JSON.stringify(newQuery) !== JSON.stringify(currentManagedQuery)) {
    router.replace({ query: newQuery })
  }
}

async function handleFilterChange() {
  updateRouteQuery()
  await loadEvents()
  updateUpcomingTextFromRoute(route)
}

watch(choosenCityValue, async () => {
  city.value = choosenCityValue.value ? choosenCityValue.value.toString() : city.value // Update global store
  updateRouteQuery()
  await loadEvents()
  cityInfo.value = await loadCityInfo(choosenCityValue.value)
})

onMounted(async () => {
  pageLoading.value = true
  await loadGenreFilterOptions()
  setInitValuesFromRoute(route)
  choosenCityValue.value = loadDefaultCity()
  await loadEvents()
  cityInfo.value = await loadCityInfo(choosenCityValue.value)
  updateUpcomingTextFromRoute(route)
  pageLoading.value = false
})

function loadDefaultCity(): number {
  const defaultCity = city.value != null ? Number.parseInt(city.value) : Number.NaN
  return Number.isNaN(defaultCity) ? 765 : defaultCity // TODO: Preset Warsaw as default city for now, fix later
}

function updateUpcomingTextFromRoute(currentRoute: RouteLocationNormalizedLoaded) {
  const tagsQuery = currentRoute.query.tags
  let parsedTags: number[] = []

  try {
    parsedTags = tagsQuery ? JSON.parse(tagsQuery as string) : []
  }
  catch (e) {
    feedback.error('Failed to parse tags from route query:', { level: 'error', rollbar: true, extras: e })
    parsedTags = [] // Reset to empty array on error
  }

  selectedGenresNames.value = filterOptions.value
    .filter(option => parsedTags.includes(option.id))
    .map(option => option.name)

  changeUpcomingText(filterOptions.value.filter(option => parsedTags.includes(option.id)))
}

async function loadEvent(eventId: number) {
  try {
    const { data, error: fetchError } = await useAPI(`/api/public/events/${eventId}`)
    if (fetchError.value) {
      feedback.error(t('errors.event_errors.fetch_saved_event_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      const index = events.value.findIndex(obj => obj.id === eventId)
      if (index !== -1) {
        events.value[index] = NexEvent.create_from_request(data.value) as NexEvent
      }
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message
  }
}

async function loadEvents() {
  pageLoading.value = true
  try {
    let minDate, maxDate
    if (dateFilterRange.value) {
      [minDate, maxDate] = dateFilterRange.value
    }

    const queryParams: Record<string, any> = {
      includes: 'ticket_types,tags,organiser',
      min_date: formatQueryDate(minDate),
      max_date: formatQueryDate(maxDate),
      min_price: priceFilerMin.value,
      max_price: priceFilerMax.value,
      category: categFilterVal.value || undefined,
      city: choosenCityValue.value,
      tags: selectedGenreIds.value.length > 0 ? JSON.stringify(selectedGenreIds.value) : undefined,
    }

    if (sortOptions.value.sort && sortOptions.value.direction) {
      if (sortOptions.value.sort === 'DATE') {
        queryParams.date_sort_direction = sortOptions.value.direction
      }
      else if (sortOptions.value.sort === 'PRICE') {
        queryParams.price_sort_direction = sortOptions.value.direction
      }
    }

    Object.keys(queryParams).forEach(key => queryParams[key] === undefined && delete queryParams[key])

    const { data, error: fetchError } = await useAPI('/api/public/events', {
      query: queryParams,
    })

    if (fetchError.value) {
      feedback.error(t('errors.event_errors.fetch_events_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
      events.value = []
      return
    }

    if (data.value) {
      events.value = NexEvent.create_from_request(data.value) as NexEvent[]
      error.value = ''
    }
    else {
      events.value = []
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message
    events.value = []
  }
  finally {
    pageLoading.value = false
  }
}

function changeUpcomingText(selectedGenres: { name: string }[]) {
  if (selectedGenres.length === 0) {
    upcomingText.value = 'Upcoming events:'
    return
  }

  upcomingText.value = 'Upcoming '
  const loopCount = Math.min(selectedGenres.length, 3)

  for (let i = 0; i < loopCount; i++) {
    upcomingText.value += `#${selectedGenres[i].name}${i < loopCount - 1 ? ' | ' : ''}`
  }

  if (selectedGenres.length > 3) {
    upcomingText.value += ' | ... '
  }

  upcomingText.value += ' events:'
}
</script>

<template>
  <div class="md:gap-16 md:pt-0 flex flex-col gap-9 px-8 pt-12 lg:pt-0 max-w-[80rem] mx-auto">
    <div class="md:gap-4 gap-3 flex flex-col">
      <div>
        <CityAutocomplete
          v-model="choosenCityValue"
          label-key="public.index.dropdown.title"
          variant="underlined"
        />
      </div>
      <div
        class="md:py-3 md:overflow-visible flex gap-3 overflow-x-scroll scrollbar-not-display"
      >
        <PopoverSortPanel
          v-model:sort-options="sortOptions"
          class="md:z-30 flex-shrink-0"
          @apply-sort="handleFilterChange"
        />
        <PopoverGenresFilterPanel
          v-model:selected-ids="selectedGenreIds"
          :options="filterOptions"
          filter-name-key="public.index.filters.filterNames.categories"
          class="md:z-30 flex-shrink-0"
          @apply="handleFilterChange"
        />
        <PopoverDateFilterPanel
          v-model="dateFilterRange"
          filter-name-key="public.index.filters.filterNames.date"
          :min-date="new Date()"
          placeholder="Select dates"
          class="md:z-30 flex-shrink-0"
          @apply="handleFilterChange"
        />
        <PopoverPriceFilterPanel
          v-model:first-value="priceFilerMin"
          v-model:second-value="priceFilerMax"
          filter-name-key="public.index.filters.filterNames.price"
          any-price-text-key="public.index.filters.price_filter.any_price"
          free-text-key="public.index.filters.price_filter.free"
          :min-range="0"
          :max-range="100"
          :currency-code="cityInfo !== null ? cityInfo.state.currency_code : 'EUR'"
          class="md:z-30 flex-shrink-0"
          @apply-filter="handleFilterChange"
        />
      </div>
    </div>

    <div class="flex flex-col rounded-2xl py-4 gap-6">
      <div v-if="pageLoading">
        <h2 class="md:text-3xl-medium text-xl-medium text-slate-500">
          <div class="placeholder animate-pulse h-7 w-3/5 md:w-1/3 md:h-9" />
        </h2>
      </div>
      <div v-else>
        <h2 class="md:text-3xl-medium text-xl-medium text-slate-500">
          {{ upcomingText }}
        </h2>
      </div>
      <ErrorPublic v-if="error" class="mt-4" :error="error" />
      <div v-else>
        <div
          v-if="pageLoading"
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 items-center"
        >
          <div v-for="index in 8" :key="`skeleton-${index}`">
            <EventBlock
              :id="index"
              class="hidden md:block"
              main-photo-url=""
              name="Loading event..."
              venue-name="Loading location..."
              venue-address="Loading address..."
              :start-time="new Date()"
              :end-time="new Date()"
              :saved="false"
              mode="public"
              block-width-class="w-[302px]"
              photo-height-class="h-[154px]"
              :is-loading="true"
            />
            <EventBlock
              :id="index"
              class="md:hidden"
              main-photo-url=""
              name="Loading event..."
              venue-name="Loading location..."
              venue-address="Loading address..."
              :start-time="new Date()"
              :end-time="new Date()"
              :saved="false"
              mode="public"
              block-width-class="w-full" photo-height-class="h-[164px]"
              :is-loading="true"
            />
          </div>
        </div>

        <div
          v-else
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 items-stretch"
        >
          <div v-if="events.length === 0" class="col-span-full text-center text-slate-500 text-lg-medium py-10">
            No events found matching your criteria. Try adjusting the filters.
          </div>
          <div v-for="event in events" :key="event.id">
            <EventBlock
              :id="event?.id"
              class="hidden md:block h-full" :main-photo-url="event?.main_photo?.url"
              :name="event?.name"
              :venue-name="event?.venue_name"
              :venue-address="event?.venue_address"
              :start-time="event?.start_time"
              :end-time="event?.end_time"
              :saved="event?.saved"
              mode="public"
              block-width-class="w-[302px]"
              photo-height-class="h-[154px]"
              @event-saved="loadEvent(event.id)"
            />
            <EventBlock
              :id="event?.id"
              class="md:hidden h-full" :main-photo-url="event?.main_photo?.url"
              :name="event?.name"
              :venue-name="event?.venue_name"
              :venue-address="event?.venue_address"
              :start-time="event?.start_time"
              :end-time="event?.end_time"
              :saved="event?.saved"
              mode="public"
              block-width-class="" photo-height-class="h-[164px]"
              @event-saved="loadEvent(event.id)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-not-display::-webkit-scrollbar {
  display: none;
}

.scrollbar-not-display {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.grid {
  transition: all 0.3s ease-in-out;
}
</style>
