<script lang="ts" setup>
import { ErrorMessage, Field, Form } from 'vee-validate'
import * as yup from 'yup'

const email: Ref<string> = ref('')

const emailRules = yup.string().required().email()

// const { staus } = useAuth()
// TODO add mail sending functionality
async function handleSubmit() {
  //  await signIn({ user: { email: email.value, password: password.value } }, { callbackUrl: '/' })
}
</script>

<template>
  <div>
    <Form class="md:max-w-sm md:mx-auto" @submit.prevent="handleSubmit">
      <h1
        class="md:mb-4 md:text-center md:text-4xl-extrabold md:leading-none md:tracking-tight md:text-slate-800 md:md:text-5xl md:lg:text-6xl"
      >
        {{ $t('auth.forgotten_pswd.change') }}
      </h1>
      <div class="md:mb-5 md:relative">
        <label for="email" class="md:block md:mb-2 md:text-sm-medium md:text-slate-800">{{ $t('auth.forgotten_pswd.your_email') }}</label>
        <Field
          id="email" v-model="email" type="email" name="email" :rules="emailRules"
          class="md:shadow-sm md:bg-gray-50 md:border md:border-gray-300 md:text-slate-800 md:text-sm md:rounded-lg md:focus:ring-blue-500 md:focus:border-blue-500 md:block md:w-full md:p-2.5"
          placeholder="<EMAIL>"
        />
        <ErrorMessage name="email" />
      </div>
      <div>
        <button
          type="submit"
          class="transition-basic md:mb-5 md:text-white md:bg-blue-700 md:hover:bg-blue-800 md:focus:ring-4 md:focus:outline-none md:focus:ring-blue-300 md:text-sm-medium md:rounded-lg md:w-full md:sm:w-auto md:px-5 md:py-2.5 md:text-center"
        >
          {{ $t('auth.forgotten_pswd.send') }}
          {{ $t('auth.forgotten_pswd.email') }}
        </button>
      </div>
      <div>
        <p class="md:text-lg-normal md:text-black md:lg:text-s">
          {{ $t('auth.forgotten_pswd.sent_email') }}
        </p>
      </div>
    </Form>
  </div>
</template>

<style></style>
