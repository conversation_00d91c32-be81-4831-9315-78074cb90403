<script lang="ts" setup>
import { SentIcon } from 'hugeicons-vue'
import { da } from 'vuetify/lib/locale/index.mjs'
import { boolean as yupBoolean, object as yupObject, string as yupString } from 'yup'

const { t } = useI18n()

const schema = computed(() => {
  return toTypedSchema(
    yupObject({
      name: yupString().required().min(5).max(25).label(t('auth.fields.name')),
      email: yupString().email().required().label(t('auth.fields.email')),
      subject: yupString().required().min(5).max(20).label(t('auth.fields.subject')),
      message: yupString().required().min(10).max(2500).label(t('auth.fields.contact_message')),
      user_agreement: yupBoolean().required().oneOf([true]),
    }),
  )
})

const feedback = useFeedback()

const { validate } = useForm({
  validationSchema: schema,
})

const name = useField<string>('name')
const email = useField<string>('email')
const subject = useField<string>('subject')
const message = useField<string>('message')
const user_agreement = useField<boolean>('user_agreement')
const serverResponse = ref<null | any>(null)

const validatedInput = ref<boolean>(false)

async function sendMessage() {
  const isValid = await validate()
  validatedInput.value = true
  if (isValid.valid) {
    try {
      const { error: fetchError } = await useAPI('/api/public/contact_support', {
        method: 'POST',
        body: {
          name: name.value.value,
          email: email.value.value,
          subject: subject.value.value,
          message: message.value.value,
        },
      })
      if (fetchError.value) {
        feedback.error('Error creating event:', { level: 'error', rollbar: true, extras: fetchError.value })
        serverResponse.value = fetchError.value.message
      }
    }
    catch (err: any) {
      feedback.error('Unexpected error:', { level: 'error', rollbar: true, extras: err })
      serverResponse.value = err.message
    }
  }
}
</script>

<template>
  <div class="w-full px-4 max-w-[95vw] lg:max-w-[75rem] mx-auto py-8 md:py-12 lg:py-16">
    <div class="flex flex-col gap-8 md:gap-12">
      <!-- Header Section -->
      <div class="flex flex-col gap-4 md:gap-6 text-center">
        <h1 class="text-3xl-bold md:text-5xl-bold lg:text-6xl text-pie-700 leading-tight">
          {{ t('info.contact.contact_ticketpie_support') }}
        </h1>
        <p class="text-lg-medium md:text-xl-medium text-slate-600 max-w-3xl mx-auto leading-relaxed">
          {{ t('info.contact.details') }}
        </p>
      </div>

      <!-- Contact Form -->
      <div class="w-full max-w-3xl mx-auto">
        <div class="bg-white border border-slate-200 shadow-xl rounded-3xl p-8 md:p-10 lg:p-12">
          <div class="flex flex-col gap-8">
            <!-- Name and Email Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="flex flex-col gap-2">
                <label class="text-sm-bold text-slate-700 ml-1">
                  {{ t('auth.fields.name') }}
                </label>
                <v-text-field
                  v-model="name.value.value"
                  variant="outlined"
                  :placeholder="t('auth.fields.name')"
                  append-inner-icon="mdi-account"
                  :error-messages="name.errorMessage.value"
                  class="w-full"
                  density="comfortable"
                  hide-details="auto"
                />
              </div>
              <div class="flex flex-col gap-2">
                <label class="text-sm-bold text-slate-700 ml-1">
                  {{ t('auth.fields.email') }}
                </label>
                <v-text-field
                  v-model="email.value.value"
                  variant="outlined"
                  :placeholder="t('auth.fields.email')"
                  append-inner-icon="mdi-email"
                  :error-messages="email.errorMessage.value"
                  class="w-full"
                  density="comfortable"
                  hide-details="auto"
                />
              </div>
            </div>

            <!-- Subject -->
            <div class="flex flex-col gap-2">
              <label class="text-sm-bold text-slate-700 ml-1">
                {{ t('auth.fields.subject') }}
              </label>
              <v-text-field
                v-model="subject.value.value"
                variant="outlined"
                :placeholder="t('auth.fields.subject')"
                :error-messages="subject.errorMessage.value"
                class="w-full"
                density="comfortable"
                hide-details="auto"
              />
            </div>

            <!-- Message -->
            <div class="flex flex-col gap-2">
              <label class="text-sm-bold text-slate-700 ml-1">
                {{ t('info.contact.message') }}
              </label>
              <v-textarea
                id="message"
                v-model="message.value.value"
                :placeholder="t('auth.fields.contact_message')"
                :error-messages="message.errorMessage.value"
                variant="outlined"
                rows="6"
                class="w-full"
                density="comfortable"
                hide-details="auto"
                auto-grow
              />
            </div>

            <!-- User Agreement -->
            <div class="flex flex-col gap-4 pt-2">
              <NexCheckbox
                id="user_agreement"
                v-model="user_agreement.value.value"
                :text="t('public.event_info.contact_form.user_agreement')"
                :validated="validatedInput"
                :required="true"
              />
            </div>

            <!-- Submit Button -->
            <div class="flex justify-center pt-6">
              <NexButton
                :text="$t('public.event_info.contact_form.buttons.send')"
                variant="primary"
                :append-icon="SentIcon"
                :paddingx="10"
                :paddingy="5"
                text-style="text-lg-bold"
                :second-border="true"
                border-color="white"
                class="md:w-auto w-full min-w-[200px]"
                @click="sendMessage"
              />
            </div>

            <!-- Server Response -->
            <div v-if="serverResponse" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <p class="text-red-700 text-sm-medium text-center">
                {{ serverResponse }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Contact Info (Optional) -->
      <div class="w-full max-w-2xl mx-auto text-center pt-8">
        <div class="bg-pie-50 border border-pie-200 rounded-2xl p-6 md:p-8">
          <h3 class="text-xl-bold text-pie-700 mb-4">
            {{ t('info.contact.additional_help.title') }}
          </h3>
          <p class="text-base-medium text-slate-600 leading-relaxed">
            {{ t('info.contact.additional_help.description') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
