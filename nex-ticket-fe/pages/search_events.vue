<script lang="ts" setup>
import type NexCity from '@/models/NexCity'
import type { FunctionalComponent } from 'vue'
import { useCityInfo } from '@/composables/cityInfo'
import NexEvent from '@/models/NexEvent'
import NexTagCategory from '@/models/NexTagCategory'
import { formatISO, isValid } from 'date-fns'
import { AllBookmarkIcon, StarIcon } from 'hugeicons-vue'
import { useToast } from 'vue-toastification'

const { city, pageLoading } = storeToRefs(useGeneralStore())
const cityInfo = ref<NexCity | null>(null)
const { loadInfo: loadCityInfo } = useCityInfo()

const feedback = useFeedback()

const error = ref<string>('')
const eventsGrouped = ref<deSerGroupedEventData>([])
const choosenCityValue = ref<number>()
const categFilterOpned = ref<boolean>()
const dateFilterOpned = ref<boolean>()
const priceFilterOpned = ref<boolean>()
const sortOpened = ref<boolean>()
const { t } = useI18n()

const savedEventsContainer = ref<HTMLElement | null>(null)
const isInitialLoad = ref(true)
const previousSavedState = ref(false)
const shouldAnimate = ref(false)

const sortOptions = ref<{ value: string, direction: 'ASC' | 'DESC' | '', icon: FunctionalComponent | null, sort: string }>({
  value: '',
  direction: '',
  icon: null,
  sort: '',
})

const dateFilterRange = ref<[Date | null, Date | null] | null>(null)
const priceFilerMin = ref<number | undefined>()
const priceFilerMax = ref<number | undefined>()
const selectedGenreIds = ref<number[]>([])
const filterOptions = ref<{ name: string, id: number }[]>([])

watch(() => eventsGrouped.value.some(event => event.Events.length > 0 && event.G_KEY === 'saved_events'), (hasSaved) => {
  if (!isInitialLoad.value && hasSaved !== previousSavedState.value) {
    shouldAnimate.value = true
  }
  else {
    shouldAnimate.value = false
  }

  previousSavedState.value = hasSaved

  if (isInitialLoad.value && !pageLoading.value) {
    isInitialLoad.value = false
  }

  if (savedEventsContainer.value) {
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        if (savedEventsContainer.value) {
          if (hasSaved) {
            const eventGroup = savedEventsContainer.value.querySelector('.event-group-component')
            if (eventGroup) {
              void savedEventsContainer.value.offsetHeight
              const height = eventGroup.getBoundingClientRect().height
              savedEventsContainer.value.style.minHeight = `${height}px`
            }
            else {
              savedEventsContainer.value.style.minHeight = 'auto'
            }
          }
          else {
            savedEventsContainer.value.style.minHeight = '0'
          }
        }
      })
    })
  }
}, { immediate: true })

function handleFilterChange() {
  const query: Record<string, any> = {}

  if (selectedGenreIds.value.length > 0) {
    query.tags = JSON.stringify(selectedGenreIds.value)
  }

  if (dateFilterRange.value) {
    const [minDate, maxDate] = dateFilterRange.value
    if (minDate && isValid(minDate)) {
      query.min_date = formatISO(minDate, { representation: 'date' })
    }
    if (maxDate && isValid(maxDate)) {
      query.max_date = formatISO(maxDate, { representation: 'date' })
    }
  }

  if (priceFilerMin.value !== undefined) {
    query.min_price = String(priceFilerMin.value)
  }

  if (priceFilerMax.value !== undefined) {
    query.max_price = String(priceFilerMax.value)
  }

  if (sortOptions.value.sort && sortOptions.value.direction) {
    query.sortField = sortOptions.value.sort
    query.direction = sortOptions.value.direction
  }

  if (choosenCityValue.value) {
    query.city = String(choosenCityValue.value)
  }

  navigateToWLocale({ path: '/events/', query })
}

async function loadGenreFilterOptions() {
  try {
    const { data, error: fetchError } = await useAPI<any>('/api/public/tag_categories/by_name/music_gen')
    if (fetchError.value) {
      errorToast(`Failed to load genre filter options: %{fetchError.value}`)
      filterOptions.value = []
      return
    }

    if (data.value) {
      const tagCategory = NexTagCategory.create_from_request(data.value) as NexTagCategory
      if (tagCategory && tagCategory.tags) {
        filterOptions.value = tagCategory.tags.map(tag => ({
          name: tag.name,
          id: tag.id,
        }))
      }
      else {
        filterOptions.value = []
      }
    }
  }
  catch (err: any) {
    console.error('Unexpected error loading genre filter options:', err)
    error.value = `Error loading genres: ${err.message}`
    filterOptions.value = []
  }
}

// dummy data
const utils = useGroupedEventsDummyData()

type eventData = Array<{ G_KEY: string, Name: string, Events: Array<any> }>

onMounted(async () => {
  choosenCityValue.value = await loadDefaultCity()
  await loadGenreFilterOptions()
  await loadData()
  cityInfo.value = await loadCityInfo(choosenCityValue.value)
})

async function reloadEvent(eventId: number) {
  try {
    const hasSavedEventsBefore = eventsGrouped.value.some(event =>
      event.G_KEY === 'saved_events' && event.Events.length > 0,
    )

    const { data, error: fetchError } = await useAPI(`/api/public/events/${eventId}`)

    if (fetchError.value) {
      feedback.error(t('errors.event_errors.fetch_saved_event_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
      return
    }

    if (data.value) {
      const reloadedEvent = NexEvent.create_from_request(data.value) as NexEvent

      const savedEvent = eventsGrouped.value.find(event => event.G_KEY === 'saved_events')

      if (savedEvent) {
        if (reloadedEvent.saved) {
          savedEvent.Events.push(reloadedEvent)
        }
        else {
          savedEvent.Events = savedEvent.Events.filter(event => event.id !== eventId)
        }
      }

      eventsGrouped.value.forEach((group) => {
        const index = group.Events.findIndex(obj => obj.id === eventId)
        if (index !== -1) {
          group.Events[index] = reloadedEvent
        }
      })

      const hasSavedEventsAfter = eventsGrouped.value.some(event =>
        event.G_KEY === 'saved_events' && event.Events.length > 0,
      )

      isInitialLoad.value = false

      if (hasSavedEventsBefore !== hasSavedEventsAfter) {
        shouldAnimate.value = true
      }
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message
  }
}

async function loadData() {
  try {
    pageLoading.value = true
    const { data: fetchedData, error: fetchError } = await useAPI(`/api/public/grouped_events`, { query: { city: choosenCityValue.value } })
    if (fetchError.value) {
      feedback.error(t('errors.event_errors.fetch_grouped_event_data_error'), { level: 'error', rollbar: true, extras: fetchError.value })
      error.value = fetchError.value.message
    }
    else {
      // TODO: fix types
      // @ts-expect-error I dont want to define the types for this shit
      processFetchedData(fetchedData.value?.groups as eventData)
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
    error.value = err.message
  }
  finally {
    pageLoading.value = false
  }
}

function processFetchedData(data: eventData) {
  eventsGrouped.value = []
  data.forEach((item) => {
    eventsGrouped.value.push({ G_KEY: item.G_KEY, Name: item.Name, Events: NexEvent.create_from_request(item.Events) as NexEvent[] })
  })

  const hasSavedEvents = eventsGrouped.value.some(event =>
    event.G_KEY === 'saved_events' && event.Events.length > 0,
  )
  previousSavedState.value = hasSavedEvents

  if (isInitialLoad.value) {
    shouldAnimate.value = false
  }
}

async function loadDefaultCity(): Promise<number> {
  if (city.value != null) {
    return Number.parseInt(city.value)
  }
  else {
    const { data, error: fetchError } = await useAPI('/api/cities/current')

    if (fetchError.value) {
      useToast().error('Failed to load default city')
      error.value = fetchError.value.message
      return 765 // TODO Preset Warsaw as default city for now, fix later
    }
    else {
      return data.value?.id
    }
  }
  return 765 // TODO Preset Warsaw as default city for now, fix later
}

watch(choosenCityValue, async () => {
  city.value = choosenCityValue.value ? choosenCityValue.value.toString() : city.value
  loadData()
  cityInfo.value = await loadCityInfo(choosenCityValue.value)
})

watch(categFilterOpned, (newValue) => {
  if (newValue) {
    dateFilterOpned.value = false
    priceFilterOpned.value = false
    sortOpened.value = false
  }
})
watch(dateFilterOpned, (newValue) => {
  if (newValue) {
    categFilterOpned.value = false
    priceFilterOpned.value = false
    sortOpened.value = false
  }
})
watch(priceFilterOpned, (newValue) => {
  if (newValue) {
    dateFilterOpned.value = false
    categFilterOpned.value = false
    sortOpened.value = false
  }
})
watch(sortOpened, (newValue) => {
  if (newValue) {
    categFilterOpned.value = false
    dateFilterOpned.value = false
    priceFilterOpned.value = false
  }
})

function redirectForSeeAll(G_KEY: string) {
  navigateToWLocale({ path: '/events/', query: { G_KEY } })
}
</script>

<template>
  <div class="flex flex-col mx-auto px-8 pt-12 gap-16 lg:pt-0 max-w-[80rem]">
    <div class="md:gap-16 gap-9 flex flex-col">
      <div class="md:gap-4 gap-3 md:flex md:flex-col">
        <div>
          <CityAutocomplete
            v-model="choosenCityValue"
            label-key="public.index.dropdown.title"
            variant="underlined"
          />
        </div>
        <div class="md:py-3 w-full flex md:flex-wrap gap-3  overflow-x-scroll scrollbar-not-display md:overflow-visible">
          <PopoverSortPanel
            v-model:sort-options="sortOptions"
            class="md:z-30 flex-shrink-0"
            @apply-sort="handleFilterChange"
          />
          <PopoverGenresFilterPanel
            v-model:selected-ids="selectedGenreIds"
            :options="filterOptions"
            filter-name-key="public.index.filters.filterNames.categories"
            class="md:z-30 flex-shrink-0"
            @apply="handleFilterChange"
          />
          <PopoverDateFilterPanel
            v-model="dateFilterRange"
            filter-name-key="public.index.filters.filterNames.date"
            :min-date="new Date()"
            placeholder="Select dates"
            class="md:z-30 flex-shrink-0"
            @apply="handleFilterChange"
          />
          <PopoverPriceFilterPanel
            v-model:first-value="priceFilerMin"
            v-model:second-value="priceFilerMax"
            filter-name-key="public.index.filters.filterNames.price"
            any-price-text-key="public.index.filters.price_filter.any_price"
            free-text-key="public.index.filters.price_filter.free"
            :min-range="0"
            :max-range="100"
            :currency-code="cityInfo !== null ? cityInfo.state.currency_code : 'EUR'"
            class="md:z-30 flex-shrink-0"
            @apply-filter="handleFilterChange"
          />
        </div>
      </div>
    </div>
    <ErrorPublic v-if="error" class="mt-4" :error="error" />
    <div>
      <!-- main content -->
      <div v-if="!pageLoading" class="md:py-4 gap-3 flex flex-col mb-3">
        <div class="event-groups-container flex flex-col">
          <div ref="savedEventsContainer" class="saved-events-container">
            <transition name="fade-slide" :disabled="!shouldAnimate">
              <EventGroup
                v-if="eventsGrouped.some(event => event.Events.length > 0 && event.G_KEY === 'saved_events')"
                :events-grouped="eventsGrouped.filter(event => event.Events.length > 0 && event.G_KEY === 'saved_events')"
                title-color="text-slate-100"
                :title="$t('public.event_info.saved_events')"
                :add-all-events-button="false"
                :icon="AllBookmarkIcon"
                bg-color="bg-slate-700"
                class="event-group-component"
                @saved-triggered="reloadEvent"
              />
            </transition>
          </div>
          <div>
            <EventGroup
              :events-grouped="eventsGrouped.filter(event => event.Events.length > 0 && event.G_KEY === 'recommended')"
              :icon="StarIcon"
              title-color="text-pie-950"
              :title="$t('public.event_info.popular_events')"
              :add-all-events-button="false"
              bg-color="bg-pie-100"
              @saved-triggered="reloadEvent"
            />
          </div>

          <div class="md:max-w-[1280px] md:min-h-[1098px] lex flex-col">
            <div
              class="md:min-h-[64rem] flex flex-col"
            >
              <div>
                <EventGroup
                  :events-grouped="eventsGrouped.filter(event => event.Events.length > 0 && event.G_KEY !== 'saved_events' && event.G_KEY !== 'recommended')"
                  :add-all-events-button="true"
                  title-color="text-slate-800"
                  bg-color="bg-slate-200"
                  @see-all="redirectForSeeAll"
                  @saved-triggered="reloadEvent"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- placeholder for pageLoading -->
      <div v-else class="md:py-4 gap-3 flex flex-col">
        <div class="gap-6 flex flex-col">
          <div>
            <EventGroup
              :events-grouped="utils.groupedEventsDummyData1"
              :icon="StarIcon"
              title-color="text-pie-950"
              :title="$t('public.event_info.popular_events')"
              :add-all-events-button="false"
              bg-color="bg-pie-100"
              @saved-triggered="reloadEvent"
            />
          </div>
          <div
            class="md:max-w-[80rem] md:min-h-[64rem]
                  flex flex-col"
          >
            <div>
              <EventGroup
                :events-grouped="utils.groupedEventsDummyData2"
                :add-all-events-button="true"
                title-color="text-slate-800"
                bg-color="bg-slate-200"
                @see-all="redirectForSeeAll"
                @saved-triggered="reloadEvent"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.scrollbar-not-display {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-not-display::-webkit-scrollbar {
  display: none;
}

.saved-events-container {
  min-height: 0;
  transition: min-height 0.25s cubic-bezier(0.0, 0.0, 0.2, 1);
  will-change: min-height;
}

.fade-slide-enter-active {
  transition: opacity 0.25s cubic-bezier(0.0, 0.0, 0.2, 1),
              transform 0.25s cubic-bezier(0.0, 0.0, 0.2, 1),
              max-height 0.25s cubic-bezier(0.0, 0.0, 0.2, 1);
  max-height: 370px;
  opacity: 1;
  transform: translateY(0) translateZ(0);
  will-change: transform, opacity, max-height;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

.fade-slide-leave-active {
  transition: opacity 0.25s cubic-bezier(0.4, 0.0, 0.2, 1),
              transform 0.25s cubic-bezier(0.4, 0.0, 0.2, 1),
              max-height 0.25s cubic-bezier(0.4, 0.0, 0.2, 1);
  max-height: 370px;
  opacity: 1;
  transform: translateY(0) translateZ(0);
  will-change: transform, opacity, max-height;
  backface-visibility: hidden;
  perspective: 1000px;
}

.fade-slide-enter-from {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px) translateZ(0);
  overflow: hidden;
}

.fade-slide-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px) translateZ(0);
  overflow: hidden;
}

.event-groups-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  transition: gap 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: gap;
}
</style>
