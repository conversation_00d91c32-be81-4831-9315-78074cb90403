{"name": "nex-ticket-fe", "type": "module", "private": true, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321", "scripts": {"preinstall": "npx only-allow pnpm", "build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "pnpm lint:eslint && pnpm lint:prettier", "lint:eslint": "eslint .", "lint:prettier": "prettier . --check", "lintfix": "eslint . --fix && prettier --write --list-different ."}, "dependencies": {"@mdi/font": "^7.4.47", "@nuxt/scripts": "^0.11.2", "@nuxtjs/i18n": "^9.3.4", "@pinia/nuxt": "0.10.1", "@stripe/stripe-js": "^5.10.0", "@vee-validate/nuxt": "^4.15.0", "@vee-validate/yup": "^4.15.0", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^13.1.0", "@vueuse/nuxt": "13.0.0", "chart.js": "^4.4.8", "corepack": "^0.31.0", "date-fns": "^4.1.0", "hugeicons-vue": "^0.0.1", "nuxt": "^3.16.1", "pinia": "^3.0.1", "rollbar": "^2.26.4", "vue": "latest", "vue-advanced-cropper": "^2.8.9", "vue-chartjs": "^5.3.2", "vue-router": "latest", "vue-slider-component": "^3.2.24", "vue-toastification": "2.0.0-rc.5", "vue3-slider": "^1.10.1", "vuetify": "^3.7.18", "vuetify-nuxt-module": "0.18.3", "yup": "^1.6.1", "yup-password": "^0.4.0"}, "devDependencies": {"@antfu/eslint-config": "^3.16.0", "@eslint/js": "^9.23.0", "@nuxt/devtools": "^2.3.1", "@nuxt/eslint": "^0.7.6", "@nuxtjs/tailwindcss": "^6.13.2", "@types/jsonapi-serializer": "^3.6.8", "@typescript-eslint/parser": "^8.27.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "eslint": "^9.23.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.33.0", "globals": "^15.15.0", "prettier": "^3.5.3", "prettier-eslint": "^16.3.0", "sass-embedded": "^1.86.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0", "vue-eslint-parser": "^9.4.3"}}