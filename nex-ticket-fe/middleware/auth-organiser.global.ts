export default defineNuxtRouteMiddleware(async (to) => {
  if (isOrganiserPath(to.path, to.meta.layout)) {
    const authStore = useAuthStore(useNuxtApp().$pinia)

    if (authStore.loading)
      await authStore.checkAuth()

    if (!authStore.isAuthenticated || !authStore.user?.user_type || !['organiser', 'admin'].includes(authStore.user?.user_type)) {
      return navigateToWLocale('/login')
    }
  }
})
