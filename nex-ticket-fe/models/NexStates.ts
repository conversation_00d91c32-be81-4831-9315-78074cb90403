import NexModel from '@/models/NexModel'
import { Deserializer } from '@/utils/deserialiser'

export default class NexState extends NexModel {
  constructor(
    id: number,
    public name: string,
    public iso_alpha_2: string,
    public iso_alpha_3: string,
    public iso_numeric: string,
    public calling_code: string,
    public currency_code: string,
    public currency_name: string,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexState | Array<NexState> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexState.create_instances(data)

    return NexState.create_instance(data)
  }

  static create_instance(state: any): NexState {
    return new NexState(
      state.id,
      state.name,
      state.iso_alpha_2,
      state.iso_alpha_3,
      state.iso_numeric,
      state.calling_code,
      state.currency_code,
      state.currency_name,
    )
  }

  static create_instances(states: Array<any>): Array<NexState> {
    return states.map(state => NexState.create_instance(state))
  }
}
