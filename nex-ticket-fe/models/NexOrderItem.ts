import NexModel from '@/models/NexModel'

export default class NexOrderItem extends NexModel {
  constructor(
    id: number,
    public quantity: number,
    public name: string,
    public order_itemable_type: string,
    public order_itemable_id: number,
    public organiser_price_per_piece: number,
    public platform_price_per_piece: number,
    public price_per_piece: number,
    public total_price: number,
  ) {
    super(id)
  }

  static create_instance(item: any): NexOrderItem {
    return new NexOrderItem(
      item.id,
      item.quantity,
      item.name,
      item.order_itemable_type,
      item.order_itemable_id,
      item.organiser_price_per_piece,
      item.platform_price_per_piece,
      item.price_per_piece,
      item.total_price,
    )
  }

  static create_instances(items: Array<any>): Array<NexOrderItem> {
    return items.map(item => NexOrderItem.create_instance(item))
  }
}
