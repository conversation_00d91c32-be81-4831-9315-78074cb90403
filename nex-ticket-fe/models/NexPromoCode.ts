import NexModel from '@/models/NexModel'
import { Deserializer } from '@/utils/deserialiser'

export default class NexPromoCode extends NexModel {
  constructor(
    id: number,
    public code: string,
    public description: string | null,
    public discount_type: string,
    public discount_value: number,
    public valid_from: string | null,
    public valid_until: string | null,
    public max_uses: number | null,
    public uses_count: number,
    public max_uses_per_user: number | null,
    public min_order_amount: number | null,
    public active: boolean,
    public applicable_event_ids: number[],
    public applicable_ticket_type_ids: number[],
    public organiser_id: number,
    public created_at: string,
    public updated_at: string,
    public disabled: boolean,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexPromoCode | Array<NexPromoCode> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexPromoCode.create_instances(data)

    return NexPromoCode.create_instance(data)
  }

  static create_instance(promoData: any): NexPromoCode {
    return new NexPromoCode(
      Number(promoData.id),
      promoData.code,
      promoData.description ?? null,
      promoData.discount_type,
      Number(promoData.discount_value ?? 0),
      promoData.valid_from ?? null,
      promoData.valid_until ?? null,
      promoData.max_uses ? Number(promoData.max_uses) : null,
      Number(promoData.uses_count ?? 0),
      promoData.max_uses_per_user ? Number(promoData.max_uses_per_user) : null,
      promoData.min_order_amount ? Number(promoData.min_order_amount) : null,
      promoData.active ?? false,
      Array.isArray(promoData.applicable_event_ids) ? promoData.applicable_event_ids.map(Number) : [],
      Array.isArray(promoData.applicable_ticket_type_ids) ? promoData.applicable_ticket_type_ids.map(Number) : [],
      Number(promoData.organiser_id),
      promoData.created_at,
      promoData.updated_at,
      promoData.disabled,
    )
  }

  static create_instances(promoDataArray: Array<any>): Array<NexPromoCode> {
    return promoDataArray.map(promoData => NexPromoCode.create_instance(promoData))
  }
}
