import NexModel from '@/models/NexModel'
import stringToColor from '@/utils/stringToColor'

export default class NexTag extends NexModel {
  public bgColor: string
  public textColor: string

  constructor(
    id: number,
    public name: string,
  ) {
    super(id)
    const { bgColor, textColor } = stringToColor(id + name)
    this.bgColor = bgColor
    this.textColor = textColor
  }

  static create_instance(tag: any): NexTag {
    return new NexTag(tag.id, tag.name)
  }

  static create_instances(tags: Array<any>): Array<NexTag> {
    return tags.map(tag => NexTag.create_instance(tag))
  }
}
