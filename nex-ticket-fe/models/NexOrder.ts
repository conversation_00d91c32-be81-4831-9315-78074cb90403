import NexModel from '@/models/NexModel'
import NexOrderItem from '@/models/NexOrderItem'
import { Deserializer } from '@/utils/deserialiser'

export default class NexOrder extends NexModel {
  constructor(
    id: number,
    public order_id: string,
    public external_payment_gate_id: string,
    public external_payment_gate_type: string,
    public customer_id: number,
    public status: string,
    public order_items: NexOrderItem[],
    public first_name: string,
    public last_name: string,
    public email: string,
    public event_id: number,
    public vat_percentage: number,
    public vat_value: number,
    public total: number,
    public currency_code: string,
    public subtotal_without_vat: number,
    public promo_discount_amount: number,
    public promocode: string,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexOrder | Array<NexOrder> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexOrder.create_instances(data)

    return NexOrder.create_instance(data)
  }

  static create_instance(order: any): NexOrder {
    const order_items = order.order_items
      ? NexOrderItem.create_instances(order.order_items)
      : [] // Fallback to empty array

    return new NexOrder(
      order.id,
      order.order_id,
      order.external_payment_gate_id,
      order.external_payment_gate_type,
      order.customer_id,
      order.status,
      order_items,
      order.first_name,
      order.last_name,
      order.email,
      order.event_id,
      order.vat_percentage,
      order.vat_value,
      order.total,
      order.currency_code,
      order.subtotal_without_vat,
      order.promo_discount_amount,
      order.promocode,
    )
  }

  static create_instances(orders: Array<any>): Array<NexOrder> {
    return orders.map(order => NexOrder.create_instance(order))
  }

  get formattedVatPercentage(): string {
    return formatToDynamicDecimal(this.vat_percentage)
  }
}
