import NexModel from '@/models/NexModel'
import NexTag from '@/models/NexTag'
import { Deserializer } from '@/utils/deserialiser'

export default class NexTagCategory extends NexModel {
  constructor(
    id: number,
    public name: string,
    public tags: NexTag[],

  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexTagCategory | Array<NexTagCategory> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexTagCategory.create_instances(data)

    return NexTagCategory.create_instance(data)
  }

  static create_instance(tag_category: any): NexTagCategory {
    const tags = tag_category.tags
      ? NexTag.create_instances(tag_category.tags)
      : []

    return new NexTagCategory(
      tag_category.id,
      tag_category.name,
      tags,
    )
  }

  static create_instances(TagCategories: Array<any>): Array<NexTagCategory> {
    return TagCategories.map(tag_category => NexTagCategory.create_instance(tag_category))
  }
}
