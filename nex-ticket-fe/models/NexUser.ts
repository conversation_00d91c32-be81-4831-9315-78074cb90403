interface RegistrationData {
  user: {
    email: string
    first_name: string
    last_name: string
    password: string
    type: 'OrganiserAccount' | 'Customer' | 'ScannerAccount'
    organiser_attributes?: {
      name: string
      contact_email: string
      contact_mobile: string
      reg_number: string
      vat_number: string
      state_id: string
      default_currency?: string
    }
  }
}

export type { RegistrationData }
