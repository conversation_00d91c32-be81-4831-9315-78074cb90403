import NexModel from '@/models/NexModel'

export default class NexBasketItem extends NexModel {
  constructor(
    id: number,
    public quantity: number,
    public itemable_type: string,
    public itemable_id: number,
    public price: string,
  ) {
    super(id)
  }

  static create_instance(item: any): NexBasketItem {
    return new NexBasketItem(
      item.id,
      item.quantity,
      item.itemable_type,
      item.itemable_id,
      item.price,
    )
  }

  static create_instances(items: Array<any>): Array<NexBasketItem> {
    return items.map(item => NexBasketItem.create_instance(item))
  }
}
