import NexModel from '@/models/NexModel'

interface NexDiscount {
  start_date: Date
  end_date: Date
  percentage: number
}

export interface SelectedTicket {
  quantity: number
  ticket: NexTicketType
}

export default class NexTicketType extends NexModel {
  constructor(
    id: number,
    public max_amount: number,
    public available_amount: number,
    public price: number,
    public description: string,
    public ticket_category: string,
    public name: string,
    public discounted_price?: number,
    public discounts?: NexDiscount[],
    public disabled?: boolean,
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexTicketType | Array<NexTicketType> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexTicketType.create_instances(data)

    return NexTicketType.create_instance(data)
  }

  static create_instance(ticket: any): NexTicketType {
    return new NexTicketType(
      ticket.id,
      ticket.max_amount,
      ticket.available_amount,
      Number.parseFloat(ticket.price),
      ticket.description,
      ticket.ticket_category,
      ticket.name,
      Number.parseFloat(ticket.discounted_price),
      ticket.discounts,
      ticket.disabled,
    )
  }

  static create_instances(tickets: Array<any>): Array<NexTicketType> {
    return tickets.map(ticket => NexTicketType.create_instance(ticket))
  }

  static create_discounts(discounts: Array<any> | undefined): Array<NexDiscount> {
    if (discounts === undefined)
      return []
    return discounts.map((discount: any) => ({
      ...discount,
      start_date: NexModel.convertToDate(discount.start_date),
      end_date: NexModel.convertToDate(discount.end_date),
    }))
  }

  get isAvailable() {
    return true
    // return this.available_amount > 0
  }
}
