import NexBasketItem from '@/models/NexBasketItem'
import NexModel from '@/models/NexModel'
import { Deserializer } from '@/utils/deserialiser'

export default class NexBasket extends NexModel {
  constructor(
    id: number,
    public expires_at: Date,
    public basket_items: NexBasketItem[],
  ) {
    super(id)
  }

  public static create_from_request(requestData: any): NexBasket | Array<NexBasket> {
    const deserializer = new Deserializer()
    const data = deserializer.deserialize(requestData) as any

    if (Array.isArray(data))
      return NexBasket.create_instances(data)

    return NexBasket.create_instance(data)
  }

  static create_instance(basket: any): NexBasket {
    // Fix: Handle missing basket_items and ensure array type
    const basket_items = basket.basket_items
      ? NexBasketItem.create_instances(basket.basket_items)
      : [] // Fallback to empty array

    return new NexBasket(
      basket.id,
      basket.expires_at,
      basket_items,
    )
  }

  static create_instances(baskets: Array<any>): Array<NexBasket> {
    return baskets.map(basket => NexBasket.create_instance(basket))
  }
}
