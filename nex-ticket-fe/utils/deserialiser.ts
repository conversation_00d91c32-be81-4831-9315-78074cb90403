interface JSONAPIResource {
  id: string
  type: string
  attributes: { [key: string]: any }
  relationships?: { [key: string]: { data: JSONAPIResource | JSONAPIResource[] } }
}

interface JSONAPIResponse {
  data: JSONAPIResource | JSONAPIResource[]
  included?: JSONAPIResource[]
}

interface DeserializedResource {
  id: string
  type: string
  [key: string]: any
}

export class Deserializer {
  constructor() {
  }

  private deserializeResource(resource: JSONAPIResource, included: JSONAPIResource[]): DeserializedResource {
    const deserialized: DeserializedResource = {
      id: resource.id,
      type: resource.type,
      ...resource.attributes,
    }

    if (resource.relationships) {
      for (const [key, relationship] of Object.entries(resource.relationships)) {
        if (Array.isArray(relationship.data)) {
          deserialized[key] = relationship.data.map(rel => this.findIncludedResource(rel, included))
        }
        else {
          deserialized[key] = this.findIncludedResource(relationship.data, included)
        }
      }
    }

    return deserialized
  }

  private findIncludedResource(resource: JSONAPIResource, included: JSONAPIResource[]): DeserializedResource | string {
    const includedResource = included.find(
      includedItem => includedItem.id === resource.id && includedItem.type === resource.type,
    )
    return includedResource ? this.deserializeResource(includedResource, included) : resource.id
  }

  public deserialize(jsonApiResponse: JSONAPIResponse): DeserializedResource | DeserializedResource[] {
    const { data, included = [] } = jsonApiResponse

    if (Array.isArray(data)) {
      return data.map(resource => this.deserializeResource(resource, included))
    }
    else {
      return this.deserializeResource(data, included)
    }
  }
}
