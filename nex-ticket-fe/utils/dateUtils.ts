import { format } from 'date-fns'

export function formatDateToDate(date: Date): string {
  return format(date, 'EEE, dd MMM yyyy')
}

export function formatDateToTime(date: Date): string {
  return format(date, 'HH:mm')
}

export function toLocalISOString(date: Date): string {
  const offsetMs = date.getTimezoneOffset() * 60 * 1000
  const localTime = new Date(date.getTime() - offsetMs)
  return localTime.toISOString().slice(0, 16) // Format: YYYY-MM-DDTHH:mm
}

/**
 * Converts a datetime-local string (YYYY-MM-DDTHH:mm) to a Date object
 * Assumes the input is in local timezone
 */
export function fromLocalISOString(dateTimeLocalString: string): Date {
  if (!dateTimeLocalString) {
    throw new Error('Invalid datetime-local string provided')
  }

  // datetime-local format is YYYY-MM-DDTHH:mm
  // We need to treat this as local time, not UTC
  return new Date(dateTimeLocalString)
}

/**
 * Safely converts a date (Date object or string) to local ISO string format
 * Returns empty string if date is invalid or null/undefined
 */
export function safeToLocalISOString(date: Date | string | null | undefined): string {
  if (!date) {
    return ''
  }

  try {
    // If it's already a string in ISO format for datetime-local input, return as is
    if (typeof date === 'string') {
      // Check if it's already in the format YYYY-MM-DDTHH:mm (datetime-local format)
      const datetimeLocalRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/
      if (datetimeLocalRegex.test(date)) {
        return date
      }

      // Try to parse the string as a date
      const dateObj = new Date(date)

      // Check if the date is valid
      if (Number.isNaN(dateObj.getTime())) {
        console.warn('Invalid date string provided to safeToLocalISOString:', date)
        return ''
      }

      return toLocalISOString(dateObj)
    }

    // If it's a Date object, check if it's valid
    if (Number.isNaN(date.getTime())) {
      console.warn('Invalid Date object provided to safeToLocalISOString:', date)
      return ''
    }

    return toLocalISOString(date)
  }
  catch (error) {
    console.error('Error converting date to local ISO string:', error, date)
    return ''
  }
}
