interface AuthStates {
  unauthenticated: 'unauthenticated'
  loading: 'loading'
  authenticated: 'authenticated'
}

interface CookieNames {
  search_city: 'search_city'
}

interface Constants {
  auth: AuthStates
  cookies: <PERSON>ieNames
}

interface I18nTranslation {
  linked: (key: string) => string
  named: (key: string) => string
}

const constants: Constants = {
  auth: {
    unauthenticated: 'unauthenticated',
    loading: 'loading',
    authenticated: 'authenticated',
  },
  cookies: {
    search_city: 'search_city',
  },
}

export default constants

export type { I18nTranslation }
