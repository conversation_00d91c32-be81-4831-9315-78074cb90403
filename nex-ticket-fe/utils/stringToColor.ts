/**
 * Generates a HEX background color and a contrasting text color
 * from the provided string.
 *
 * @param inputString - The input string used to generate the color.
 * @returns An object containing the background color (e.g. "#a1b2c3")
 *          and text color (either "#ffffff" or "#000000").
 */
export default (inputString: string): { bgColor: string, textColor: string } => {
  // 1) Convert the input string into a 24-bit integer (hash).
  let hash = 0
  for (let i = 0; i < inputString.length; i++) {
    hash = inputString.charCodeAt(i) + ((hash << 5) - hash)
    // Ensures hash doesn’t overflow the 32-bit integer limit.
    hash |= 0
  }

  // 2) Convert that integer to an RGB hex code (ensure it’s 6 digits).
  let hexColor = (hash & 0x00FFFFFF).toString(16).toUpperCase()
  // Pad with leading zeros if necessary (to ensure 6 digits).
  hexColor = '000000'.substring(0, 6 - hexColor.length) + hexColor

  // 3) Determine if this color is "light" or "dark" to pick the text color.
  const r = Number.parseInt(hexColor.substring(0, 2), 16)
  const g = Number.parseInt(hexColor.substring(2, 4), 16)
  const b = Number.parseInt(hexColor.substring(4, 6), 16)

  // A simple contrast formula:
  const brightness = (r + g + b) / (128 * 3)

  // If brightness is high, choose black text; otherwise white text.
  const textColor = brightness > 1 ? '#000000' : '#FFFFFF'
  return {
    bgColor: `#${hexColor}`,
    textColor,
  }
}
