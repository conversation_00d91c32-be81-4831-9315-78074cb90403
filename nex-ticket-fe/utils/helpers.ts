import type NexEvent from '@/models/NexEvent'
import type { NavigationFailure, RouteLocationRaw } from 'vue-router'
import { useToast } from 'vue-toastification'

export function isOrganiserPath(path: string, layout: string | boolean | undefined): boolean {
  return /^\/organiser|^\/[a-z]{2}\/organiser/.test(path) && layout !== 'auth'
}

export function navigateToWLocale(routeParams: RouteLocationRaw): Promise<void | NavigationFailure | false> | false | void | RouteLocationRaw {
  const localeRoute = useLocaleRoute()
  const route = localeRoute(routeParams)
  if (route) {
    return navigateTo(route.fullPath)
  }
}

/**
 * Funcion to encapsulate toast, rollbar and console logging.
 * @name errorToast
 * @function
 * @param {string} message Message to be shown to user and logged
 * @param {string} system_level 'error' - level logged to rollbar and console
 */
export function errorToast(message: string, system_level: 'error' | 'warning' = 'error'): void {
  const toast = useToast()
  const rollbar = useRollbar()

  toast.error(message)
  switch (system_level) {
    case 'error':
      rollbar.error(message)
      console.error(message)
      break
    case 'warning':
      rollbar.warning(message)
      console.warn(message)
      break
  }
}

export function infoToast(message: string): void {
  const toast = useToast()
  toast.info(message)
}

export function warningToast(message: string): void {
  const toast = useToast()
  toast.warning(message)
}

export function formatCurrency(amount: number, currency: string = 'EUR') {
  const rollbar = useRollbar()
  const currencyLocaleMap: Record<string, string> = {
    PLN: 'pl-PL',
    USD: 'en-US',
    EUR: 'de-DE',
    ALL: 'sq-AL',
    AMD: 'hy-AM',
    AZN: 'az-AZ',
    BYN: 'be-BY',
    BAM: 'bs-BA',
    BGN: 'bg-BG',
    HRK: 'hr-HR',
    CZK: 'cs-CZ',
    DKK: 'da-DK',
    GEL: 'ka-GE',
    HUF: 'hu-HU',
    ISK: 'is-IS',
    KZT: 'kk-KZ',
    MDL: 'en-MD',
    MKD: 'mk-MK',
    NOK: 'nn-NO',
    RON: 'ro-RO',
    RUB: 'ru-RU',
    RSD: 'sr-RS',
    SEK: 'sv-SE',
    CHF: 'de-CH',
    TRY: 'tr-TR',
    UAH: 'uk-UA',
    GPB: 'en-GB',
    // Add other currencies as needed
  }
  let locale = currencyLocaleMap[currency]
  if (!locale) {
    rollbar.critical(`Undefined currency in formatting method ${currency}`)
    locale = 'en-US'
  }
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount)
}

export interface LocalImageType {
  isLoaded: boolean
  uploadFailed: boolean
  rawFile?: string
  url?: string
  file?: File
  key?: string
}

export function getCloudIcon(image: LocalImageType): string {
  return image.isLoaded ? 'mdi-cloud-check-variant' : image.uploadFailed ? 'mdi-cloud-alert' : 'mdi-cloud-upload'
}

export function formatOrderDate(date: Date): string {
  return new Date(date).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  })
}

export function formatPrice(value: string | Date | number): string {
  const num = Number.parseFloat(String(value))
  if (Number.isNaN(num)) {
    return '0.00'
  }
  return num.toFixed(2)
}

export function getRandomMagnitude(from: number, range: number): number {
  return Math.floor(Math.random() * range) + from
}

export function formatToDynamicDecimal(value: string | number): string {
  const num = Number(value)
  if (Number.isNaN(num))
    return '0.00'
  return num % 1 === 0 ? num.toString() : num.toFixed(2)
}

export function capitalize(value: string): string {
  return value.replace(/(?:^|\s|-)\S/g, x => x.toUpperCase())
}

export function trim_date(date: Date): Date {
  return date.toString().length > 16 ? new Date(date.toISOString().slice(0, 16)) : date
}

function getStyleSpec(path: string, specification: string | null): string {
  if (specification == null)
    return path
  return `${specification}.${path}`
}

function getStylesFromKey(fullKey: string): string {
  const keys = fullKey.replace(/\s/g, '').split(';')
  const styles = []
  for (const key of keys) {
    const style = styleMap[key]
    if (style)
      styles.push(style)
  }
  return styles.join(';')
}

export function getCustomStyle(path: string, specification: string | null = null): string {
  const spec = getStyleSpec(path, specification)
  const static_key = styleMapStatic[spec]
  if (static_key !== undefined) {
    return getStylesFromKey(static_key)
  }

  for (const { pattern, key } of styleMapDynamic) {
    if (pattern.test(spec)) {
      return getStylesFromKey(key)
    }
  }

  return ''
}

export function formatDate(
  dateString: string | Date | null | undefined,
  locale: string | undefined = undefined,
  options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' },
): string {
  if (!dateString) {
    return ''
  }
  try {
    if (locale === undefined) {
      const { locale: userLocale } = useI18n()
      locale = userLocale.value
    }
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString
    if (Number.isNaN(date.getTime())) {
      return String(dateString)
    }
    return new Intl.DateTimeFormat(locale, options).format(date)
  }
  catch (error) {
    useFeedback().error(`Failed to format date: ${dateString} ${error}`)
    // errorToast(`Failed to format date: ${dateString} ${error}`)
    return String(dateString)
  }
}

export type deSerGroupedEventData = Array<{ G_KEY: string, Name: string, Events: NexEvent[] }>
