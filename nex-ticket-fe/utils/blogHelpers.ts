import type { BlogPost } from '~/data/blogPosts'

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
}

/**
 * Estimate reading time based on content length
 * Average reading speed: 200 words per minute
 */
export function estimateReadingTime(content: string): number {
  // Remove HTML tags for word count
  const textContent = content.replace(/<[^>]*>/g, '')
  const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length
  const readingTime = Math.ceil(wordCount / 200)
  return Math.max(1, readingTime) // Minimum 1 minute
}

/**
 * Generate a blog post template
 */
export function generateBlogPostTemplate(
  title: string,
  author: string = 'TicketPie Team',
  category: BlogPost['category'] = 'guides'
): Partial<BlogPost> {
  const slug = generateSlug(title)
  const now = new Date()
  
  return {
    title,
    excerpt: 'Add a compelling excerpt that summarizes your blog post and entices readers to continue reading.',
    content: `
      <h2>Introduction</h2>
      <p>Start your blog post with an engaging introduction that hooks the reader and clearly states what they'll learn.</p>
      
      <h2>Main Content Section</h2>
      <p>Break your content into logical sections with clear headings. Use paragraphs, lists, and other HTML elements to make your content easy to read.</p>
      
      <ul>
        <li>Use bullet points for lists</li>
        <li>Keep paragraphs concise</li>
        <li>Include practical examples</li>
      </ul>
      
      <h2>Conclusion</h2>
      <p>Summarize the key points and provide a clear call-to-action or next steps for your readers.</p>
    `,
    author,
    publishedAt: now,
    category,
    slug,
    featuredImage: '/api/placeholder/800/400',
    readTime: 5, // Will be updated based on actual content
    tags: ['add', 'relevant', 'tags'],
    featured: false,
    published: false, // Start as draft
  }
}

/**
 * Validate blog post data
 */
export function validateBlogPost(post: Partial<BlogPost>): string[] {
  const errors: string[] = []
  
  if (!post.title || post.title.trim().length === 0) {
    errors.push('Title is required')
  }
  
  if (!post.excerpt || post.excerpt.trim().length === 0) {
    errors.push('Excerpt is required')
  }
  
  if (!post.content || post.content.trim().length === 0) {
    errors.push('Content is required')
  }
  
  if (!post.author || post.author.trim().length === 0) {
    errors.push('Author is required')
  }
  
  if (!post.slug || post.slug.trim().length === 0) {
    errors.push('Slug is required')
  } else if (!/^[a-z0-9-]+$/.test(post.slug)) {
    errors.push('Slug must contain only lowercase letters, numbers, and hyphens')
  }
  
  if (!post.category || !['events', 'tips', 'updates', 'guides'].includes(post.category)) {
    errors.push('Category must be one of: events, tips, updates, guides')
  }
  
  if (!post.tags || !Array.isArray(post.tags) || post.tags.length === 0) {
    errors.push('At least one tag is required')
  }
  
  if (!post.featuredImage || post.featuredImage.trim().length === 0) {
    errors.push('Featured image is required')
  }
  
  return errors
}

/**
 * Format date for display
 */
export function formatBlogDate(date: Date, locale: string = 'en-US'): string {
  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

/**
 * Get category display name
 */
export function getCategoryDisplayName(category: BlogPost['category']): string {
  const categoryNames = {
    events: 'Events',
    tips: 'Tips & Tricks',
    updates: 'Platform Updates',
    guides: 'Guides',
  }
  return categoryNames[category] || category
}

/**
 * Get category color classes
 */
export function getCategoryColorClasses(category: BlogPost['category']): string {
  const colors = {
    events: 'bg-blue-100 text-blue-800',
    tips: 'bg-green-100 text-green-800',
    updates: 'bg-purple-100 text-purple-800',
    guides: 'bg-orange-100 text-orange-800',
  }
  return colors[category] || 'bg-slate-100 text-slate-800'
}

/**
 * Extract plain text from HTML content for search
 */
export function extractTextFromHTML(html: string): string {
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
}

/**
 * Search blog posts
 */
export function searchBlogPosts(
  posts: BlogPost[],
  query: string,
  category?: string
): BlogPost[] {
  let filteredPosts = posts.filter(post => post.published)
  
  // Filter by category
  if (category && category !== 'all') {
    filteredPosts = filteredPosts.filter(post => post.category === category)
  }
  
  // Filter by search query
  if (query.trim()) {
    const searchTerm = query.toLowerCase()
    filteredPosts = filteredPosts.filter(post => {
      const searchableText = [
        post.title,
        post.excerpt,
        extractTextFromHTML(post.content),
        post.author,
        ...post.tags,
      ].join(' ').toLowerCase()
      
      return searchableText.includes(searchTerm)
    })
  }
  
  // Sort by published date (newest first)
  return filteredPosts.sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime())
}

/**
 * Get blog post statistics
 */
export function getBlogStats(posts: BlogPost[]) {
  const publishedPosts = posts.filter(post => post.published)
  const draftPosts = posts.filter(post => !post.published)
  const featuredPosts = posts.filter(post => post.featured && post.published)
  
  const categoryCounts = publishedPosts.reduce((acc, post) => {
    acc[post.category] = (acc[post.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const totalReadTime = publishedPosts.reduce((total, post) => total + post.readTime, 0)
  
  return {
    total: posts.length,
    published: publishedPosts.length,
    drafts: draftPosts.length,
    featured: featuredPosts.length,
    categoryCounts,
    totalReadTime,
    averageReadTime: publishedPosts.length > 0 ? Math.round(totalReadTime / publishedPosts.length) : 0,
  }
}
