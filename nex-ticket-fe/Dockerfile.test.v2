ARG NODE_VERSION=23.9.0

FROM node:${NODE_VERSION}-slim

WORKDIR /app

RUN apt-get update && \
    apt-get install -y curl --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

COPY .output ./

COPY ../scripts/entrypoint.sh /run/custom_entrypoint.sh

RUN chmod +x /run/custom_entrypoint.sh

# Define environment variables, but let them be overridden at runtime
ENV HOST=0.0.0.0 NODE_ENV=production
ENV NODE_ENV=production

ENTRYPOINT ["/run/custom_entrypoint.sh"]

CMD ["node","/app/server/index.mjs"]
