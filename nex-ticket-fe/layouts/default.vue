<script setup lang="ts">
import { Menu01Icon, Search01Icon, UserSharingIcon } from 'hugeicons-vue'

const route = useRoute()
const path = ref<string>(route.path)
const header = ref<HTMLElement | null>(null)
const isMobile = ref<boolean>(false)
const mobileHeaderStyle = ref<string>(getCustomStyle(path.value, 'mobile.header'))
const mobileHeaderBTNStyle = ref<string>(getCustomStyle(path.value, 'mobile.headerbtn'))

const isMenuOpen = ref<boolean>(false)
const isSearchOpen = ref<boolean>(false)

function checkMobileVersion(): void {
  const mobileDiv = header.value?.querySelector('.mobile-version')

  if (mobileDiv == null)
    return

  const displayStyle = window.getComputedStyle(mobileDiv).display
  isMobile.value = (displayStyle !== 'none')
}

watch(
  () => route.path,
  (newPath, _) => {
    path.value = newPath
    mobileHeaderStyle.value = getCustomStyle(path.value, 'mobile.header')
    mobileHeaderBTNStyle.value = getCustomStyle(path.value, 'mobile.headerbtn')
  },
)

onMounted(() => {
  window.addEventListener('resize', checkMobileVersion)
  checkMobileVersion()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkMobileVersion)
})

async function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value
}
</script>

<template>
  <div
    class="flex flex-col min-h-screen
                md:flex md:flex-col lg:min-h-screen bg-pie-25"
  >
    <div class="lg:min-h-screen flex flex-col">
      <!-- Header -->
      <header
        ref="header"
        class="bg-slate-50 border-b border-slate-300 shadow-sm w-full h-[4.5rem] flex items-center px-4
             md:bg-slate-50 md:border-slate-300 md:shadow-sm md:w-auto md:h-auto md:flex-row md:px-0"
        :style="isMobile ? mobileHeaderStyle : ''"
      >
        <!-- Desktop -->
        <div class="hidden justify-between items-center w-full h-full lg:flex lg:max-w-7xl lg:py-6 lg:m-auto lg:gap-6 md:px-2">
          <NuxtLinkLocale to="/" class="md:w-auto md:h-auto">
            <TicketPieLogo class="md:scale-100" />
          </NuxtLinkLocale>

          <nav class="md:w-full md:flex md:flex-row md:items-center md:justify-center md:gap-6 ">
            <NavSearchBar />
            <NexButton to="/search_events" text-key="nav.events" variant="secondary" class="md:inline-flex transition-basic" />
            <NexButton to="/registration" text-key="nav.for_organisers" variant="secondary" class="md:inline-flex transition-basic" />
            <NexButton to="/login" text-key="nav.login" :append-icon="UserSharingIcon" class="md:inline-flex transition-basic" />
            <NavLanguageSwitcher />
          </nav>
        </div>

        <!-- Mobile -->
        <div class="mobile-version lg:hidden flex justify-between items-center w-screen h-full py-3">
          <button class="transition-basic w-14 h-full place-items-center z-10">
            <Menu01Icon class="text-pie-950 stroke-2" :style="mobileHeaderBTNStyle" />
          </button>

          <NuxtLinkLocale to="/" class="w-auto h-auto z-10">
            <TicketPieLogo class="scale-75 md:scale-100" :is-mobile="true" />
          </NuxtLinkLocale>

          <button class="transition-basic w-14 h-full  place-items-center z-10" @click="isSearchOpen = !isSearchOpen">
            <Search01Icon class="text-pie-950 stroke-2" :style="mobileHeaderBTNStyle" />
          </button>
        </div>
      </header>

      <!-- Mobile Menu -->
      <div v-if="isMenuOpen" class=" z-50 flex md:hidden">
        <nav class="flex flex-row gap-5 px-3 text-pie-950 text-base-medium">
          <NexButton to="/search_events" text-key="nav.events" variant="secondary" class="transition-basic " @click="toggleMenu()" />
          <NexButton to="/registration" text-key="nav.for_organisers" variant="secondary" class="transition-basic" @click="toggleMenu()" />
          <NexButton to="/login" text-key="nav.login" variant="secondary" class="transition-basic" @click="toggleMenu()" />
          <NavLanguageSwitcher />
        </nav>
      </div>

      <!-- Mobile SearchBar -->
      <div v-if="isSearchOpen" class="md:hidden">
        <NavSearchBar />
      </div>

      <!-- Main Content -->
      <main class="lg:flex-grow lg:container lg:mx-auto justify-items-center md:pt-8 lg:max-w-[105rem] md:max-w-[80rem]">
        <slot />
      </main>
    </div>
    <footer>
      <FooterNexPublicFooter />
    </footer>
  </div>
</template>
