<script lang="ts" setup>
import { useFavicon, usePreferredDark } from '@vueuse/core'

function setFavicon() {
  const isDark = usePreferredDark()
  const favicon = computed(() => !isDark.value ? 'favicon-light.ico' : 'favicon-dark.ico')

  useFavicon(favicon, {
    rel: 'icon',
  })
}
const { locale } = useI18n()
type supportedLocalesType = 'en' | 'sk' | 'de' | 'pl'

onMounted(async () => {
  setFavicon()
  const supportedLocales = ['en', 'sk', 'de', 'pl']
  locale.value = supportedLocales.includes(useBrowserLocale() || '') ? useBrowserLocale() as supportedLocalesType : 'en'
})
</script>

<template>
  <div>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>
