import NexEvent from '@/models/NexEvent'
import { defineStore } from 'pinia'

export const useSingleEventStore = defineStore('single_event', () => {
  const event = ref<NexEvent | null>(null)
  const currentEventId = ref<number | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const feedback = useFeedback()

  function setEvent(newEvent: NexEvent) {
    event.value = newEvent
    currentEventId.value = newEvent.id
  }

  async function fetchEvent(eventId: number, isOrganiser: boolean) {
    try {
      loading.value = true
      const path = isOrganiser ? `/api/organiser/events/${eventId}` : `/api/public/events/${eventId}`
      const { data: fetchedData, error: fetchError } = await useAPI(
        path,
        { query: { includes: 'ticket_types,tags,organiser' } },
      )

      if (fetchError.value) {
        error.value = fetchError.value.message
        return
      }

      const newEvent = NexEvent.create_from_request(fetchedData.value) as NexEvent
      setEvent(newEvent)
    }
    catch (err: unknown) {
      feedback.error('Failed to load event', err)
    }
    finally {
      loading.value = false
    }
  }

  async function getEvent(eventId: number, forceReload = false, isOrganiser = false): Promise<NexEvent | null> {
    if (!forceReload && currentEventId.value === eventId && event.value !== null) {
      return event.value
    }

    await fetchEvent(eventId, isOrganiser)
    return event.value
  }

  return {
    event,
    currentEventId,
    loading,
    error,
    getEvent,
  }
})
