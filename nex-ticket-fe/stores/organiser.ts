import NexOrganiser from '@/models/NexOrganiser'
import { defineStore } from 'pinia'

export const useOrganiserStore = defineStore('organiser', () => {
  const organiser = ref<NexOrganiser | null>(null)

  async function setOrganiser() {
    const nuxtApp = useNuxtApp()
    try {
      const response = await nuxtApp.$api('/api/organiser/show')
      organiser.value = NexOrganiser.create_from_request(response) as NexOrganiser
    }
    catch (error: any) {
      nuxtApp.$toast.error(error.response.error)
      return false
    }
  }

  return {
    organiser,
    setOrganiser,
  }
})
