import { acceptHMRUpdate, defineS<PERSON> } from 'pinia'

interface User {
  id: number
  email: string
  first_name: string
  last_name: string
  user_type: 'customer' | 'organiser' | 'admin'
}

interface DefaultResponse {
  status: {
    code: number
    message: string
  }
  data: {
    user: User
  }
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(true)
  const organiserStore = useOrganiserStore()

  function setUser(newUser: User) {
    user.value = newUser
    if (user.value.user_type === 'organiser') {
      organiserStore.setOrganiser()
    }
    isAuthenticated.value = true
  }

  function setToken(newToken: string) {
    token.value = newToken
    localStorage.setItem('auth.token', newToken)
  }

  function clearAuth() {
    user.value = null
    token.value = null
    isAuthenticated.value = false
    localStorage.removeItem('auth.token')
  }

  async function login(email: string, password: string, redirectTo = '/'): Promise<boolean> {
    clearAuth()
    const nuxtApp = useNuxtApp()
    try {
      const response: DefaultResponse = await nuxtApp.$api('/api/login', {
        method: 'POST',
        body: { user: { email, password } },
      })

      setUser(response.data.user)
      nuxtApp.$toast.success(response.status.message)
      navigateToWLocale(redirectTo)
      return true
    }
    catch (error: any) {
      return false
    }
  }

  async function signup(data: object, redirectTo = '/'): Promise<boolean> {
    clearAuth()
    const nuxtApp = useNuxtApp()
    try {
      const response: DefaultResponse = await nuxtApp.$api('/api/signup', {
        method: 'POST',
        body: data,
      })

      setUser(response.data.user)
      nuxtApp.$toast.success(response.status.message)
      navigateToWLocale(redirectTo)
      return true
    }
    catch (error: any) {
      nuxtApp.$toast.error(error.response.error)
      return false
    }
  }

  async function logout() {
    const nuxtApp = useNuxtApp()

    try {
      const tokenValue = localStorage.getItem('auth.token')
      if (!tokenValue)
        return
      await nuxtApp.$api('/api/logout', {
        method: 'DELETE',
        headers: {
          Authorization: tokenValue,
        },
      })
    }
    finally {
      clearAuth()
      await navigateToWLocale('/')
    }
  }

  async function checkAuth() {
    const nuxtApp = useNuxtApp()
    try {
      const tokenValue = localStorage.getItem('auth.token')
      if (!tokenValue)
        return false

      const response: User = await nuxtApp.$api('/api/current_user', {
        headers: {
          Authorization: tokenValue,
        },
      })

      setToken(tokenValue)
      setUser(response)
      return true
    }
    catch {
      clearAuth()
      return false
    }
    finally {
      loading.value = false
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    loading,
    setUser,
    setToken,
    clearAuth,
    login,
    signup,
    logout,
    checkAuth,
  }
})
