/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './components/**/*.{js,vue,ts}',
    './layouts/**/*.vue',
    './pages/**/*.vue',
    './plugins/**/*.{js,ts}',
    './nuxt.config.{js,ts}',
  ],
  safelist: [
    'px-9',
    'py-6',
    'py-5',
    'px-7',
    {
      pattern: /^(bg|border|text)-(pie|slate)-(10|25|50|100|200|300|400|500|600|700|800|900|950|1000|1100)$/,
      variants: ['hover'],
    },
  ],
  theme: {
    extend: {
      colors: {
        pie: {
          10: '#FDF6F6',
          25: '#F5F5FB',
          50: '#F3F2FE',
          100: '#E9E8FD',
          200: '#D5D3FC',
          300: '#B5B1FA',
          400: '#9085F7',
          500: '#6A56F6',
          600: '#5D3FF4',
          700: '#492AE0',
          800: '#3D23BB',
          900: '#331D9A',
          950: '#1C0F69',
          1000: '#0D0A4B',
          1100: '#05052D',
        },
      },
      fontFamily: {
        onest: ['Onest', 'serif'],
        sofia: ['Sofia Sans Condensed', 'serif'],
      },
    },
  },
}
