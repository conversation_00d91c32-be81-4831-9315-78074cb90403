/* onest-100 - cyrillic_cyrillic-ext_latin_latin-ext */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 100;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-100.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-200 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 200;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-200.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-300 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 300;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-300.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-regular - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-500 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 500;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-500.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-600 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 600;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-600.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-700 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-800 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 800;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-800.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* onest-900 - cyrillic_cyrillic-ext_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Onest';
    font-style: normal;
    font-weight: 900;
    src: url('../fonts/onest-v8-cyrillic_cyrillic-ext_latin_latin-ext-900.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }

  /* sofia-sans-condensed-100 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 100;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-100.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-100italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 100;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-100italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-200 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 200;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-200.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-200italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 200;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-200italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-300 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 300;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-300.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-300italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 300;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-300italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-regular - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 400;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-500 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 500;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-500.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-500italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 500;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-500italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-600 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 600;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-600.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-600italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 600;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-600italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-700 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-700italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 700;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-700italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-800 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 800;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-800.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-800italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 800;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-800italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-900 - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: normal;
    font-weight: 900;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-900.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  
  /* sofia-sans-condensed-900italic - cyrillic_cyrillic-ext_greek_latin_latin-ext */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Sofia Sans Condensed';
    font-style: italic;
    font-weight: 900;
    src: url('../fonts/sofia-sans-condensed-v2-cyrillic_cyrillic-ext_greek_latin_latin-ext-900italic.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  