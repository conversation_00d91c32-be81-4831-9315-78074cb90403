@import url('./fonts.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Texts */

@layer utilities {

    /* Transition Buttons */
    .transition-basic {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 150ms;
    }

    .transition-75 {
        transition-property: all;
        transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
        transition-duration: 75ms;
    }

    .placeholder {
        background-color: rgb(144 133 247 / var(--tw-text-opacity, 1)) /* pie-400  */;
        border-radius: 0.25rem; /* base */
        min-height: 1rem; /* h-4 */
    }
    
    .placeholder-circle {
        aspect-ratio: 1 / 1;
        background-color: rgb(144 133 247 / var(--tw-text-opacity, 1)) /* pie-400 */;
        border-radius: 9999px; /* full */
        min-height: 1.25rem; /* h-5 */
    }
    
    .placeholder-circle-slate-100 {
        aspect-ratio: 1 / 1;
        background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1)); /* #f1f5f9 / slate-100 */
        border-radius: 9999px; /* full */
        min-height: 1.25rem; /* h-5 */
    }


    /* xs */
    .text-xs-normal {
        font-size: 0.75rem; /* 12px */
        line-height: 1rem; /* 16px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-xs-medium {
        font-size: 0.75rem;
        line-height: 1rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-xs-semibold {
        font-size: 0.75rem;
        line-height: 1rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-xs-bold {
        font-size: 0.75rem;
        line-height: 1rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-xs-extrabold {
        font-size: 0.75rem;
        line-height: 1rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
    
    /* sm */
    .text-sm-normal {
        font-size: 0.875rem; /* 14px */
        line-height: 1.25rem; /* 20px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-sm-medium {
        font-size: 0.875rem;
        line-height: 1.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-sm-semibold {
        font-size: 0.875rem;
        line-height: 1.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-sm-bold {
        font-size: 0.875rem;
        line-height: 1.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-sm-extrabold {
        font-size: 0.875rem;
        line-height: 1.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
    
    /* base */
    .text-base-normal {
        font-size: 1rem; /* 16px */
        line-height: 1.5rem; /* 24px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-base-medium {
        font-size: 1rem; 
        line-height: 1.5rem; 
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-base-semibold {
        font-size: 1rem; 
        line-height: 1.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-base-bold {
        font-size: 1rem; 
        line-height: 1.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-base-extrabold {
        font-size: 1rem;
        line-height: 1.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
    
    /* lg */
    .text-lg-normal {
        font-size: 1.125rem; /* 18px */
        line-height: 1.75rem; /* 28px*/
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-lg-medium {
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-lg-semibold {
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-lg-bold {
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-lg-extrabold {
        font-size: 1.125rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
    
    /* xl */
    .text-xl-normal {
        font-size: 1.25rem; /* 20px */
        line-height: 1.75rem; /* 28px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-xl-medium {
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-xl-semibold {
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-xl-bold {
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-xl-extrabold {
        font-size: 1.25rem;
        line-height: 1.75rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
    
    /* 2xl */
    .text-2xl-normal {
        font-size: 1.5rem; /* 24px */
        line-height: 2rem; /* 32px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-2xl-medium {
        font-size: 1.5rem;
        line-height: 2rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-2xl-semibold {
        font-size: 1.5rem;
        line-height: 2rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-2xl-bold {
        font-size: 1.5rem;
        line-height: 2rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-2xl-extrabold {
        font-size: 1.5rem;
        line-height: 2rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
    
    /* 3xl */
    .text-3xl-normal {
        font-size: 1.875rem; /* 30px */
        line-height: 2.25rem; /* 36px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-3xl-medium {
        font-size: 1.875rem;
        line-height: 2.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-3xl-semibold {
        font-size: 1.875rem;
        line-height: 2.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-3xl-bold {
        font-size: 1.875rem;
        line-height: 2.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-3xl-extrabold {
        font-size: 1.875rem;
        line-height: 2.25rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }

    /* 4xl */
    .text-4xl-normal {
        font-size: 2.25rem; /* 36px */
        line-height: 2.5rem; /* 40px */
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-4xl-medium {
        font-size: 2.25rem;
        line-height: 2.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-4xl-semibold {
        font-size: 2.25rem;
        line-height: 2.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-4xl-bold {
        font-size: 2.25rem;
        line-height: 2.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-4xl-extrabold {
        font-size: 2.25rem;
        line-height: 2.5rem;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }

    /* 5xl */
    .text-5xl-normal {
        font-size: 3rem; /* 48px */
        line-height: 1; 
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-5xl-medium {
        font-size: 3rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-5xl-semibold {
        font-size: 3rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-5xl-bold {
        font-size: 3rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-5xl-extrabold {
        font-size: 3rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }

    /* 6xl */
    .text-6xl-normal {
        font-size: 3.75rem; /* 60px */
        line-height: 1; 
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-6xl-medium {
        font-size: 3.75rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-6xl-semibold {
        font-size: 3.75rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-6xl-bold {
        font-size: 3.75rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-6xl-extrabold {
        font-size: 3.75rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }

    /* 7xl */
    .text-7xl-normal {
        font-size: 4.5rem; /* 72px */
        line-height: 1; 
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-7xl-medium {
        font-size: 4.5rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-7xl-semibold {
        font-size: 4.5rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-7xl-bold {
        font-size: 4.5rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-7xl-extrabold {
        font-size: 4.5rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }

    /* 8xl */
    .text-8xl-normal {
        font-size: 6rem; /* 96px */
        line-height: 1; 
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-8xl-medium {
        font-size: 6rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-8xl-bold {
        font-size: 6rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-8xl-semibold {
        font-size: 6rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-8xl-extrabold {
        font-size: 6rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }

    /* 9xl */
    .text-9xl-normal {
        font-size: 8rem; /* 128px */
        line-height: 1; 
        font-family: 'Onest', sans-serif;
        font-weight: 400;
    }
    .text-9xl-medium {
        font-size: 8rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 500;
    }
    .text-9xl-semibold {
        font-size: 8rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 600;
    }
    .text-9xl-bold {
        font-size: 8rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 700;
    }
    .text-9xl-extrabold {
        font-size: 8rem;
        line-height: 1;
        font-family: 'Onest', sans-serif;
        font-weight: 800;
    }
}

@tailwind variants;