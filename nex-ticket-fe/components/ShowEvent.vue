<script lang="ts" setup>
import type NexEvent from '@/models/NexEvent'
import ShowEventDetailsGridBG from '@/assets/images/backgrounds/ShowEventDetailsGridBG.svg'
import defaultImage from '@/assets/images/def_promoter_profile_photo.jpg'
import { ArrowRight01Icon, ArrowTurnBackwardIcon, LinkSquare02Icon, Mail01Icon } from 'hugeicons-vue'

const props = defineProps<{
  isOrganiser?: boolean
}>()

const { t } = useI18n()

const route = useRoute()
const error = ref<string>('')
const event = ref<NexEvent | null>(null)
const showDeleteDialog = ref(false)
const isDeleting = ref(false)
const deleteError = ref<string>('')
const singleEventStore = useSingleEventStore()
const { loading } = storeToRefs(singleEventStore)
const showContactForm = ref(false)
const mapUrl = computed(() => {
  return `https://www.google.com/maps?q=${event.value?.latitude},${event.value?.longitude}`
})
const profilePictureSrc = computed(() => {
  return event.value?.organiser?.profile_picture?.url || defaultImage
})

onMounted(async () => {
  event.value = await singleEventStore.getEvent(Number(route.params.id), true, props.isOrganiser)
})

const ticketPrices = computed(() =>
  (event.value?.ticket_types ?? [])
    .filter(t => typeof t !== 'number')
    .map(t => Number(t.discounted_price))
    .filter(price => !Number.isNaN(price)) as number[],
)

const ticketVariantsCount = computed(() =>
  (event.value?.ticket_types ?? [])
    .filter(t => typeof t !== 'number')
    .length,
)

async function reloadEvent() {
  event.value = await singleEventStore.getEvent(Number(route.params.id), true, props.isOrganiser)
}

async function deleteEvent() {
  try {
    isDeleting.value = true
    deleteError.value = ''

    const { error: apiDeleteError } = await useAPI(
      `/api/organiser/events/${route.params.id}`,
      {
        method: 'DELETE',
      },
    )

    if (apiDeleteError.value) {
      throw new Error(apiDeleteError.value.message || t('errors/event_errors/delete_event_fail'))
    }

    navigateToWLocale('/organiser/events')
  }
  catch (err: unknown) {
    deleteError.value = err instanceof Error ? err.message : t('errors/event_errors/delete_event_fail')
  }
  finally {
    isDeleting.value = false
  }
}

function navigateToTickets() {
  navigateToWLocale(`/events/${event.value?.id}/tickets`)
}

function isEventEditable(): boolean {
  if (event.value && event.value.start_time > new Date()) {
    return true
  }
  return false
}

async function toggleDisableEvent() {
  await useAPI(`/api/organiser/events/${event.value?.id}`, {
    method: 'PUT',
    body: {
      disabled: !event.value?.disabled,
    },
  })
  reloadEvent()
}
</script>

<template>
  <v-dialog v-model="showDeleteDialog" max-width="500">
    <v-card>
      <v-card-title class="lg:text-h5">
        {{ $t('organiser.delete_event.text.confirm_del') }}
      </v-card-title>
      <v-card-text>
        {{ $t('organiser.delete_event.text.sec_confirm_del') }}
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="showDeleteDialog = false"
        >
          {{ $t('organiser.delete_event.buttons.cancel') }}
        </v-btn>
        <v-btn
          color="error"
          variant="text"
          :loading="isDeleting"
          @click="deleteEvent"
        >
          {{ $t('organiser.delete_event.buttons.delete') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <div class="lg:mx-auto max-w-[80rem]">
    <!-- Loading State -->
    <div v-if="loading" class="flex flex-col md:w-[1280px] gap-8 md:py-8">
      <!-- Header Section -->
      <div
        class=" md:h-[32rem] md:rounded-2xl md:mt-0 md:p-12
          animate-pulse relative -mt-[4.5rem] overflow-hidden p-4 border border-slate-600/50"
      >
        <!-- Header Content -->
        <div class="grid grid-cols-1 h-full w-full content-between mt-20 md:mt-0 gap-12">
          <!-- Top Buttons -->
          <div>
            <!-- Back Navigation Link -->
            <div class="flex flex-row z-10 w-full justify-between">
              <div class="placeholder m-2 h-8 w-40 animate-pulse" />
            </div>

            <!-- Organiser Buttons -->
            <div
              v-if="props.isOrganiser"
              class="md:absolute md:top-14 md:right-10 md:z-20 md:flex md:items-center md:gap-4"
            >
              <!-- Edit Button -->
              <div class="placeholder animate-pulse" />

              <!-- Event Deletion -->
              <div class="placeholder animate-pulse" />

              <!-- Event Disable -->
              <div class="placeholder animate-pulse" />
            </div>
          </div>

          <!-- Tags and Name of event -->
          <div class="relative z-10  flex flex-col gap-4">
            <div class="flex flex-wrap gap-2 md:gap-4">
              <!-- placeholder -->
            </div>
            <div class="text-4xl-extrabold text-slate-50 md:text-6xl-extrabold">
              <div class="placeholder animate-pulse h-14 w-2/5" />
            </div>
          </div>
        </div>
      </div>

      <!-- Details Grid -->
      <div class="flex flex-col md:grid md:grid-cols-[2fr_1fr] md:gap-6">
        <div class="flex flex-col overflow-hidden md:self-start gap-8 px-4 md:px-0">
          <div class="flex flex-col md:flex md:flex-row overflow-hidden bg-white rounded-2xl border border-slate-400 relative">
            <!-- Decorative background element -->
            <div class="z-0 inset-0 absolute">
              <img :src="ShowEventDetailsGridBG" alt="Example party photos" class="h-full w-full">
            </div>
            <div class="flex flex-col md:grid md:grid-cols-2 gap-6 py-5 px-4 z-10">
              <!-- Date Column -->
              <div class="space-y-1 z-10">
                <div class="text-slate-500 tracking-wide text-base-medium">
                  {{ $t('public.event_info.date') }}
                </div>
                <div class="md:space-y-1 flex flex-col gap-5">
                  <div class="placeholder animate-pulse h-7 md:w-96 w-80" />
                  <div class="placeholder animate-pulse h-6 w-1/5" />
                </div>
              </div>

              <!-- Venue Column -->
              <div class="space-y-1 z-10 flex flex-col">
                <div class="text-slate-500 tracking-wide text-base-medium">
                  {{ $t('public.event_info.venue.venue_text') }}
                </div>
                <div class="flex flex-col gap-1">
                  <div class="placeholder animate-pulse h-7 w-48" />
                  <div class="placeholder animate-pulse h-5 w-56" />
                  <div class="placeholder animate-pulse h-6 w-36" />
                </div>
              </div>
            </div>
          </div>
          <!-- Gallery -->
          <section class="md:space-y-4">
            <div class="text-base-medium text-slate-500">
              {{ $t('public.event_info.gallery') }}
            </div>
            <div class="md:relative">
              <div class="placeholder animate-pulse md:h-96 h-48 w-full" />
            </div>
          </section>

          <!-- Description -->
          <section class="space-y-4 ">
            <div class="text-base-medium text-slate-500">
              {{ $t('public.event_info.description') }}
            </div>
            <div class="placeholder animate-pulse h-6 w-1/2" />
          </section>

          <!-- Policies and Contact Promoter Section -->
          <div class="flex flex-col gap-9  md:flex md:flex-row md:gap-6 md:mt-6 ">
            <!-- Contact Promoter Card -->
            <div class="flex flex-col items-start p-5 gap-6 w-full md:min-h-52 bg-pie-100 rounded-2xl">
              <div class="flex flex-col gap-4">
                <!-- Headline Promoter -->
                <p class="text-base-medium text-slate-700">
                  {{ $t('public.event_info.promoter') }}
                </p>

                <!-- Profil promoter -->
                <div class="flex items-center justify-between w-full">
                  <div class="flex items-center gap-3">
                    <!-- Placeholder image -->
                    <div class="w-12 h-12">
                      <img
                        :src="profilePictureSrc"
                        class="rounded-full object-cover w-full h-full"
                        alt="Profile Picture"
                      >
                    </div>

                    <!-- Placeholder name  -->
                    <NuxtLinkLocale
                      to="/promoter_info"
                      class="text-base-medium md:text-xl-medium text-slate-900 underline "
                    >
                      <!-- {{ $t('public.event_info.promoter_name') }} -->
                      {{ event?.organiser?.name }}
                    </NuxtLinkLocale>
                  </div>
                  <ArrowRight01Icon class="text-slate-900 stroke" />
                </div>
              </div>
              <button
                class="p-1 gap-3 bg-pie-700 rounded-lg hover:bg-pie-600 w-full
                   md:flex md:p-1 md:gap-3 md:bg-pie-700 md:rounded-lg md:hover:bg-pie-600 md:justify-center md:items-center md:w-full"
                @click="showContactForm = true"
              >
                <div
                  class="border border-white rounded-lg py-2
                     md:border md:border-white md:rounded-lg md:px-12 md:place-items-center md:w-full"
                >
                  <div
                    class=" bg-transparent text-white text-lg-bold
                    md:bg-transparent md:text-slate-100 md:text-xl-bold md:text-center md:w-full md:text-nowrap"
                  >
                    <div class="flex flex-row place-items-center gap-2 justify-center">
                      <Mail01Icon />
                      {{ $t('public.event_info.buttons.contact_promoter') }}
                    </div>
                  </div>
                </div>
              </button>
            </div>

            <!-- Venue Policies Card -->
            <div class="flex flex-col md:items-start p-5 gap-5 w-full md:min-h-52 bg-pie-100 rounded-2xl">
              <div class="text-slate-700 text-base-medium">
                {{ $t('public.event_info.venue.policies') }}
              </div>
              <div class="flex flex-col gap-2">
                <div v-for="index in 2" :key="index">
                  <div class="flex flex-col gap-1">
                    <div class="placeholder animate-pulse h-4 md:h-5 w-40" />
                    <div class="placeholder animate-pulse h-4 md:h-5 w-80" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tickets Section -->
        <div
          class="flex flex-row sticky w-full bottom-0 z-20 bg-white border-t border-slate-900 h-auto px-6 py-3 gap-6 mt-8
                  md:flex md:flex-col md:rounded-2xl md:border md:border-slate-400 md:self-start md:sticky md:p-6 md:top-32 md:mt-0"
        >
          <div>
            <!-- Price Range -->
            <div class="flex flex-col gap-3">
              <div class="placeholder animate-pulse md:h-7 h-5 w-60" />
              <div class="placeholder animate-pulse md:h-5 h-3 w-40" />
            </div>
          </div>

          <!-- Tickets Button -->
          <button
            :disabled="props.isOrganiser"
            class="p-1 gap-3 bg-pie-700 rounded-lg hover:bg-pie-600 w-full
                   md:p-1 md:gap-3 md:bg-pie-700 md:rounded-lg md:hover:bg-pie-600"
          >
            <div
              class="flex border border-white rounded-lg w-full h-full content-center py-2
                     md:border md:border-white md:rounded-lg md:px-12"
            >
              <div
                class="w-full h-full text-slate-100 text-nowrap text-base-bold md:text-xl-bold content-center"
              >
                {{ $t('public.ticket.get_tickets') }}
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="lg:text-red-500 lg:p-4 lg:text-center">
      {{ error }}
    </div>

    <!-- Event Content -->
    <div v-else-if="event" class="flex flex-col lg:max-w-[80rem] gap-8 lg:py-8">
      <!-- Header Section -->
      <div class="relative -mt-[4.5rem] lg:h-[32rem] lg:rounded-2xl overflow-hidden lg:mt-0 p-4 lg:p-12">
        <img
          :src="event.main_photo?.url"
          class="absolute h-full w-full lg:inset-0 top-0 lg:w-full lg:h-full lg:object-cover blur-[20px]"
        >
        <div class="absolute inset-0 top-0 bg-pie-700/50" />

        <!-- Header Content -->
        <div class="grid grid-cols-1 h-full w-full content-between mt-20 lg:mt-0 gap-12">
          <!-- Top Buttons -->
          <div class="flex flex-col gap-12 lg:flex-row lg:justify-between lg:items-center">
            <!-- Back Navigation Link -->
            <div class="flex flex-row z-10 w-full justify-between">
              <NuxtLinkLocale
                :to="props.isOrganiser ? '/organiser/events' : '/events'"
                class="h-fit flex flex-row text-slate-50 z-10 gap-2 border-b-2 border-slate-50 self-center hover:text-slate-300 hover:border-slate-300 transition-basic"
              >
                <ArrowTurnBackwardIcon class="stroke-2" />
                <p class="text-xl-bold ">
                  {{ $t('public.event_info.see_all_events') }}
                </p>
              </NuxtLinkLocale>
              <SaveEventButton
                v-if="!props.isOrganiser"
                :event-id="event.id"
                :saved="event.saved"
                border-size="54"
                icon-size="24"
                class="z-10"
                @reload-saved-events="reloadEvent()"
              />
            </div>
            <!-- Organiser Buttons -->
            <div
              v-if="props.isOrganiser"
              class="z-20 flex justify-between lg:absolute lg:top-14 lg:right-10  lg:items-center lg:gap-4"
            >
              <!-- Edit Button -->
              <NuxtLinkLocale v-if="isEventEditable()" :to="`/organiser/events/${event.id}/edit`">
                <v-btn
                  color="#1E293B"
                  outlined
                >
                  {{ $t('organiser.edit_event.buttons.edit_event') }}
                </v-btn>
              </NuxtLinkLocale>

              <v-btn
                v-else
                color="#1E293B"
                outlined
                :disabled="!isEventEditable()"
              >
                {{ $t('organiser.edit_event.buttons.edit_event') }}
              </v-btn>

              <v-btn
                color="#1E293B"
                outlined
                @click="toggleDisableEvent()"
              >
                {{ event.disabled ? t('common.enable') : t('common.disable') }}
              </v-btn>

              <!-- Event Deletion -->
              <v-btn
                color="error"
                outlined
                @click="showDeleteDialog = true"
              >
                <v-icon left>
                  mdi-delete
                </v-icon>
                {{ $t('organiser.delete_event.buttons.delete_event') }}
              </v-btn>
            </div>
          </div>

          <!-- Tags and Name of event -->
          <div class="relative z-10  flex flex-col gap-4">
            <div class="flex flex-wrap gap-2 lg:gap-4">
              <div
                v-for="tag in event.tags"
                :key="tag.id"
                class="bg-slate-50 text-slate-900 px-3 py-1.5 rounded-md  border border-slate-300 text-sm-medium lg:text-base-medium"
              >
                #{{ tag.name }}
              </div>
            </div>
            <div class="text-4xl-extrabold text-slate-50 lg:text-6xl-extrabold">
              {{ event.name }}
            </div>
          </div>
        </div>
      </div>

      <!-- Details Grid -->
      <div class="flex flex-col lg:grid lg:grid-cols-[2fr_1fr] lg:gap-6">
        <div class="flex flex-col overflow-hidden lg:self-start gap-8 px-4 lg:px-0">
          <div class="flex flex-col lg:flex lg:flex-row overflow-hidden bg-white rounded-2xl border border-slate-400 relative">
            <!-- Decorative background element -->
            <div class="z-0 inset-0 absolute">
              <img :src="ShowEventDetailsGridBG" alt="Example party photos" class="h-full w-full">
            </div>
            <div class="flex flex-col lg:grid lg:grid-cols-2 gap-6 py-5 px-4 z-10">
              <!-- Date Column -->
              <div class="space-y-1 z-10">
                <div class="text-slate-500 tracking-wide text-base-medium">
                  {{ $t('public.event_info.date') }}
                </div>
                <div class="lg:space-y-1">
                  <p class="text-lg-bold lg:text-2xl-bold text-slate-950">
                    {{ event.dateFromTo }}
                  </p>
                  <p class="text-slate-800 text-base-medium lg:text-xl-medium">
                    {{ event.timeFromTo }}
                  </p>
                </div>
              </div>

              <!-- Venue Column -->
              <div class="space-y-1 z-10 flex flex-col">
                <div class="text-slate-500 tracking-wide text-base-medium">
                  {{ $t('public.event_info.venue.venue_text') }}
                </div>
                <div class="flex flex-col">
                  <p class="text-lg-bold lg:text-2xl-bold text-slate-950">
                    {{ event.venue_name }}
                  </p>
                  <p class="text-slate-800 text-base-medium">
                    {{ event.venue_address }}
                  </p>
                  <a
                    :href="mapUrl"
                    target="_blank"
                    class=" w-fit inline-flex flex-row gap-1 text-slate-800 hover:text-slate-400 border-b-2 border-slate-800 hover:border-slate-400 lg:text-lg-bold text-base-bold"
                  >
                    <span class="lg:text-sm">{{ $t('public.event_info.open_map') }}</span>
                    <LinkSquare02Icon class="text-base-medium stroke-2" />
                  </a>
                </div>
              </div>
            </div>
          </div>
          <!-- Gallery -->
          <section v-if="event.photo_gallery && event.photo_gallery.length > 0" class="lg:space-y-4">
            <div class="text-base-medium text-slate-500">
              {{ $t('public.event_info.gallery') }}
            </div>
            <div class="lg:relative">
              <!-- Scroll container -->
              <ScrollableGallery :photos="event.photo_gallery.map(photo => ({ photo: photo?.url, url: photo?.url })) ?? []" />
            </div>
          </section>

          <!-- Description -->
          <section class="space-y-4 ">
            <div class="text-base-medium text-slate-500">
              {{ $t('public.event_info.description') }}
            </div>
            <p class="text-slate-950 whitespace-pre-wrap leading-relaxed text-sm-normal lg:text-lg-normal">
              {{ event.description }}
            </p>
          </section>

          <!-- Event Links -->
          <EventLinks
            :event-links="event.social_media_links"
          />

          <!-- Policies and Contact Promoter Section -->
          <div class="flex flex-col gap-9  lg:flex lg:flex-row lg:gap-6 lg:mt-6 ">
            <!-- Contact Promoter Card -->
            <div
              class="flex flex-col items-start p-5 gap-6 lg:min-h-52 bg-pie-100 rounded-2xl self-start" :class="[
                (event.policies && event.policies.length) ? 'w-full' : 'w-1/2',
              ]"
            >
              <div class="flex flex-col gap-4">
                <!-- Headline Promoter -->
                <p class="text-base-medium text-slate-700">
                  {{ $t('public.event_info.promoter') }}
                </p>

                <!-- Profil promoter -->
                <div class="flex items-center justify-between w-full">
                  <div class="flex items-center gap-3">
                    <!-- Placeholder image -->
                    <div class="w-12 h-12">
                      <img
                        :src="profilePictureSrc"
                        class="rounded-full object-cover w-full h-full"
                        alt="Profile Picture"
                      >
                    </div>

                    <!-- Placeholder name  -->
                    <NuxtLinkLocale
                      to="/promoter_info"
                      class="text-base-medium lg:text-xl-medium text-slate-900 underline "
                    >
                      {{ event?.organiser?.name }}
                    </NuxtLinkLocale>
                  </div>
                  <ArrowRight01Icon class="text-slate-900 stroke" />
                </div>
              </div>
              <button
                class="p-1 gap-3 bg-pie-700 rounded-lg hover:bg-pie-600 w-full
                   lg:flex lg:p-1 lg:gap-3 lg:bg-pie-700 lg:rounded-lg lg:hover:bg-pie-600 lg:justify-center lg:items-center lg:w-full"
                @click="showContactForm = true"
              >
                <div
                  class="border border-white rounded-lg py-2
                     lg:border lg:border-white lg:rounded-lg lg:px-12 lg:place-items-center lg:w-full"
                >
                  <div
                    class=" bg-transparent text-white text-lg-bold
                    lg:bg-transparent lg:text-slate-100 lg:text-xl-bold lg:text-center lg:w-full lg:text-nowrap"
                  >
                    <div class="flex flex-row place-items-center gap-2 justify-center">
                      <Mail01Icon />
                      {{ $t('public.event_info.buttons.contact_promoter') }}
                    </div>
                  </div>
                </div>
              </button>

              <EventContactForm v-if="showContactForm" :organiser-email="event.organiser.contact_email" @close="showContactForm = false" />
            </div>

            <!-- Venue Policies Card -->
            <div v-if="event.policies && event.policies.length" class="flex flex-col lg:items-start p-5 gap-5 w-full lg:min-h-52 bg-pie-100 rounded-2xl ">
              <div class="text-slate-700 text-base-medium">
                {{ $t('public.event_info.venue.policies') }}
              </div>
              <div class="flex flex-col gap-2">
                <div v-for="(policy, index) in event.policies" :key="index">
                  <div>
                    <p class="text-sm-medium text-slate-950">
                      {{ policy.type }}
                    </p>
                    <p class="text-sm-normal text-slate-600">
                      {{ policy.details }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tickets Section -->
        <div
          class="flex flex-row w-full bottom-0 z-20 bg-white border-t border-slate-900 h-auto px-6 py-3 gap-12 mt-8
                  lg:flex lg:flex-col lg:rounded-2xl lg:border lg:border-slate-400 lg:self-start lg:sticky lg:p-6 lg:top-32 lg:mt-0"
          :class="[!isOrganiser ? 'sticky' : 'absolute bottom-14']"
        >
          <div>
            <!-- Price Range -->
            <div v-if="(event.ticket_types ?? []).length > 1" class="flex flex-col">
              <p class="text-base-bold lg:text-2xl-bold lg:text-slate-800 text-nowrap">
                {{ formatCurrency(Math.min(...ticketPrices), event?.currency) }} -
                {{ formatCurrency(Math.max(...ticketPrices), event?.currency) }}
              </p>
              <p class="text-slate-800 text-sm-medium lg:text-lg-medium text-nowrap">
                {{ ticketVariantsCount }} {{ $t('public.event_info.price.variants_available') }}
              </p>
            </div>
            <div v-if="event.ticket_types?.length === 1">
              <p class="mt-1 text-base-bold lg:text-2xl-bold lg:text-slate-800">
                {{ formatCurrency(Math.min(...ticketPrices), event?.currency) }}
              </p>
              <p class=" text-slate-800 text-sm-medium lg:text-lg-medium text-nowrap">
                {{ ticketVariantsCount }} {{ $t('public.event_info.price.variant_available') }}
              </p>
            </div>
          </div>

          <!-- Tickets Button -->
          <div class="flex w-full justify-end">
            <button
              :disabled="props.isOrganiser"
              class="p-1 gap-3 bg-pie-700 rounded-lg hover:bg-pie-600 w-full
                   lg:p-1 lg:gap-3 lg:bg-pie-700 lg:rounded-lg lg:hover:bg-pie-600 max-w-[24rem] lg:max-w-full"
              @click="navigateToTickets"
            >
              <div
                class="flex border border-white rounded-lg w-full h-full content-center py-2
                     lg:border lg:border-white lg:rounded-lg lg:px-12"
              >
                <div
                  class="w-full h-full text-slate-100 text-nowrap text-base-bold lg:text-xl-bold content-center"
                >
                  {{ $t('public.ticket.get_tickets') }}
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
