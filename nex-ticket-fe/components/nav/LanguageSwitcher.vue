<script setup lang="ts">
import { ArrowDown01Icon, ArrowUp01Icon } from 'hugeicons-vue'

const { locale, locales } = useI18n()

const dropdownOpen = ref<boolean>(false)

function toggleDropdown() {
  dropdownOpen.value = !dropdownOpen.value
}
</script>

<template>
  <div class="md:relative">
    <div class="md:flex md:flex-row md:items-center md:justify-center md:gap-1 md:rounded-lg md:py-3 md:ps-6 md:pe-2 md:cursor-pointer" @click="toggleDropdown">
      <h3 class="md:text-xl-bold md:text-slate-800">
        {{ locale.toUpperCase() }}
      </h3>
      <ArrowUp01Icon v-if="dropdownOpen" class="md:text-slate-800" />
      <ArrowDown01Icon v-else class="md:text-slate-800" />
    </div>
    <transition
      name="fade-slide"
      appear
    >
      <div v-if="dropdownOpen" class="z-30 md:absolute md:mt-2 md:text-lg-medium md:min-w-max md:rounded-md md:shadow-lg md:bg-white md:ring-1 md:ring-black md:ring-opacity-5">
        <div class="md:py-1">
          <SwitchLocalePathLink
            v-for="loc in locales"
            :key="loc.code"
            :locale="loc.code"
            class="md:block md:w-full md:text-left md:px-4 md:py-2 md:text-slate-800"
          >
            {{ $t(`nav.lang.${loc.code}`) }}
          </SwitchLocalePathLink>
        </div>
      </div>
    </transition>
  </div>
  <div v-if="dropdownOpen" class="z-20 absolute w-full h-full top-0 left-0" @click="dropdownOpen = false" />
</template>
