<script lang="ts" setup>
import { Cancel01Icon, Search01Icon } from 'hugeicons-vue'
import NexEvent from '~/models/NexEvent'

const { t } = useI18n()

interface SearchEvent {
  id: number
  name: string
  description: string
  start_time: Date
  end_time: Date
  venue_name: string
  main_photo: string
}

const feedback = useFeedback()

const showSearchBar = ref<boolean>(false)
const searchBarRef = ref<HTMLElement | null>(null)
const seachText = ref<string>('')
const searchItems = ref<{
  events: SearchEvent[]
}>({ events: [] })

function hadleshowSearchBar() {
  showSearchBar.value = true
  setTimeout(() => {
    searchBarRef.value?.focus()
  }, 50)
}

async function loadData() {
  const { data: searchData, error: apiError } = await useAPI('/api/public/search', {
    method: 'GET',
    query: {
      q: seachText.value,
    },
  })

  if (apiError.value) {
    feedback.error(t('errors/fetch_data_error'), { level: 'error', rollbar: true, extras: apiError.value })
    return
  }

  searchItems.value.events = []

  // @ts-expect-error - The types are not important for the MVP
  if (searchData.value && searchData.value.Events && Array.isArray(searchData.value.Events)) {
    searchItems.value.events = searchData.value.Events as SearchEvent[]
  }
}

watch(seachText, () => {
  if (seachText.value !== '' && seachText.value.length > 2) {
    loadData()
  }
  else {
    searchItems.value.events = []
  }
})
</script>

<template>
  <div class="w-full px-4 md:px-9">
    <div
      class="flex items-center rounded-lg py-3 px-6 gap-2 bg-slate-100 border border-slate-300 md:hover:border-slate-400 md:transition-colors md:duration-75 md:ease-out"
      @click="hadleshowSearchBar"
    >
      <Search01Icon class="text-slate-600 w-6 h-6" />
      <input
        type="text" class="w-full bg-transparent text-xl-medium placeholder:text-slate-500 focus:outline-none"
        :placeholder="t('nav.search_bar.placeholder')"
      >
    </div>

    <!-- Overlay -->
    <div
      v-show="showSearchBar"
      class="fixed inset-0 z-40 bg-slate-500 bg-opacity-70 backdrop-blur-sm"
      @click="showSearchBar = false"
    />

    <div class="absolute w-[556px] top-24 left-1/2 z-50 flex flex-col gap-6 -translate-x-[278px]">
      <div
        v-show="showSearchBar"
        class="flex w-full items-center rounded-lg py-3 shadow-2xl px-6 gap-2 bg-white border border-slate-200"
      >
        <Search01Icon class="text-slate-600 w-6 h-6" />
        <input
          ref="searchBarRef"
          v-model="seachText"
          type="text" class="w-full bg-transparent text-xl-medium placeholder:text-slate-500 group focus:outline-none " :placeholder="t('nav.search_bar.placeholder_long')"
        >
        <Cancel01Icon
          class="text-slate-800 cursor-pointer hover:text-slate-500 md:transition-colors md:duration-75 md:ease-out"
          @click="seachText = ''"
        />
      </div>
      <div
        v-show="showSearchBar && searchItems.events.length > 0"
        class="w-full bg-white border shadow-2xl border-slate-200 z-50 rounded-lg p-4 flex flex-col gap-6"
      >
        <div class="flex flex-col gap-2">
          <h2 class="text-sm-medium text-start text-slate-600">
            {{ $t('nav.search_bar.events') }}
          </h2>
          <div class="flex flex-col w-full gap-4">
            <NuxtLinkLocale
              v-for="event in searchItems.events" :to="`/events/${event.id}`"
              @click="showSearchBar = false"
            >
              <div class="rounded-lg border border-gray-200 bg-slate-50 flex flex-row">
                <div class="max-w-[152px] min-w-[76px]">
                  <img :src="event.main_photo">
                </div>
                <div class="py-3 px-6">
                  <h3 class="text-xs-normal text-red-600 align-text-bottom">
                    {{ NexEvent.formatDate(event.start_time) }}
                  </h3>
                  <h2 class="text-sm-bold text-slate-950">
                    {{ event.name }}
                  </h2>
                  <h4 class="text-xs-medium text-slate-500 align-text-bottom">
                    {{ event.venue_name }}
                  </h4>
                </div>
              </div>
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
