<script lang="ts" setup>

const props = defineProps({
  to: {
    type: String,
    required: true,
    readonly: true,
  },
  rounded: {
    type: String,
    default: 'lg',
  },
  borderColor: {
    type: [String, Boolean],
    default: 'slate-800',
  },
  bgColor: {
    type: [String, Boolean],
    default: false,
  },
  textColor: {
    type: String,
    default: 'slate-800',
  },
  icon: {
  },
  i18Key: {
    type: String,
    required: true,
    readonly: true,
  },
})

const border = computed(() => {
  return props.borderColor ? `md:border-y md:border-x md:border-${props.borderColor}` : ''
})

const bgColor = computed(() => {
  return props.bgColor ? `md:bg-${props.bgColor}` : ''
})
</script>

<template>
  <NuxtLinkLocale
    :to="props.to"
    class="md:flex md:flex-row md:items-center md:justify-center md:py-3 md:px-6 md:gap-3"
    :class="`md:rounded-${props.rounded} md:text-${props.textColor} ${bgColor} ${border}`"
  >
    <template v-if="props.icon">
      <component :is="props.icon" :class="`md:text-${props.textColor}`" size="24" />
    </template>
    <h3 :class="`md:text-xl-bold md:text-${props.textColor}`">
      {{ $t(props.i18Key) }}
    </h3>
  </NuxtLinkLocale>
</template>

<style>

</style>
