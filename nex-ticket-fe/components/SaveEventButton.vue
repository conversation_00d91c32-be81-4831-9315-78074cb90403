<script lang="ts" setup>
import { BookmarkAdd02Icon, BookmarkMinus02Icon } from 'hugeicons-vue'
import { useFeedback } from '~/composables/useFeedback'

const props = defineProps<{
  eventId: number
  saved: boolean
  borderSize: string
  iconSize: string
}>()

const emit = defineEmits(['reloadSavedEvents'])

const { t } = useI18n({ useScope: 'global' })
const feedback = useFeedback()

const buttonBgClass = computed(() => {
  return props.saved ? 'bg-slate-600 hover:bg-slate-800' : 'bg-slate-50 hover:bg-pie-100'
})

const buttonBorderClass = computed(() => {
  // Fix because of tailwind classes are not included unless they are found in code as whole
  switch (props.borderSize) {
    case '40':
      return 'w-[40px] h-[40px]'
    case '54':
      return 'w-[54px] h-[54px]'
    default:
      return 'w-[40px] h-[40px]'
  }
})

const buttonPadClass = computed(() => {
  // Fix because of tailwind classes are not included unless they are found in code as whole
  switch (props.borderSize) {
    case '40':
      return 'p-[10px]'
    case '54':
      return 'p-[15px]'
    default:
      return 'p-[10px]'
  }
})

const iconClass = computed(() => {
  // Fix because of tailwind classes are not included unless they are found in code as whole
  switch (props.iconSize) {
    case '20':
      return 'w-[20px] h-[20px]'
    case '24':
      return 'w-[24px] h-[24px]'
    default:
      return 'w-[20px] h-[20px]'
  }
})

const iconComponent = computed(() => {
  return props.saved ? BookmarkMinus02Icon : BookmarkAdd02Icon
})

const iconColorClass = computed(() => {
  return props.saved ? 'text-slate-100' : 'text-pie-950'
})

async function toggleSaved() {
  if (props.saved) {
    await unsaveEvent()
  }
  else {
    await saveEvent()
  }
  emit('reloadSavedEvents')
}

async function unsaveEvent() {
  try {
    const { error: fetchError } = await useAPI(`/api/public/saved_event/${props.eventId}`, { method: 'DELETE' })

    if (fetchError.value) {
      feedback.error(t('errors.event_errors.delete_event_error') + fetchError.value, { level: 'warning', rollbar: true })
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
  }
}

async function saveEvent() {
  try {
    const { error: fetchError } = await useAPI(`/api/public/save_event`, { body: { event_id: props.eventId }, method: 'POST' })

    if (fetchError.value || true) {
      feedback.error(t('errors.event_errors.save_event_error') + fetchError.value, { level: 'warning', rollbar: true })
    }
  }
  catch (err: any) {
    feedback.error(t('errors.unexpected_error'), { level: 'error', rollbar: true, extras: err })
  }
}
</script>

<template>
  <button class="transition-basic rounded-lg gap-3 shadow z-20" :class="`${buttonBgClass} ${buttonBorderClass} ${buttonPadClass}`" @click="toggleSaved">
    <component :is="iconComponent" :class="`${iconColorClass} ${iconClass} flex-shrink-0`" />
  </button>
</template>
