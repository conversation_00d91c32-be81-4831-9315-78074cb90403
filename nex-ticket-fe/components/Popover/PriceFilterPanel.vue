<script lang="ts" setup>
import { ArrowDown01Icon, ArrowUp01Icon, Cancel01Icon } from 'hugeicons-vue'

const props = defineProps({
  filterNameKey: {
    type: String,
    required: true,
  },
  minRange: {
    type: Number,
    default: 0,
  },
  maxRange: {
    type: Number,
    default: 1000,
  },
  currencyCode: {
    type: String,
    default: 'EUR',
  },
  anyPriceTextKey: {
    type: String,
    default: 'public.index.filters.price_filter.any_price',
  },
  freeTextKey: {
    type: String,
    default: 'public.index.filters.price_filter.free',
  },
})

const emit = defineEmits(['applyFilter'])

const { t } = useI18n()

const firstValue = defineModel<number | undefined>('firstValue')
const secondValue = defineModel<number | undefined>('secondValue')
const popoverOpened = defineModel<boolean>('popoverOpened', {
  type: Boolean,
  default: false,
})

const containerRef = ref<HTMLElement | null>(null)
const priceRange = ref<[number, number]>([props.minRange, props.maxRange])

const isFiltered = computed(() => {
  const minIsSet
    = firstValue.value !== undefined && firstValue.value !== props.minRange
  const maxIsSet
    = secondValue.value !== undefined && secondValue.value !== props.maxRange
  const bothSetToDefaults = firstValue.value === props.minRange && secondValue.value === props.maxRange
  return minIsSet || maxIsSet || (firstValue.value !== undefined && secondValue.value !== undefined && !bothSetToDefaults)
})

const computedFilterName = computed(() => {
  const min = firstValue.value
  const max = secondValue.value
  const freeText = t(props.freeTextKey)
  const anyPriceText = t(props.anyPriceTextKey)

  if (min === undefined && max === undefined) {
    return t(props.filterNameKey)
  }

  let name = ''
  if (min === props.minRange || min === undefined) {
    name += `${freeText} → `
  }
  else {
    name += `${formatCurrency(min, props.currencyCode)} → `
  }

  if (max === props.maxRange || max === undefined) {
    name += anyPriceText
  }
  else {
    name += formatCurrency(max, props.currencyCode)
  }
  return name
})

const applyButtonText = computed(() => {
  const [min, max] = priceRange.value
  const freeText = t(props.freeTextKey)
  const anyPriceText = t(props.anyPriceTextKey)

  let text = ''
  if (min === props.minRange) {
    text += `${freeText} → `
  }
  else {
    text += `${formatCurrency(min, props.currencyCode)} → `
  }

  if (max === props.maxRange) {
    text += anyPriceText
  }
  else {
    text += formatCurrency(max, props.currencyCode)
  }
  return text
})

watch(
  [firstValue, secondValue],
  ([newFirst, newSecond]) => {
    const currentFirst = priceRange.value[0] === props.minRange && newFirst === undefined ? undefined : priceRange.value[0]
    const currentSecond = priceRange.value[1] === props.maxRange && newSecond === undefined ? undefined : priceRange.value[1]

    if (currentFirst !== newFirst || currentSecond !== newSecond) {
      priceRange.value = [
        newFirst !== undefined ? newFirst : props.minRange,
        newSecond !== undefined ? newSecond : props.maxRange,
      ]
    }
  },
  { immediate: true },
)

function applyFilter() {
  const [newMin, newMax] = priceRange.value
  const finalMin = newMin === props.minRange ? undefined : newMin
  const finalMax = newMax === props.maxRange ? undefined : newMax

  if (firstValue.value !== finalMin || secondValue.value !== finalMax) {
    firstValue.value = finalMin
    secondValue.value = finalMax
    emit('applyFilter')
  }
  popoverOpened.value = false
}

function resetFilters() {
  firstValue.value = undefined
  secondValue.value = undefined
  priceRange.value = [props.minRange, props.maxRange]
  popoverOpened.value = false
  emit('applyFilter')
}

function togglePopover() {
  popoverOpened.value = !popoverOpened.value
}

function handleClickOutside(event: MouseEvent) {
  if (
    containerRef.value
    && !containerRef.value.contains(event.target as Node)
  ) {
    popoverOpened.value = false
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<template>
  <div
    ref="containerRef"
    class="relative inline-block gap-1 border rounded-lg"
    :class="isFiltered || popoverOpened ? ' bg-pie-100 border-pie-700' : 'border-slate-500 bg-slate-50 md:hover:bg-slate-100 md:hover:border-slate-600'"
  >
    <div
      class="w-full h-full px-4 py-3
             md:w-full md:px-6 md:cursor-pointer"
      role="button"
      :aria-expanded="popoverOpened"
      :aria-haspopup="true"
      tabindex="0"
      @click="togglePopover"
      @keydown.enter="togglePopover"
      @keydown.space="togglePopover"
    >
      <div
        class="flex items-center gap-1 text-sm-bold whitespace-nowrap md:flex md:items-center md:gap-1 md:text-lg-medium md:rounded-lg"
        :class="isFiltered || popoverOpened ? 'text-pie-700 md:text-pie-700' : 'text-slate-600'"
      >
        <span class="flex-grow">
          {{ computedFilterName }}
        </span>
        <button
          v-if="isFiltered && !popoverOpened"
          class="p-0 m-0 bg-transparent border-none cursor-pointer text-inherit hover:opacity-75"
          :aria-label="t('public.index.filters.actions.reset')"
          @click.stop="resetFilters()"
        >
          <Cancel01Icon
            :size="20"
            class="shrink-0 hover:text-red-500"
          />
        </button>
        <ArrowUp01Icon v-else-if="popoverOpened" :size="20" aria-hidden="true" />
        <ArrowDown01Icon v-else :size="20" aria-hidden="true" />
      </div>
    </div>
    <transition
      enter-active-class="transition duration-200 ease-out md:transition md:ease-out md:duration-200"
      enter-from-class="transform opacity-0 translate-y-1 md:opacity-0 md:translate-y-1"
      enter-to-class="transform opacity-100 translate-y-0 md:opacity-100 md:translate-y-0"
      leave-active-class="transition duration-150 ease-in md:transition md:ease-in md:duration-150"
      leave-from-class="transform opacity-100 translate-y-0 md:opacity-100 md:translate-y-0"
      leave-to-class="transform opacity-0 translate-y-1 md:opacity-0 md:translate-y-1"
    >
      <div
        v-if="popoverOpened"
        class="absolute z-50 p-8 mt-3 overflow-hidden transform -translate-x-1/2 bg-white border shadow-2xl left-1/2 min-w-80 rounded-2xl border-slate-200 max-w-[427px] flex flex-col gap-6 justify-center
               md:w-[427px] md:mt-3"
        role="dialog"
        aria-modal="true"
        :aria-labelledby="`${filterNameKey}-label`"
      >
        <p :id="`${filterNameKey}-label`" class="sr-only">
          {{ t(filterNameKey) }} {{ t('public.index.filters.filterPopupTitle') }}
        </p>

        <div class="flex justify-center w-full pb-6 ml-2">
          <v-range-slider
            v-model="priceRange"
            :min="minRange"
            :max="maxRange"
            :step="1"
            :ripple="false"
            range
            class="w-full price-slider !m-0 !p-0"
            :aria-label="t('public.index.filters.priceRangeSliderLabel')"
            color="#492AE0"
            track-color="#D5D3FC"
            thumb-color="#492AE0"
            :thumb-size="20"
            :track-size="10"
            rounded="pill"
          />
        </div>
        <button
          class="flex justify-center gap-3 px-6 py-3 text-xl-bold text-slate-100 rounded-lg cursor-pointer bg-pie-700 hover:bg-pie-800 focus:outline-none focus:ring-2 focus:ring-pie-500 focus:ring-opacity-50"
          @click="applyFilter()"
        >
          {{ applyButtonText }}
        </button>
      </div>
    </transition>
  </div>
</template>

<style scoped>
.price-slider {
  width: 100%;
  height: 10px;
}

.price-slider :deep(.v-slider-track) {
  background-color: #D5D3FC !important;
  height: 10px !important;
}

.price-slider :deep(.v-slider-track__fill) {
  background-color: #9085F7 !important;
  border-radius: 0px !important;
}

.price-slider :deep(.v-slider-thumb:nth-of-type(2)) {
  background-color: #492AE0 !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.price-slider :deep(.v-slider-thumb:nth-of-type(3)) {
  background-color: #492AE0 !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.price-slider :deep(.v-slider-thumb::after) {
  content: '' !important;
  position: absolute !important;
  top: 3px !important;
  left: 3px !important;
  right: 3px !important;
  bottom: 3px !important;
  border: 1px solid white !important;
  border-radius: 8px !important;
  box-sizing: border-box !important;
}

.price-slider :deep(.v-slider-thumb__ripple) {
  visibility: hidden !important;
}

.price-slider :deep(.v-slider-thumb__surface) {
  display: none !important;
}

.price-slider :deep(.v-slider-track__background) {
  display: none !important;
}

.price-slider :deep(.v-slider__container) {
  width: 90% !important;
}
</style>
