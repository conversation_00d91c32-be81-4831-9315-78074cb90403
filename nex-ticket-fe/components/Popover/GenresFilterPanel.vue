<script lang="ts" setup>
import { ArrowDown01Icon, ArrowUp01Icon, Cancel01Icon } from 'hugeicons-vue'

interface GenreOption {
  name: string
  id: number
}

const props = defineProps({
  options: {
    type: Array as () => GenreOption[],
    required: true,
    default: () => [],
  },
  selectedIds: {
    type: Array as () => number[],
    required: true,
    default: () => [],
  },
  filterNameKey: {
    type: String,
    required: true,
    default: 'public.index.filters.filterNames.genres',
  },
})

const emit = defineEmits(['update:selectedIds', 'update:popoverOpened', 'apply'])

const { t } = useI18n()

const popoverOpened = defineModel('popoverOpened', { type: Boolean, default: false })

const tempSelectedIds = ref<number[]>([])
const containerRef = ref<HTMLElement | null>(null)

const isFiltered = computed(() => props.selectedIds.length > 0)

const mainText = computed(() => {
  if (!isFiltered.value) {
    return t(props.filterNameKey)
  }
  const selectedGenres = props.options.filter(option => props.selectedIds.includes(option.id))
  let text = ''
  const loopCount = selectedGenres.length < 4 ? selectedGenres.length : 3
  for (let i = 0; i < loopCount; i++) {
    text += `#${selectedGenres[i].name} | `
  }
  if (selectedGenres.length > 3) {
    text += '...'
  }
  else if (selectedGenres.length > 0) {
    text = text.slice(0, -3)
  }
  else {
    return t(props.filterNameKey)
  }
  return text
})

watch(popoverOpened, (isOpen) => {
  if (isOpen) {
    tempSelectedIds.value = [...props.selectedIds]
  }
})

function toggleSelection(genreId: number) {
  const index = tempSelectedIds.value.indexOf(genreId)
  if (index > -1) {
    tempSelectedIds.value.splice(index, 1)
  }
  else {
    tempSelectedIds.value.push(genreId)
  }
}

function applyFilter() {
  emit('update:selectedIds', [...tempSelectedIds.value])
  emit('apply')
  popoverOpened.value = false
}

function resetFilters() {
  tempSelectedIds.value = []
  emit('update:selectedIds', [])
  emit('apply')
  popoverOpened.value = false
}

function handleClickOutside(event: MouseEvent) {
  if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
    popoverOpened.value = false
  }
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<template>
  <div
    ref="containerRef"
    class="relative inline-block border rounded-lg gap-1"
    :class="isFiltered || popoverOpened ? ' bg-pie-100 border-pie-700' : 'border-slate-500 bg-slate-50 hover:bg-slate-100 hover:border-slate-600 hover:text-slate-700'"
  >
    <div
      class="md:px-6 md:cursor-pointer
              w-full h-full px-4 py-3"
      @click="popoverOpened = !popoverOpened"
    >
      <button
        class="md:flex md:items-center md:gap-1 md:text-lg-medium md:rounded-lg
             flex whitespace-nowrap items-center gap-1 text-sm-bold"
        :class="isFiltered || popoverOpened ? 'md:text-pie-700 text-pie-700' : 'text-slate-600'"
      >
        {{ mainText }}
        <ArrowUp01Icon v-if="popoverOpened" :size="20" />
        <Cancel01Icon v-else-if="isFiltered" :size="20" class="shrink-0 hover:text-red-500" @click.stop="resetFilters()" />
        <ArrowDown01Icon v-else :size="20" />
      </button>
    </div>
    <transition
      enter-active-class="md:transition md:ease-out md:duration-200"
      enter-from-class="md:opacity-0 md:translate-y-1"
      enter-to-class="md:opacity-100 md:translate-y-0"
      leave-active-class="md:transition md:ease-in md:duration-150"
      leave-from-class="md:opacity-100 md:translate-y-0"
      leave-to-class="md:opacity-0 md:translate-y-1"
    >
      <div
        v-if="popoverOpened" class="md:w-[514px]
                                    absolute z-50 flex flex-col left-1/2 transform -translate-x-1/2 bg-white border rounded-2xl shadow-2xl p-8 gap-6 mt-3 border-slate-200 max-h-[184px] overflow-y-auto"
      >
        <div class="flex flex-wrap gap-2 max-h-[11.5rem] overflow-y-auto">
          <div
            v-for="genre in options" :key="genre.id" class="border rounded-lg px-4 py-3 cursor-pointer"
            :class="tempSelectedIds.includes(genre.id) ? 'bg-pie-100 border-pie-700 text-pie-700' : 'bg-slate-50 border-slate-300 hover:bg-slate-100 hover:border-slate-400 text-slate-900'"
            @click="toggleSelection(genre.id)"
          >
            <p class="text-lg-medium">
              {{ `#${genre.name}` }}
            </p>
          </div>
        </div>

        <div
          v-if="tempSelectedIds.length > 0"
          class="rounded-lg px-6 py-3 gap-3 bg-pie-700 flex justify-center cursor-pointer whitespace-nowrap"
          @click="applyFilter()"
        >
          <button>
            <p class="text-xl-bold text-slate-100">
              {{ `Select ${tempSelectedIds.length} genres` }}
            </p>
          </button>
        </div>
        <div
          v-else-if="isFiltered"
          class="rounded-lg px-6 py-3 gap-3 bg-pie-700 flex justify-center cursor-pointer"
          @click="resetFilters()"
        >
          <button>
            <p class="text-xl-bold text-slate-100">
              Clear filters
            </p>
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>
