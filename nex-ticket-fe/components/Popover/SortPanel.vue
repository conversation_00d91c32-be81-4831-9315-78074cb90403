<script lang="ts" setup>
import type { FunctionalComponent } from 'vue'
import { Cancel01Icon, CoinsEuroIcon, SortByDown02Icon, SortByUp02Icon } from 'hugeicons-vue'
import { onMounted, onUnmounted, ref } from 'vue'

interface SortOption {
  value: string
  direction: 'ASC' | 'DESC' | ''
  icon: FunctionalComponent | null
  sort: string
}
interface FilterOption {
  option: string
  icon: FunctionalComponent
  sort: string
}

const props = defineProps({
  navTarget: {
    type: String,
    required: false,
  },
})
const emit = defineEmits(['applySort'])

const sortOptions = defineModel<SortOption>('sortOptions', {
  required: true,
  default: () => ({ value: '', direction: '', icon: null, sort: '' }),
})
const popoverOpened = defineModel<boolean>('popoverOpened', { default: false })
const containerRef = ref<HTMLElement | null>(null)

const filterOptions = ref<FilterOption[]>([
  { option: 'Newest first', icon: SortByDown02Icon, sort: 'DATE' },
  { option: 'Cheapest first', icon: CoinsEuroIcon, sort: 'PRICE' },
])

function triggerAction() {
  if (props.navTarget) {
    const query = (sortOptions.value.sort && sortOptions.value.direction)
      ? { sortField: sortOptions.value.sort, direction: sortOptions.value.direction }
      : {}
    navigateToWLocale({ path: props.navTarget, query })
  }
  else {
    emit('applySort')
  }
}

function resetSort(event?: MouseEvent) {
  event?.stopPropagation()
  sortOptions.value = { value: '', direction: '', icon: null, sort: '' }
  popoverOpened.value = false
  triggerAction()
}

function processChoosenValue(chosenFilter: FilterOption) {
  popoverOpened.value = false
  let newDirection: SortOption['direction'] = 'ASC'

  if (sortOptions.value.sort === chosenFilter.sort && sortOptions.value.direction) {
    newDirection = sortOptions.value.direction === 'ASC' ? 'DESC' : 'ASC'
  }

  sortOptions.value = {
    value: chosenFilter.option.toUpperCase(),
    icon: chosenFilter.icon,
    sort: chosenFilter.sort,
    direction: newDirection,
  }
  triggerAction()
}

function handleClickOutside(event: MouseEvent) {
  if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
    popoverOpened.value = false
  }
}

onMounted(() => document.addEventListener('mousedown', handleClickOutside))
onUnmounted(() => document.removeEventListener('mousedown', handleClickOutside))

const buttonBaseClasses = 'flex whitespace-nowrap items-center gap-1 text-sm-bold md:text-lg-medium md:rounded-full md:justify-center'
const buttonActiveClasses = 'text-pie-700'
const buttonInactiveClasses = 'text-slate-600'

const containerBaseClasses = 'border rounded-full relative inline-block gap-1 md:relative md:inline-block md:border md:rounded-full md:gap-1 md:cursor-pointer'
const containerActiveClasses = 'bg-pie-100 border-pie-700'
const containerInactiveClasses = 'border-slate-500 bg-slate-50 hover:border-slate-600 hover:bg-slate-100 hover:text-slate-700'
</script>

<template>
  <div
    ref="containerRef"
    :class="[
      containerBaseClasses,
      (sortOptions.direction || popoverOpened) ? containerActiveClasses : containerInactiveClasses,
    ]"
  >
    <div
      class="w-full h-full px-4 py-3 md:w-full md:h-full md:px-6 md:py-3 md:cursor-pointer"
      @click="popoverOpened = !popoverOpened"
    >
      <button
        :class="[
          buttonBaseClasses,
          (sortOptions.direction || popoverOpened) ? buttonActiveClasses : buttonInactiveClasses,
        ]"
      >
        <component :is="sortOptions.icon" v-if="sortOptions.direction" :size="20" class="md:mb-1" />
        <p v-if="!sortOptions.direction">
          {{ $t('public.index.sorter.sort') }}
        </p>
        <p v-else>
          {{ sortOptions.value.charAt(0).toUpperCase() + sortOptions.value.slice(1).toLowerCase() }}
        </p>
        <Cancel01Icon
          v-if="sortOptions.direction"
          :size="20"
          class="shrink-0 hover:text-red-500"
          @click.stop="resetSort"
        />
        <SortByUp02Icon v-else :size="20" class="md:mb-1" />
      </button>
    </div>

    <transition
      enter-active-class="md:transition md:ease-out md:duration-200"
      enter-from-class="md:opacity-0 md:translate-y-1"
      enter-to-class="md:opacity-100 md:translate-y-0"
      leave-active-class="md:transition md:ease-in md:duration-150"
      leave-from-class="md:opacity-100 md:translate-y-0"
      leave-to-class="md:opacity-0 md:translate-y-1"
    >
      <div
        v-if="popoverOpened"
        class="z-50 bg-white shadow-2xl border mt-3 min-w-20 text-center border-slate-200 rounded-2xl gap-6 p-8 flex absolute"
      >
        <ul class="flex flex-col gap-3">
          <li
            v-for="option in filterOptions"
            :key="option.option"
            class="min-w-[300px] rounded-lg border flex gap-3 p-4 cursor-pointer hover:bg-pie-100 hover:border-pie-700 hover:text-pie-700"
            :class="option.sort === sortOptions.sort ? 'bg-pie-100 border-pie-700 text-pie-700' : 'bg-slate-50 border-slate-300'"
            @click="processChoosenValue(option)"
          >
            <component :is="option.icon" :size="24" />
            <p class="text-base-medium text-slate">
              {{ option.option }}
            </p>
          </li>
        </ul>
      </div>
    </transition>
  </div>
</template>
