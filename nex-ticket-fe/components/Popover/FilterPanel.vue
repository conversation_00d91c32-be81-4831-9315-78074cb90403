<script lang="ts" setup>
import { ArrowDown01Icon, ArrowUp01Icon, Cancel01Icon } from 'hugeicons-vue'

const props = defineProps({
  filterFor: {
    type: String,
    required: true,
  },
  filterNameKey: {
    type: String,
    required: true,
  },
  filterTextKey: {
    type: String,
    required: true,
  },
  inputType: {
    type: String,
    default: 'text',
  },
  singleInput: {
    type: Boolean,
    default: true,
  },
  firstInputHolderKey: {
    type: String,
    default: '',
  },
  secondInputHolderKey: {
    type: String,
    default: '',
  },
  navTarget: {
    type: String,
    required: false,
  },
})

const emit = defineEmits(['applyFilter'])

const firstValue = defineModel<string | Date | number>('firstValue')
const secondValue = defineModel<string | Date | number>('secondValue')
const popoverOpened = defineModel('popoverOpened', { type: Boolean, default: false })

const valuesSupplied = ref<boolean>()
const valuesCorrect = ref<boolean>(true)

const isFiltered = ref(false)

const minValue = ref<string | undefined>()
const maxValue = ref<string | undefined>()
const filterFor = ref<string | undefined>()

onMounted(() => {
  const route = useRoute()
  minValue.value = route.query.minValue ? String(route.query.minValue) : undefined
  maxValue.value = route.query.maxValue ? String(route.query.maxValue) : undefined
  filterFor.value = route.query.filterFor ? String(route.query.filterFor) : undefined

  if ((minValue.value || maxValue.value) && filterFor.value === props.filterFor.toUpperCase()) {
    isFiltered.value = true
  }
})

function applyFilter() {
  isFiltered.value = true
  popoverOpened.value = false
  emit('applyFilter')
}

function formatPriceFilter(value: string | Date | number) {
  if (props.filterFor.toUpperCase() === 'PRICE') {
    formatPrice(value)
  }
  return value
}

function resetFilters() {
  isFiltered.value = false
  firstValue.value = ''
  secondValue.value = ''
  valuesSupplied.value = false
  emit('applyFilter')
}

function validateValues() {
  if ((props.singleInput === true && !firstValue.value) || (props.singleInput === false && (!firstValue.value || !secondValue.value))) {
    valuesSupplied.value = false
  }
  else {
    valuesSupplied.value = true
  }

  if ((props.inputType === 'number' || props.inputType === 'date') && (firstValue.value ?? 0) > (secondValue.value ?? 0) && (firstValue.value && secondValue.value)) {
    valuesCorrect.value = false
  }
  else {
    valuesCorrect.value = true
  }
}
</script>

<template>
  <div
    class="md:relative md:inline-block md:border md:rounded-lg md:gap-1
           relative inline-block border rounded-lg gap-1"
    :class="isFiltered || popoverOpened ? ' bg-pie-100 border-pie-700' : 'border-slate-500 bg-slate-50 md:hover:bg-slate-100 md:hover:border-slate-600'"
  >
    <div
      class="md:w-full md:h-full md:px-6 md:py-3 md:cursor-pointer
              w-full h-full px-4 py-3"
      @click="popoverOpened = !popoverOpened"
    >
      <button
        class="md:flex md:items-center md:gap-1 md:text-lg-medium md:rounded-lg
             flex items-center gap-1 text-sm-bold"
        :class="isFiltered || popoverOpened ? 'md:text-pie-700 text-pie-700' : 'md:text-slate-500 text-slate-500'"
      >
        {{ $t(props.filterNameKey) }}
        <ArrowUp01Icon v-if="popoverOpened" :size="20" />
        <Cancel01Icon v-else-if="isFiltered" :size="20" @click="resetFilters()" />
        <ArrowDown01Icon v-else :size="20" />
      </button>
    </div>
    <transition
      enter-active-class="md:transition md:ease-out md:duration-200"
      enter-from-class="md:opacity-0 md:translate-y-1"
      enter-to-class="md:opacity-100 md:translate-y-0"
      leave-active-class="md:transition md:ease-in md:duration-150"
      leave-from-class="md:opacity-100 md:translate-y-0"
      leave-to-class="md:opacity-0 md:translate-y-1"
    >
      <div
        v-if="popoverOpened" class="md:absolute md:bg-slate-50 md:border md:border-gray-300 md:p-4 md:rounded md:shadow-lg md:mt-3 md:min-w-80
                                    fixed z-50 left-1/2 transform -translate-x-1/2 bg-slate-50 border rounded shadow-lg mt-3 min-w-80 p-4 border-gray-300"
      >
        <h2 class="md:text-base-medium">
          <p>{{ $t('public.index.filters.filter_X') }} {{ $t(props.filterTextKey) }}:</p>
        </h2>
        <div v-if="props.singleInput">
          <h4 class="md:text-base-medium">
            <!-- TODO Implement autocomplete -->
            <input
              v-model="firstValue"
              ondragenter="firstValue"
              :type="props.inputType" class="md:border md:border-gray-300 md:p-2 md:rounded md:mt-2 md:m-2 md:w-40
                                             border rounded p-2 mt-2 m-2 w-40 border-gray-300"
              :placeholder="$t(props.firstInputHolderKey)"
              @change="validateValues"
            >
          </h4>
        </div>
        <div v-else>
          <h5 class="md:text-base-medium">
            <input
              v-model="firstValue"
              :type="props.inputType" class="md:border md:border-gray-300 md:p-2 md:rounded md:m-2 md:w-28
                                             border border-gray-300 p-2 rounded m-2 w-28"
              :placeholder="$t(props.firstInputHolderKey)"
              onchange="firstValue"
              @focusout="firstValue && (firstValue = formatPriceFilter(firstValue))"
              @change="validateValues"
            >
            -
            <input
              v-model="secondValue"
              :type="props.inputType" class="md:border md:border-gray-300 md:p-2 md:rounded md:m-2 md:w-28
                                             border border-gray-300 p-2 rounded m-2 w-28"
              :placeholder="$t(props.secondInputHolderKey)"
              onchange="secondValue"
              @focusout="secondValue && (secondValue = formatPriceFilter(secondValue))"
              @change="validateValues"
            >
          </h5>
          <div
            v-if="!valuesCorrect" class="md:text-red-600 md:font-onest
                                         text-red-600 font-onest"
          >
            {{ $t('public.index.filters.warnings.minBiggerThanMax') }}
          </div>
        </div>
        <div
          v-if="!valuesSupplied" class="md:text-red-600 md:font-onest
                                        text-red-600 font-onest"
        >
          {{ $t('public.index.filters.warnings.noValues') }}
        </div>
        <div class="float-left">
          <button
            class="transition-basic bg-slate-800 text-white px-4 py-2 rounded-xl mt-2"
            @click="resetFilters"
          >
            <h6 class="text-base-medium">
              {{ $t('public.index.filters.buttonTexts.reset') }}
            </h6>
          </button>
        </div>
        <div class="md:float-right float-right">
          <NuxtLinkLocale
            v-if="props.navTarget"
            :to="{ path: `${props.navTarget}`,
                   query: { filterFor: `${props.filterFor.toUpperCase()}`,
                            minValue: `${firstValue}`,
                            maxValue: `${secondValue}` } }"
          >
            <button
              :disabled="!valuesSupplied || !valuesCorrect" class="bg-slate-800 text-white px-4 py-2 rounded-xl mt-2"
            >
              <h6 class="text-base-medium">
                {{ $t('public.index.filters.buttonTexts.apply') }}
              </h6>
            </button>
          </NuxtLinkLocale>
          <button
            v-else :disabled="!valuesSupplied || !valuesCorrect" class="transition-basic bg-slate-800 text-white px-4 py-2 rounded-xl mt-2" @click="applyFilter()"
          >
            <h6 class="text-base-medium">
              {{ $t('public.index.filters.buttonTexts.apply') }}
            </h6>
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>

<style>

</style>
