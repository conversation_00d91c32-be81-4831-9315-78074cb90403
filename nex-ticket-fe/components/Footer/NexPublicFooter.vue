<script lang="ts" setup>
import { PlayIcon } from 'hugeicons-vue'

const localePath = useLocalePath()

const footerSections = {
  company: [
    { textKey: 'footer.contact_us', path: '/contact' },
    { textKey: 'footer.about_us', path: '/about' },
  ],
  services: [
    { textKey: 'footer.ticketing', path: '/events' },
    { textKey: 'footer.for_organizers', path: '/for_organisers_info' },
  ],
  resources: [
    { textKey: 'footer.blog', path: '/blog' },
    { textKey: 'footer.faq', path: '/faq' },
    { textKey: 'footer.help_center', path: '/help_center' },
  ],
}

const footerSocialLinks = [
  { platform: 'facebook', link: 'https://www.facebook.com/yourcompany' },
  { platform: 'instagram', link: 'https://www.instagram.com/yourcompany' },
  { platform: 'telegram', link: 'https://t.me/yourchannel' },
  // { platform: 'website', link: 'https://www.yourcompany.com' },
]
</script>

<template>
  <div
    class="md:py-48 md:gap-16 md:px-0
              gap-9 py-24 px-4 flex flex-col bg-pie-950 items-center justify-center text-center"
  >
    <p
      class="md:text-8xl
              text-4xl font-sofia text-white font-[800]"
    >
      TICKETPIE — WHERE IT ALL<br>CLICKS TOGETHER.
    </p>
    <img
      class="shrink-0 md:w-[6.438rem] md:h-[6.438rem] w-[3.219rem] h-[3.219rem] bg-transparent"
      src="assets/images/logo.png"
      alt="Logo"
    >
    <div
      class="md:flex-row md:gap-6 md:w-auto
                flex flex-col gap-3 w-full"
    >
      <!-- Mobile button -->
      <NexButton to="/registration" text-key="footer.get_started_for_free" variant="primary" bg-color="white" hover-color="pie-950" hover-text-color="white" text-color="pie-950" :second-border="true" border-color="pie-950" :paddingx="6" :paddingy="4" class="md:hidden" />
      <NexButton to="/search_events" text-key="footer.demo_request" variant="secondary" :append-icon="PlayIcon" text-color="white" hover-color="white" border-color="pie-100" hover-text-color="pie-950" :paddingx="6" :paddingy="4" class="md:hidden" />
      <!-- Desktop button -->
      <NexButton to="/registration" variant="primary" text-key="footer.get_started" bg-color="white" hover-color="pie-950" hover-text-color="white" text-color="pie-950" :second-border="true" border-color="pie-950" :paddingx="9" :paddingy="6" class="hidden md:block" />
      <NexButton to="/search_events" text-key="footer.explore_events" text-color="white" hover-color="white" border-color="pie-100" hover-text-color="pie-950" :paddingx="9" :paddingy="6" variant="secondary" class="hidden md:block" />
    </div>
  </div>
  <div class="w-full bg-pie-950 md:px-24">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 px-8 md:px-6 pt-10 pb-6">
      <div class="flex flex-col justify-center items-center lg:items-start">
        <NuxtLinkLocale :to="localePath('/')" class="inline-block mb-5">
          <div class="flex text-4xl-bold text-white hover:text-slate-300 transition-colors uppercase">
            TicketPie
          </div>
        </NuxtLinkLocale>

        <p class="text-base-normal text-slate-200 mb-6 pr-4">
          {{ $t('footer.site_description') }}
        </p>
        <div class="text-sm-semibold text-slate-50 mb-3 uppercase tracking-wider">
          {{ $t('footer.follow_us') }}
        </div>
        <FooterIconLinks :event-links="footerSocialLinks" />
      </div>

      <div class="lg:col-span-2 grid grid-cols-2 md:grid-cols-3 gap-10 gap-x-32 md:gap-x-0 items-between mx-auto md:mx-0">
        <div class="flex md:justify-center">
          <div class="flex flex-col gap-3 max-w-min">
            <div class="text-lg-bold text-white mb-2  tracking-wider">
              {{ $t('footer.company') }}
            </div>
            <NuxtLinkLocale
              v-for="link in footerSections.company"
              :key="link.textKey"
              :to="localePath(link.path)"
              class="self-start text-slate-100 hover:text-white text-sm-normal transition-colors duration-300 hover-underline-animation"
            >
              {{ $t(link.textKey) }}
            </NuxtLinkLocale>
          </div>
        </div>
        <div class="flex md:justify-center">
          <div class="flex flex-col gap-3">
            <div class="text-lg-bold text-white mb-2  tracking-wider">
              {{ $t('footer.services') }}
            </div>
            <NuxtLinkLocale
              v-for="link in footerSections.services"
              :key="link.textKey"
              :to="localePath(link.path)"
              class="self-start text-slate-100 hover:text-white text-sm-normal transition-colors duration-300 hover-underline-animation"
            >
              {{ $t(link.textKey) }}
            </NuxtLinkLocale>
          </div>
        </div>
        <div class="flex md:justify-center">
          <div class="flex flex-col gap-3">
            <div class="text-lg-bold text-white mb-2  tracking-wider">
              {{ $t('footer.resources') }}
            </div>
            <NuxtLinkLocale
              v-for="link in footerSections.resources"
              :key="link.textKey"
              :to="localePath(link.path)"
              class="self-start text-slate-100 hover:text-white text-sm-normal transition-colors duration-300 hover-underline-animation"
            >
              {{ $t(link.textKey) }}
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </div>
  </div>

  <hr class="w-full h-[1px] bg-slate-100">

  <div class="flex flex-col md:flex-row items-center justify-between gap-4 text-sm-normal px-28 py-6 bg-pie-1100">
    <p class="text-slate-50 text-center md:text-left order-2 md:order-1">
      &copy; {{ new Date().getFullYear() }} {{ $t('footer.copyright', { brand: 'TicketPie' }) }}
    </p>
    <nav class="flex flex-col sm:flex-row gap-4 md:gap-6 order-1 md:order-2 items-center">
      <NuxtLinkLocale
        :to="localePath('/terms_and_conditions')"
        class="text-slate-50 hover:text-white transition-colors duration-300 hover-underline-animation"
      >
        {{ $t('footer.terms_and_conditions') }}
      </NuxtLinkLocale>
      <NuxtLinkLocale
        :to="localePath('/privacy_policy')"
        class="text-slate-50 hover:text-white transition-colors duration-300 hover-underline-animation"
      >
        {{ $t('footer.privacy_policy') }}
      </NuxtLinkLocale>
    </nav>
  </div>
</template>

<style scoped>
.hover-underline-animation {
  position: relative;
  display: inline-block;
  transition: color 0.3s ease;
}

.hover-underline-animation:hover {
  color: #ffffff;
}

.hover-underline-animation::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 0;
  background-color: #ffffff;
  transition: width 0.3s ease;
}

.hover-underline-animation:hover::after {
  width: 100%;
}
</style>
