<script setup lang="ts">
const props = defineProps<{
  height: string
}>()

const { t } = useI18n({ useScope: 'global' })

const lat = defineModel<number>('lat')
const lng = defineModel<number>('lng')
const { onLoaded } = useScriptGoogleMaps({
  apiKey: useRuntimeConfig().public.googleMapsApiKey,
  libraries: ['places', 'marker'],
})

const feedback = useFeedback()

const mapRef = ref()
let map: google.maps.Map | null = null
const mapsApi = ref()
const eventListeners = ref()
const searchString = ref('')
const showMap = ref(false)
let autocomplete: google.maps.places.Autocomplete | null = null
let dragableMarker: google.maps.marker.AdvancedMarkerElement | null = null

function importLibrary(key: string): Promise<void> {
  const p = mapsApi.value?.importLibrary(key) || new Promise((resolve) => {
    const stop = watch(mapsApi, (api) => {
      if (api) {
        const p = api.importLibrary(key)
        resolve(p)
        stop()
      }
    }, { immediate: true })
  })
  return p as any as Promise<void>
}

function initMap() {
  map = new mapsApi.value.Map(mapRef.value, {
    center: { lat: lat.value ? Number(lat.value) : 37.39094933041195, lng: lng.value ? Number(lng.value) : -122.02503913145092 },
    zoom: 14,
    mapId: '4504f8b37365c3d0',
  })
}

function addMarker(location: google.maps.LatLng) {
  if (dragableMarker) {
    dragableMarker.position = location
    lat.value = location.lat()
    lng.value = location.lng()
    return
  }
  dragableMarker = new mapsApi.value.marker.AdvancedMarkerElement({
    position: location,
    map,
    gmpDraggable: true,
  })

  if (dragableMarker) {
    eventListeners.value = dragableMarker.addListener('dragend', () => {
      if (!dragableMarker || !dragableMarker.position) {
        return
      }
      const possition = dragableMarker.position
      lat.value = possition.lat as number
      lng.value = possition.lng as number
    })
  }
}

function onPlaceChanged() {
  if (!autocomplete || !map) {
    return
  }

  const place = autocomplete.getPlace()
  if (place && place.geometry && place.geometry.location) {
    lat.value = place.geometry.location.lat()
    lng.value = place.geometry.location.lng()
    map.setCenter(place.geometry.location)
    addMarker(place.geometry.location)
    showMap.value = true
  }
}

function initAutocomplete() {
  const searchBar = document.getElementById('google-autocomplete')
  autocomplete = new mapsApi.value.places.Autocomplete(searchBar)

  if (!autocomplete) {
    feedback.error(t('errors.google_autocomplete_init_fail'), { level: 'error', rollbar: true })
    return
  }

  autocomplete.addListener('place_changed', onPlaceChanged)
}

onMounted(() => {
  onLoaded(async (instance) => {
    mapsApi.value = await instance.maps as any as typeof google.maps
    await importLibrary('marker')
    initMap()
    initAutocomplete()
  })
})

onUnmounted(() => {
  if (eventListeners.value) {
    eventListeners.value.remove()
  }
})
</script>

<template>
  <v-text-field
    id="google-autocomplete"
    v-model="searchString"
    class="font-onest"
    placeholder="Search place"
    variant="outlined"
    @input="onPlaceChanged"
  />
  <div id="map" ref="mapRef" :class="props.height" class="rounded-lg border border-slate-400" />
</template>

<style scoped></style>
