<script lang="ts" setup>
import { Calendar03Icon, Clock01Icon, UserIcon } from 'hugeicons-vue'

interface BlogPost {
  id: number
  title: string
  excerpt: string
  author: string
  publishedAt: Date
  category: string
  slug: string
  featuredImage: string
  readTime: number
  tags: string[]
}

const props = defineProps<{
  post: BlogPost
}>()

// Format date for display
const formattedDate = computed(() => {
  return props.post.publishedAt.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
})

// Get category color
const categoryColor = computed(() => {
  const colors: Record<string, string> = {
    events: 'bg-blue-100 text-blue-800',
    tips: 'bg-green-100 text-green-800',
    updates: 'bg-purple-100 text-purple-800',
    guides: 'bg-orange-100 text-orange-800',
  }
  return colors[props.post.category] || 'bg-slate-100 text-slate-800'
})
</script>

<template>
  <NuxtLinkLocale :to="`/blog/${post.slug}`" class="block h-full">
    <article
      class="bg-white border border-slate-200 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden h-full flex flex-col"
    >
    <!-- Featured Image -->
    <div class="relative overflow-hidden rounded-t-2xl">
      <img
        :src="post.featuredImage"
        :alt="post.title"
        class="w-full h-48 md:h-52 object-cover group-hover:scale-105 transition-transform duration-300"
      >
      <!-- Category Badge -->
      <div class="absolute top-4 left-4">
        <span
          class="px-3 py-1 rounded-full text-xs font-medium"
          :class="categoryColor"
        >
          {{ $t(`blog.categories.${post.category}`) }}
        </span>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6 flex flex-col flex-1">
      <!-- Title -->
      <h2 class="text-xl-bold text-slate-900 mb-3 group-hover:text-pie-700 transition-colors duration-200 line-clamp-2">
        {{ post.title }}
      </h2>

      <!-- Excerpt -->
      <p class="text-sm-medium text-slate-600 mb-4 flex-1 line-clamp-3">
        {{ post.excerpt }}
      </p>

      <!-- Meta Information -->
      <div class="flex flex-col gap-3 mt-auto">
        <!-- Author and Date -->
        <div class="flex items-center gap-4 text-xs text-slate-500">
          <div class="flex items-center gap-1">
            <UserIcon class="w-4 h-4" />
            <span>{{ post.author }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Calendar03Icon class="w-4 h-4" />
            <span>{{ formattedDate }}</span>
          </div>
        </div>

        <!-- Read Time and Tags -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-1 text-xs text-slate-500">
            <Clock01Icon class="w-4 h-4" />
            <span>{{ post.readTime }} {{ $t('blog.read_time_minutes') }}</span>
          </div>

          <!-- Tags -->
          <div class="flex gap-1 flex-wrap">
            <span
              v-for="tag in post.tags.slice(0, 2)"
              :key="tag"
              class="px-2 py-1 bg-slate-100 text-slate-600 rounded text-xs"
            >
              {{ tag }}
            </span>
            <span
              v-if="post.tags.length > 2"
              class="px-2 py-1 bg-slate-100 text-slate-600 rounded text-xs"
            >
              +{{ post.tags.length - 2 }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Read More Indicator -->
    <div class="px-6 pb-6">
      <div class="flex items-center justify-between pt-4 border-t border-slate-100">
        <span class="text-sm-medium text-pie-600 group-hover:text-pie-700 transition-colors duration-200">
          {{ $t('blog.read_more') }}
        </span>
        <svg
          class="w-4 h-4 text-pie-600 group-hover:text-pie-700 group-hover:translate-x-1 transition-all duration-200"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </div>
    </article>
  </NuxtLinkLocale>
</template>

<style scoped>
/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
