<script lang="ts" setup>
const props = defineProps({
  size: {
    type: String as PropType<'sm' | 'lg'>,
    default: 'lg',
  },
  prependIcon: {
    optional: true,
  },
  appendIcon: {
    optional: true,
  },
  textKey: {
    type: String,
    default: 'Missing text key',
  },
  text: {
    type: String,
  },
  bgColor: {
    type: String,
    optional: true,
  },
  hoverColor: {
    type: String,
    optional: true,
  },
  borderColor: {
    type: String,
    optional: true,
  },
  hoverBorderColor: {
    type: String,
    optional: true,
  },
  textColor: {
    type: String,
    optional: true,
  },
  hoverTextColor: {
    type: String,
    optional: true,
  },
  secondBorder: {
    type: Boolean,
    default: false,
  },
  paddingx: {
    type: Number,
    optional: true,
  },
  paddingy: {
    type: Number,
    optional: true,
  },
  textStyle: {
    type: String,
    optional: true,
  },
  to: {
    type: String,
    default: '',
    readonly: true,
  },
  variant: {
    type: String as PropType<'primary' | 'secondary'>,
    default: 'primary',
  },
  fullWidth: {
    type: Boolean,
    default: false,
  },
  secondBorderInset: {
    type: String,
    default: 'inset-1',
  },
})

const divClasses = computed(() => {
  let classes = props.size === 'sm' ? 'md:gap-1 md:px-3 md:py-2' : 'gap-3 border shadow-sm'

  switch (props.variant) {
    case 'primary':
      classes += props.bgColor ? ` bg-${props.bgColor}` : ' bg-pie-700'
      classes += props.hoverColor ? ` hover:bg-${props.hoverColor}` : ' hover:bg-pie-500'
      classes += props.borderColor ? ` border-${props.borderColor}` : ' border-pie-700'
      classes += props.hoverBorderColor ? ` hover:border-${props.hoverBorderColor}` : ' hover:border-pie-500'
      classes += props.textColor ? ` text-${props.textColor}` : ' text-slate-100'
      classes += props.hoverTextColor ? ` hover:text-${props.hoverTextColor}` : ' '
      break
    case 'secondary':
      classes += props.bgColor ? ` bg-${props.bgColor}` : ' bg-transparent'
      classes += props.hoverColor ? ` hover:bg-${props.hoverColor}` : ' hover:bg-pie-100'
      classes += props.borderColor ? ` border-${props.borderColor}` : ' border-slate-800'
      classes += props.hoverBorderColor ? ` hover:border-${props.hoverBorderColor}` : ' hover:border-pie-700'
      classes += props.textColor ? ` text-${props.textColor}` : ' text-slate-800'
      classes += props.hoverTextColor ? ` hover:text-${props.hoverTextColor}` : ' hover:text-pie-700'
      break
  }

  classes += props.textStyle ? ` ${props.textStyle}` : ' md:text-xl-bold text-base-bold'
  classes += props.paddingx ? ` px-${props.paddingx}` : ' md:px-6 px-4'
  classes += props.paddingy ? ` py-${props.paddingy}` : ' md:py-3 py-2'
  classes += ' md:ease-out md:transition-colors md:duration-75'
  classes += props.fullWidth ? ' w-full' : ''

  return classes
})
</script>

<template>
  <NuxtLinkLocale
    v-if="props.to"
    :to="props.to"
  >
    <div class="flex flex-row items-center justify-center rounded-lg relative" :class="divClasses">
      <div
        v-if="props.secondBorder" class="block absolute border rounded-lg" :class="[
          props.secondBorderInset,
          props.borderColor ? `border-${props.borderColor}` : 'border-slate-100',
          props.hoverBorderColor ? `hover:border-${props.hoverBorderColor}` : '',
        ]"
      />
      <component :is="props.prependIcon" v-if="props.prependIcon" class="md:text-xl-bold text-base-bold" />
      <h2 class="text-nowrap">
        {{ props.text ? props.text : $t(props.textKey) }}
      </h2>
      <component :is="props.appendIcon" v-if="props.appendIcon" class="md:text-xl-bold text-base-bold" />
    </div>
  </NuxtLinkLocale>
  <template v-else>
    <div class="flex flex-row items-center justify-center rounded-lg relative" :class="divClasses">
      <div
        v-if="props.secondBorder" class="block absolute border rounded-lg" :class="[
          props.secondBorderInset,
          props.borderColor ? `border-${props.borderColor}` : 'border-slate-100',
          props.hoverBorderColor ? `hover:border-${props.hoverBorderColor}` : '',
        ]"
      />
      <component :is="props.prependIcon" v-if="props.prependIcon" class="md:text-xl-bold text-base-bold" />
      <h2 class="text-nowrap">
        {{ props.text ? props.text : $t(props.textKey) }}
      </h2>
      <component :is="props.appendIcon" v-if="props.appendIcon" class="md:text-xl-bold text-base-bold" />
    </div>
  </template>
</template>

<style>

</style>
