<script lang="ts" setup>
const props = defineProps<{
  name: string
  type?: 'email' | 'text' | 'password'
  label?: string
  labelI18?: string
  placeholder?: string
}>()

const { t } = useI18n({ useScope: 'global' })

const model = defineModel()

const label = ref<string>()

function validationMethod() {
  return true
}

onMounted(() => {
  label.value = props.labelI18 !== undefined ? t(props.labelI18) : props.label
})
</script>

<template>
  <div class="md:mb-5">
    <label :for="props.name" class="md:block md:mb-2 md:text-sm-medium md:text-slate-800">{{ label }}</label>
    <Field
      :id="props.name" v-model="model" :type="props.type" :name="props.name" :rules="validationMethod"
      class="md:shadow-sm md:bg-gray-50 md:border md:border-gray-300 md:text-slate-800 md:text-sm md:rounded-lg md:focus:ring-blue-500 md:focus:border-blue-500 md:block md:w-full md:p-2.5"
      :placeholder="props.placeholder"
    />
    <ErrorMessage :name="props.name" />
  </div>
</template>

<style></style>
