<script lang="ts" setup>
import type { RegistrationData } from '@/models/NexUser'
import RegImage from '@/assets/images/reg_image.png'
import { useAuthStore } from '@/stores/auth'
import { toTypedSchema } from '@vee-validate/yup'
import { ArrowRight01Icon } from 'hugeicons-vue'
import { addMethod, defaultLocale, setLocale, object as yupObject, ref as yupRef, string as yupString } from 'yup'
import YupPassword from 'yup-password'

// @ts-expect-error - The types are correct in this point, but TS is not able to infer them
YupPassword({ setLocale, defaultLocale, addMethod, string: yupString })

const { t } = useI18n()
const authStore = useAuthStore()

const currentStep = ref<0 | 1>(0)

// Step 1

const schema = computed(() => {
  return toTypedSchema(
    yupObject({
      first_name: yupString().required().min(5).max(30).label(t('auth.fields.first_name')),
      last_name: yupString().required().min(5).max(30).label(t('auth.fields.last_name')),
      email: yupString().email().required().label(t('auth.fields.email')),
      password: yupString().required().min(8).max(30).minLowercase(1).minUppercase(1).minNumbers(1).minSymbols(1).label(t('auth.fields.password')),
      password_confirmation: yupString().required().oneOf([yupRef('password'), ''], t('auth.organiser_registration.password_mismatch')).label(t('auth.fields.password_confirmation')),
    }),
  )
})

const { validate: validateStep1 } = useForm({
  validationSchema: schema,
})

const first_name = useField<string>('first_name')
const last_name = useField<string>('last_name')
const email = useField<string>('email')
const password = useField<string>('password')
const password_confirmation = useField<string>('password_confirmation')

// Step 2

const schema_step_2 = computed(() => {
  return toTypedSchema(
    yupObject({
      organiser_name: yupString().required().min(5).max(30).label(t('auth.fields.organiser_name')),
      contact_email: yupString().email().required().label(t('auth.fields.contact_email')),
      contact_mobile: yupString().required().test('is-valid-phone', t('errors.invalid_phone'), value => /^\+\d+$/.test(value || '')).label(t('auth.fields.contact_mobile')),
      reg_number: yupString().required().min(5).max(30).label(t('auth.fields.reg_number')),
      vat_number: yupString().matches(/^(?:[A-Z]{2})?\d+$/i, t('errors.invalid_vat')).label(t('auth.fields.vat_number')),
      country: yupString().required().label(t('auth.fields.country')),
    }),
  )
})

const { validate: validateStep2 } = useForm({
  validationSchema: schema_step_2,
})

const organiser_name = useField<string>('organiser_name')
const contact_email = useField<string>('contact_email')
const contact_mobile = useField<string>('contact_mobile')
const reg_number = useField<string>('reg_number')
const vat_number = useField<string>('vat_number')
const country = useField<string>('country')

function getRegistrationData(): RegistrationData {
  return {
    user: {
      email: email.value.value,
      first_name: first_name.value.value,
      last_name: last_name.value.value,
      password: password.value.value,
      type: 'OrganiserAccount',
      organiser_attributes: {
        name: organiser_name.value.value,
        contact_email: contact_email.value.value,
        contact_mobile: contact_mobile.value.value,
        reg_number: reg_number.value.value,
        vat_number: vat_number.value.value,
        state_id: country.value.value,
      },
    },
  }
}

async function onSubmit() {
  if ((await validateStep2()).valid) {
    await authStore.signup(getRegistrationData(), '/organiser')
  }
}

async function handleClick() {
  if (currentStep.value === 0) {
    if ((await validateStep1()).valid) {
      currentStep.value = 1
    }
  }
  else {
    onSubmit()
  }
}

async function handleStepClick(index: number): Promise<void> {
  if (index === 0) {
    currentStep.value = 0
  }
  else {
    if (currentStep.value === 0) {
      if ((await validateStep1()).valid) {
        currentStep.value = 1
      }
    }
  }
}

const showPassword = ref(false)

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}
</script>

<template>
  <div class="grid grid-cols-1 xl:grid-cols-2 gap-12 items-center justify-center mt-8 px-6">
    <img class="hidden xl:block md:max-w-[48rem] md:mx-auto lg:max-w-full" :src="RegImage" alt="Example party photos">

    <div class="flex flex-col gap-10 md:gap-8 lg:gap-6">
      <div class="flex flex-col gap-6">
        <div
          class="rounded-lg border py-3 px-4 bg-pie-100 border-pie-300 text-xl-normal text-pie-950 w-fit"
        >
          {{ $t('auth.registration.welcome') }}
        </div>
        <div class="flex flex-col gap-2 md:gap-0">
          <h1 class="text-6xl md:text-8xl font-extrabold text-pie-700 font-sofia uppercase tracking-[-2%]">
            {{ $t('auth.registration.title') }}
          </h1>
          <p class="text-base-normal md:text-xl-normal text-slate-500">
            {{ $t('auth.registration.subtitle') }}
          </p>
        </div>
      </div>
      <div class="rounded-2xl md:border p-6 md:p-12 bg-pie-25 md:border-slate-400">
        <div class="flex flex-col gap-4 md:gap-8">
          <form class="lg:mt-[-2rem]" @submit.prevent="onSubmit">
            <TicketSelectorCheckoutStepper
              :steps="[
                { label: $t('auth.registration.step_1') },
                { label: $t('auth.registration.step_2') },
              ]"
              :current-step="currentStep"
              @step-click="handleStepClick"
            />
            <div
              v-if="currentStep === 0" class="md:flex md:flex-col md:gap-3
                     md:[&_.v-field]:rounded-lg
                     md:[&_.v-field]:shadow-sm
                     md:[&_.v-field]:bg-white"
            >
              <div class="md:grid md:grid-cols-2 md:gap-4">
                <v-text-field
                  id="first_name" v-model="first_name.value.value" variant="outlined"
                  :label="t('auth.fields.first_name')" append-inner-icon="mdi-account-arrow-left"
                  :error-messages="first_name.errorMessage.value"
                />

                <v-text-field
                  id="last_name" v-model="last_name.value.value" variant="outlined"
                  :label="t('auth.fields.last_name')" append-inner-icon="mdi-account-arrow-right"
                  :error-messages="last_name.errorMessage.value"
                />
              </div>

              <v-text-field
                id="email" v-model="email.value.value" variant="outlined" :label="t('auth.fields.email')"
                append-inner-icon="mdi-email" :error-messages="email.errorMessage.value"
              />

              <v-text-field
                id="password" v-model="password.value.value" variant="outlined" :type="showPassword ? 'text' : 'password'"
                :label="t('auth.fields.password')" :append-inner-icon="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                :error-messages="password.errorMessage.value"
                @click:append-inner="togglePasswordVisibility"
              />

              <v-text-field
                id="password_confirmation" v-model="password_confirmation.value.value" variant="outlined"
                type="password" :label="t('auth.fields.password_confirmation')"
                :append-inner-icon="`mdi-lock${password.value.value === password_confirmation.value.value ? '-check' : '-remove'}`"
                :error-messages="password_confirmation.errorMessage.value"
              />
            </div>
            <div
              v-else
              class="md:flex md:flex-col md:gap-3
                     md:[&_.v-field]:rounded-lg
                     md:[&_.v-field]:shadow-sm
                   md:[&_.v-field]:bg-white"
            >
              <v-text-field
                id="organiser_name" v-model="organiser_name.value.value" variant="outlined"
                :label="t('auth.fields.organiser_name')" :error-messages="organiser_name.errorMessage.value"
              />

              <v-text-field
                id="contact_email" v-model="contact_email.value.value" variant="outlined"
                :label="t('auth.fields.contact_email')" :error-messages="contact_email.errorMessage.value"
              />

              <v-text-field
                id="contact_mobile" v-model="contact_mobile.value.value" variant="outlined"
                :label="t('auth.fields.contact_mobile')" :error-messages="contact_mobile.errorMessage.value"
              />

              <v-text-field
                id="reg_number" v-model="reg_number.value.value" variant="outlined"
                :label="t('auth.fields.reg_number')"
                :error-messages="reg_number.errorMessage.value"
              />

              <v-text-field
                id="vat_number" v-model="vat_number.value.value" variant="outlined" :label="t('auth.fields.vat_number')"
                :error-messages="vat_number.errorMessage.value" optional
              />

              <Autocomplete
                id="country" v-model="country.value.value" label-key="auth.fields.country" search-endpoint="/api/states"
                variant="outlined" :error-messages="country.errorMessage.value" field-width="auto"
              />
            </div>
          </form>
          <div
            class="bg-pie-700 rounded-lg p-1 hover:bg-pie-600 transition-75"
            @click="handleClick"
          >
            <div
              class="rounded-md border py-3 px-6 border-pie-300 text-lg-bold text-pie-50 flex flex-row items-center justify-center gap-1"
            >
              <template v-if="currentStep === 0">
                {{ $t('auth.buttons.next') }}
                <ArrowRight01Icon />
              </template>
              <template v-else>
                {{ $t('auth.buttons.register') }}
              </template>
            </div>
          </div>
          <div class="text-base-normal text-center text-slate-800">
            {{ $t('auth.registration.have_account') }}
            <NuxtLinkLocale
              to="/login"
              class="text-base-bold text-pie-700 hover:underline transition-75"
            >
              {{ $t('auth.registration.sign_in') }}
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(input:-webkit-autofill),
:deep(input:-webkit-autofill:hover),
:deep(input:-webkit-autofill:focus),
:deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #000 !important;
  caret-color: #000;
  transition: background-color 9999s ease-in-out 0s;
}

:deep(.v-field__overlay) {
  background-color: white !important;
}
</style>
