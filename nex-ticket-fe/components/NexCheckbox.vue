<script setup lang="ts">
import { SquareIcon, Tick03Icon } from 'hugeicons-vue'

defineProps({
  required: {
    type: Boolean,
    default: false,
  },
  validated: {
    type: Boolean,
    default: false,
  },
  text: String,
})

const model = defineModel<boolean>()
</script>

<template>
  <div
    class="flex items-center gap-2 cursor-pointer"
    @click="model = !model"
  >
    <div class="relative">
      <div class="relative w-6 h-6 flex transition-colors">
        <SquareIcon
          class="w-6 h-6 absolute inset-0 m-auto transition-opacity duration-100 stroke-2"
          :class=" {
            'text-pie-700 fill-pie-700': model,
            'text-pie-950': !model && !(required && validated),
          }"
          :style="{ color: (!model && validated && required) ? 'rgb(176, 0, 32)' : '' }"
        />
        <Tick03Icon
          v-show="model"
          class="w-4 h-4 md:text-pie-700 absolute inset-0 m-auto transition-opacity duration-100  fill-white "
        />
      </div>
    </div>
    <div class="md:flex md:flex-col">
      <span
        class="text-sm-medium text-slate-600 select-none"
        :style="{ color: (required && !model && validated) ? 'rgb(176, 0, 32)' : '' }"
      >
        {{ text }}
      </span>
      <span v-if="!required" class="text-sm-medium text-slate-400 select-none">({{ $t('public.ticket.checkout.checkBox.optional') }})</span>
    </div>
  </div>
</template>
