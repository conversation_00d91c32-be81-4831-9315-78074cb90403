<script lang="ts" setup>
import { Cancel01Icon, Edit02Icon } from 'hugeicons-vue'
import { useFeedback } from '~/composables/useFeedback'

interface RequestOption {
  method?: 'GET'
  query?: Record<string, string>
}

interface City {
  [key: string]: any // Flexible, but consider a more specific type (e.g., { id: number; name: string; ... })
}

const props = defineProps({
  labelKey: {
    type: String,
    required: true,
  },
  variant: {
    type: String as PropType<'outlined' | 'plain' | 'filled' | 'underlined' | 'solo' | 'solo-inverted' | 'solo-filled' | undefined>,
    default: 'outlined',
  },
  displayFuction: {
    type: Function,
  },
  displayKey: {
    type: String,
    default: 'name',
  },
  valueKey: {
    type: String,
    default: 'id',
  },
})

const model = defineModel<number | null>()

const searchValue = ref<string>('')
const searchItems = ref<{ value: any, title: string }[]>([])
const loading = ref<boolean>(false)
const isOpen = ref<boolean>(false)
const arrowCounter = ref<number>(-1)

const isDataLoaded = ref<boolean>(false) // Tracks if data has been loaded
const shouldDisplayValue = ref<boolean>(false) // Controls display of the model value
const feedback = useFeedback()
const rootElement = ref<HTMLElement | null>(null)
const dropdownList = ref<HTMLElement | null>(null)
const dropdownContent = ref<HTMLElement | null>(null)
const listItems = ref<HTMLElement[]>([])
const inputElement = ref<HTMLInputElement | null>(null)
const inputElementPhone = ref<HTMLInputElement | null>(null)
const textMeasureElement = ref<HTMLSpanElement | null>(null)
const inputWidth = ref<string>('370px')
const isInputHovered = ref<boolean>(false)
const firstLoad = ref<boolean>(true)

const requestOptions = computed(() => {
  const option: RequestOption = { method: 'GET' }
  if (searchValue.value) {
    option.query = {
      q: searchValue.value,
    }
  }
  return option
})

const { t } = useI18n()

onMounted(async () => {
  await loadData()

  document.addEventListener('click', handleClickOutside)

  nextTick(() => {
    updateInputWidth(searchValue.value)
  })
})

onUnmounted(async () => {
  document.removeEventListener('click', handleClickOutside)
})

async function loadData() {
  loading.value = true
  const { data: searchData, error: apiError } = await useAPI('/api/cities', requestOptions.value)

  if (apiError.value) {
    feedback.error(t('errors.fetch_data_error'), { level: 'error', rollbar: true, extras: apiError.value })
    loading.value = false
    isDataLoaded.value = false
    return
  }

  if (searchData.value && Array.isArray(searchData.value)) {
    searchItems.value = searchData.value.map((item: City) => ({
      value: item[props.valueKey],
      title: props.displayFuction ? props.displayFuction(item) : item[props.displayKey].toString().toUpperCase(),
    }))
    isDataLoaded.value = true

    // Check if the current model value exists in the loaded data *after* loading.
    if (model.value) {
      const selectedCity = searchItems.value.find(item => item.value === model.value)
      if (selectedCity) {
        shouldDisplayValue.value = true // Only display if the model value is valid
        if (firstLoad.value) {
          searchValue.value = selectedCity.title
          firstLoad.value = false
        }
      }
    }
  }
  else {
    searchItems.value = []
    isDataLoaded.value = true // Still "loaded" even with no data
  }
  loading.value = false
}

watch(searchValue, () => {
  setTimeout(() => {
    loadData()
  }, 700)
})

// Reset list items array when search items change
watch(searchItems, () => {
  listItems.value = []

  // Give Vue time to update the DOM before trying to access the list items
  nextTick(() => {
    if (isOpen.value && arrowCounter.value >= 0) {
      scrollSelectedItemIntoView()
    }
  })
})

// Update input width when search value changes
watch(searchValue, (newValue) => {
  updateInputWidth(newValue)
})

// Function to calculate and update input width based on text content
function updateInputWidth(text: string) {
  if (!textMeasureElement.value)
    return

  if (!text) {
    inputWidth.value = '370px'
    return
  }

  textMeasureElement.value.textContent = text

  const textWidth = textMeasureElement.value.offsetWidth
  const calculatedWidth = textWidth + 20

  if (calculatedWidth > 1400) {
    inputWidth.value = '1400px'
  }
  else {
    inputWidth.value = `${calculatedWidth}px`
  }
}

watch(model, (newValue) => {
  // When the model changes (e.g., due to parent update), check if we should display
  if (isDataLoaded.value && newValue !== null) {
    const selectedCity = searchItems.value.find(item => item.value === newValue)
    shouldDisplayValue.value = !!selectedCity // Use !! to convert to boolean (true if found, false if not)
  }
  else {
    shouldDisplayValue.value = false // Don't display until data is loaded and the value is valid.
  }
}, { immediate: true }) // Important: Run this watcher immediately

function handleClickOutside(event: Event) {
  if (rootElement.value && !rootElement.value.contains(event.target as Node)) {
    arrowCounter.value = -1
    isOpen.value = false
  }
}

function onArrowDown() {
  if (searchItems.value.length === 0)
    return

  if (!isOpen.value) {
    isOpen.value = true
  }

  if (arrowCounter.value === -1) {
    arrowCounter.value = 0
  }
  else if (arrowCounter.value < (searchItems.value.length - 1)) {
    arrowCounter.value++
  }
  else {
    arrowCounter.value = 0
  }

  nextTick(() => {
    scrollSelectedItemIntoView()
  })
}

function onArrowUp() {
  if (searchItems.value.length === 0)
    return

  if (!isOpen.value) {
    isOpen.value = true
  }

  if (arrowCounter.value > 0) {
    arrowCounter.value--
  }
  else if (arrowCounter.value === 0) {
    arrowCounter.value = searchItems.value.length - 1
  }
  else if (arrowCounter.value === -1 && searchItems.value.length > 0) {
    arrowCounter.value = searchItems.value.length - 1
  }

  nextTick(() => {
    scrollSelectedItemIntoView()
  })
}

function scrollSelectedItemIntoView() {
  if (arrowCounter.value >= 0 && listItems.value[arrowCounter.value] && dropdownContent.value) {
    const selectedItem = listItems.value[arrowCounter.value]
    const container = dropdownContent.value

    const itemTop = selectedItem.offsetTop
    const itemHeight = selectedItem.offsetHeight
    const containerTop = container.scrollTop
    const containerHeight = container.clientHeight

    if (itemTop < containerTop) {
      container.scrollTop = itemTop
      console.warn(`Scrolling up to ${itemTop}`)
    }
    else if (itemTop + itemHeight > containerTop + containerHeight) {
      container.scrollTop = itemTop + itemHeight - containerHeight
      console.warn(`Scrolling down to ${itemTop + itemHeight - containerHeight}`)
    }
  }
  else {
    console.warn(`Cannot scroll: arrowCounter=${arrowCounter.value}, hasListItem=${!!listItems.value[arrowCounter.value]}, hasDropdownContent=${!!dropdownContent.value}`)
  }
}

function onEnter() {
  if (searchItems.value.length > 0 && arrowCounter.value >= 0 && arrowCounter.value < searchItems.value.length) {
    searchValue.value = searchItems.value[arrowCounter.value].title
    model.value = searchItems.value[arrowCounter.value].value
    arrowCounter.value = -1
    isOpen.value = false

    nextTick(() => {
      updateInputWidth(searchValue.value)
    })
  }
}

function clearInput() {
  model.value = null
  searchValue.value = ''
  nextTick(() => updateInputWidth(''))
  isOpen.value = true
}
</script>

<template>
  <div ref="rootElement">
    <p class="md:text-xl-medium text-sm-medium text-slate-500 align-middle">
      {{ t(props.labelKey) }}
    </p>
    <div class="md:relative">
      <!-- Hidden element to measure text width -->
      <span
        ref="textMeasureElement"
        class="absolute invisible whitespace-nowrap font-sofia font-extrabold text-8xl tracking-tighter uppercase"
        aria-hidden="true"
      />

      <!-- Desktop view -->
      <div
        class="md:flex md:gap-4 md:items-center hidden"
        @mouseenter="isInputHovered = true"
        @mouseleave="isInputHovered = false"
      >
        <input
          ref="inputElement"
          v-model="searchValue"
          :style="{ width: inputWidth }"
          class="
          md:h-[5.5rem] md:text-8xl text-pie-700 hover:text-pie-500 font-sofia font-extrabold tracking-tighter align-middle uppercase underline md:underline-offset-8 underline-offset-6
          selection:bg-pie-700 selection:text-pie-50
          placeholder:text-pie-100 placeholder-shown:no-underline
          focus:outline-none focus:ring-0 focus:border-transparent"
          type="text"
          :placeholder="!isDataLoaded ? '' : $t('public.autocomplete.place_holder')"
          @input="isOpen = true"
          @keydown.down="onArrowDown"
          @keydown.up="onArrowUp"
          @keydown.enter="onEnter"
        >

        <transition
          enter-active-class="transition-opacity duration-100 ease-in"
          leave-active-class="transition-opacity duration-100 ease-out"
          enter-from-class="opacity-0"
          leave-to-class="opacity-0"
        >
          <Edit02Icon
            v-show="isInputHovered && searchValue"
            class="md:w-9 md:h-9 md:text-pie-500 md:cursor-pointer "
            @click="clearInput()"
          />
        </transition>
      </div>

      <!-- Mobile view -->
      <div
        class="flex items-center md:hidden"
        @mouseenter="isInputHovered = true"
        @mouseleave="isInputHovered = false"
      >
        <input
          ref="inputElementPhone"
          v-model="searchValue"
          class="
          w-4/6
          h-12 text-5xl text-pie-700 hover:text-pie-500 font-sofia font-extrabold tracking-tighter align-middle uppercase underline md:underline-offset-8 underline-offset-6
          selection:bg-pie-700 selection:text-pie-50
          placeholder:text-pie-100 placeholder-shown:no-underline
          focus:outline-none focus:ring-0 focus:border-transparent"
          type="text"
          :placeholder="!isDataLoaded ? '' : $t('public.autocomplete.place_holder')"
          @input="isOpen = true"
          @keydown.down="onArrowDown"
          @keydown.up="onArrowUp"
          @keydown.enter="onEnter"
        >
        <Edit02Icon
          v-show="searchValue"
          class="w-6 h-6 text-pie-500"
          @click="clearInput()"
        />
      </div>

      <transition
        enter-active-class="transition ease-out duration-150"
        enter-from-class="opacity-0 dissolve"
        enter-to-class="opacity-100 dissolve"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 dissolve"
        leave-to-class="opacity-0 dissolve"
      >
        <ul
          v-show="isOpen"
          ref="dropdownList"
          class="
            md:w-[42.125rem] md:max-h-96 w-[20.5rem] max-h-72 z-50 absolute overflow-hidden
            rounded-lg shadow-lg shadow-pie-700/10 bg-white border border-slate-200"
        >
          <div ref="dropdownContent" class="dropdown-content overflow-auto md:max-h-96 max-h-72">
            <div class="flex flex-row justify-between gap-3 pt-3 px-3">
              <span class="md:text-lg-medium text-base-medium text-slate-600">
                {{ $t('public.autocomplete.matching_cities') }}
              </span>
              <div>
                <Cancel01Icon
                  class="md:hover:bg-slate-200 w-6 h-6 float-right rounded-md"
                  @click="isOpen = false, arrowCounter = -1"
                />
              </div>
            </div>
            <div>
              <li
                v-for="(item, index) in searchItems"
                :key="index"
                :ref="el => { if (el) { listItems[index] = el as HTMLElement; } }"
                class="
                text-left align-middle md:py-3 md:ps-7 py-1 ps-4
                font-sofia font-extrabold md:text-5xl text-2xl tracking-tighter cursor-pointer hover:bg-pie-500 hover:text-slate-200 transition-75"
                :class="{ 'text-slate-200 bg-pie-500': index === arrowCounter, 'text-slate-800': index !== arrowCounter }"
                @click="searchValue = item.title, model = item.value, isOpen = false"
              >
                {{ item.title }}
              </li>
            </div>
          </div>
        </ul>
      </transition>
    </div>
  </div>
</template>

<style>
/* Remove focus border and outline */
input:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

/* Custom scrollbar styling */
.dropdown-content::-webkit-scrollbar {
  width: 8px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: transparent;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background-color: #cbd5e1; /* slate-300 */
  border-radius: 4px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8; /* slate-400 */
}

/* Firefox scrollbar */
.dropdown-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}
</style>
