<script setup lang="ts">
import { ArrowLeft01Icon, ArrowRight01Icon, Cancel01Icon } from 'hugeicons-vue'
import { useFeedback } from '~/composables/useFeedback'

const props = defineProps<{
  photos: { photo: string, url: string }[]
}>()

const scrollContainer = ref<HTMLElement | null>(null)
const selectedPhotoIndex = ref<number | null>(null)
const isScrollable = ref(false)
const feedback = useFeedback()

const currentPhoto = computed(() => {
  if (selectedPhotoIndex.value === null || !props.photos || props.photos.length === 0) {
    return null
  }
  const index = Math.max(0, Math.min(props.photos.length - 1, selectedPhotoIndex.value))
  return props.photos[index]
})

function openPhoto(index: number) {
  if (index >= 0 && index < props.photos.length) {
    selectedPhotoIndex.value = index
    document.body.style.overflow = 'hidden'
  }
  else {
    feedback.error(`Invalid photo index: ${index}`, { level: 'error', rollbar: true })
  }
}

function closePhoto() {
  selectedPhotoIndex.value = null
  document.body.style.overflow = ''
}

function showNext() {
  if (selectedPhotoIndex.value === null || props.photos.length <= 1)
    return
  const newIndex = (selectedPhotoIndex.value + 1) % props.photos.length
  selectedPhotoIndex.value = newIndex
}

function showPrevious() {
  if (selectedPhotoIndex.value === null || props.photos.length <= 1)
    return
  const newIndex = (selectedPhotoIndex.value - 1 + props.photos.length) % props.photos.length
  selectedPhotoIndex.value = newIndex
}

function handleKeydown(event: KeyboardEvent) {
  if (selectedPhotoIndex.value === null)
    return

  switch (event.key) {
    case 'Escape':
      closePhoto()
      break
    case 'ArrowLeft':
      showPrevious()
      break
    case 'ArrowRight':
      showNext()
      break
  }
}

const touchStartX = ref(0)
const touchEndX = ref(0)
const swipeThreshold = 50

function handleTouchStart(event: TouchEvent) {
  if (event.changedTouches.length > 0) {
    touchStartX.value = event.changedTouches[0].screenX
  }
}

function handleTouchEnd(event: TouchEvent) {
  if (event.changedTouches.length > 0) {
    touchEndX.value = event.changedTouches[0].screenX
    handleSwipeGesture()
  }
}

function handleSwipeGesture() {
  const deltaX = touchEndX.value - touchStartX.value
  if (Math.abs(deltaX) > swipeThreshold) {
    if (deltaX < 0) {
      showNext()
    }
    else {
      showPrevious()
    }
  }
  touchStartX.value = 0
  touchEndX.value = 0
}

onMounted(() => {
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})

function getScrollAmount() {
  if (!scrollContainer.value || !scrollContainer.value.children.length)
    return 316

  const firstItem = scrollContainer.value.children[0] as HTMLElement
  const scrollContainerStyle = window.getComputedStyle(scrollContainer.value)
  const itemWidth = firstItem.offsetWidth
  const gap = Number.parseInt(scrollContainerStyle.gap) || 16
  return itemWidth + gap
}

function scrollPrevious() {
  if (scrollContainer.value) {
    scrollContainer.value.scrollBy({
      left: -getScrollAmount(),
      behavior: 'smooth',
    })
  }
}

function scrollNext() {
  if (scrollContainer.value) {
    scrollContainer.value.scrollBy({
      left: getScrollAmount(),
      behavior: 'smooth',
    })
  }
}

function checkScrollability() {
  nextTick(() => {
    if (scrollContainer.value) {
      const isDisplayed = window.getComputedStyle(scrollContainer.value).display !== 'none'
      if (isDisplayed) {
        isScrollable.value = scrollContainer.value.scrollWidth > scrollContainer.value.clientWidth
      }
      else {
        isScrollable.value = false
      }
    }
    else {
      isScrollable.value = false
    }
  })
}

watchEffect(() => {
  const _photos = props.photos
  if (scrollContainer.value) {
    checkScrollability()
  }
})
</script>

<template>
  <section class="space-y-4">
    <div class="relative">
      <div v-if="isScrollable">
        <div class="pointer-events-none absolute inset-y-0 -left-1 w-6 bg-[linear-gradient(to_right,white,transparent)] z-10 block" />
        <div class="pointer-events-none absolute inset-y-0 -right-1 w-6 bg-[linear-gradient(to_left,white,transparent)] z-10 block" />
        <div class="hidden md:absolute md:inset-y-0 md:left-4 md:flex md:items-center md:z-20">
          <button class="transition-basic p-2 rounded-full bg-white/80 hover:bg-white transition-all shadow-md backdrop-blur-sm" aria-label="Scroll previous" @click="scrollPrevious">
            <ArrowLeft01Icon class=" stroke-slate-800" />
          </button>
        </div>
        <div class="hidden md:absolute md:inset-y-0 md:right-4 md:flex md:items-center md:z-20">
          <button class="transition-basic p-2 rounded-full bg-white/80 hover:bg-white transition-all shadow-md backdrop-blur-sm" aria-label="Scroll next" @click="scrollNext">
            <ArrowRight01Icon class=" stroke-slate-800 " />
          </button>
        </div>
      </div>
      <div
        ref="scrollContainer"
        class="
          flex overflow-x-auto gap-2 py-2 scroll-smooth snap-x snap-mandatory
          md:py-0 md:px-0 md:pb-4 md:h-[25rem] md:snap-none
          scrollbar scrollbar-thin scrollbar-track-slate-200 scrollbar-thumb-slate-800
          scrollbar-thumb-rounded-full scrollbar-track-rounded-full
          "
      >
        <div
          v-for="(photo, index) in props.photos"
          :key="index"
          class="
            h-48 w-fit flex-shrink-0 snap-start
            md:h-full md:snap-align-none
            cursor-pointer group
            "
          @click="openPhoto(index)"
        >
          <img
            :src="photo.url"
            class=" h-full w-full rounded-lg object-cover md:w-auto transition-transform duration-200 group-hover:scale-105 "
            :alt="`Gallery photo ${index + 1}`" loading="lazy"
          >
        </div>
      </div>
    </div>
  </section>

  <div
    v-if="selectedPhotoIndex !== null && currentPhoto" class="
      fixed inset-0 z-50
      flex items-center justify-center
      p-4 bg-black/75 backdrop-blur-sm
      "
    role="dialog"
    aria-modal="true"
    :aria-label="`Enlarged view of photo ${selectedPhotoIndex + 1}`" @click="closePhoto"
  >
    <button
      v-if="props.photos.length > 1"
      class="fixed left-4 top-1/2 -translate-y-1/2 z-[52] /* Higher z-index */
               hidden md:flex items-center justify-center /* Desktop only */
               p-2 rounded-full bg-white/60 hover:bg-white transition-all shadow-md"
      style="backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px);"
      aria-label="Previous photo"
      @click="showPrevious"
    >
      <ArrowLeft01Icon class="w-8 h-8 stroke-slate-800" />
    </button>

    <div class="relative" @click.stop>
      <img
        :key="currentPhoto.url" :src="currentPhoto.url" :alt="`Enlarged photo ${selectedPhotoIndex + 1}`" class="
           block max-w-[90vw] max-h-[85vh] w-auto h-auto
           object-contain rounded-lg shadow-xl
           touch-pan-y /* Allow vertical scroll but handle horizontal via swipe */
           "
        @touchstart="handleTouchStart" @touchend="handleTouchEnd" @click.stop
      >
      <button
        class="
           absolute -top-2 -right-2 z-[52] /* Higher z-index */
           p-1 bg-white/80 rounded-full text-slate-800
           hover:bg-white shadow-md transition-colors
           " aria-label="Close photo"
        @click.stop="closePhoto"
      >
        <Cancel01Icon class="stroke-[3px] text-slate-900" />
      </button>
    </div>
    <button
      v-if="props.photos.length > 1"
      class="fixed right-4 top-1/2 -translate-y-1/2 z-[52] /* Higher z-index */
               hidden md:flex items-center justify-center /* Desktop only */
               p-2 rounded-full bg-white/60 hover:bg-white transition-all shadow-md"
      style="backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px);"
      aria-label="Next photo"
      @click="showNext"
    >
      <ArrowRight01Icon class="w-8 h-8 stroke-slate-800" />
    </button>
  </div>
</template>
