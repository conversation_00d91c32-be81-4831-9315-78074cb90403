<script lang="ts" setup>
import { useFeedback } from '~/composables/useFeedback'

const props = defineProps({
  labelKey: {
    type: String,
    required: true,
  },
  variant: {
    type: String as PropType<'outlined' | 'plain' | 'filled' | 'underlined' | 'solo' | 'solo-inverted' | 'solo-filled' | undefined>,
    default: 'outlined',
  },
  searchEndpoint: {
    type: String,
    required: true,
  },
  searchEndpointKey: {
    type: String,
    default: 'q',
  },
  displayFuction: {
    type: Function,
  },
  displayKey: {
    type: String,
    default: 'name',
  },
  valueKey: {
    type: String,
    default: 'id',
  },
  fieldWidth: {
    type: String,
    default: '500',
  },
  errorMessages: {
    type: String || Array<string>,
    required: false,
  },
})
interface RequestOption {
  method?: 'GET'
  query?: Record<string, string>
}

const model = defineModel<string>({
  default: '',
})

const searchValue = ref<string>('')
const searchItems = ref<{ value: any, title: string }[]>([])
const loading = ref<boolean>(false)
const feedback = useFeedback()
const requestOptions = computed(() => {
  const option: RequestOption = { method: 'GET' }
  if (searchValue.value) {
    option.query = {
      [props.searchEndpointKey]: searchValue.value,
    }
  }
  return option
})

onMounted(() => {
  loadData()
})

const { t } = useI18n()

async function loadData() {
  const { data: searchData, error: apiError } = await useAPI(props.searchEndpoint, requestOptions.value)

  if (apiError.value) {
    feedback.error(`${t('errors.fetch_data_error')} ${apiError.value}`, { level: 'error', rollbar: true })
    return
  }

  if (searchData.value && Array.isArray(searchData.value)) {
    searchItems.value = searchData.value.map((item: any) => {
      return {
        value: item[props.valueKey],
        title: props.displayFuction ? props.displayFuction(item) : item[props.displayKey],
      }
    })
  }
}

watch(searchValue, () => {
  setTimeout(() => {
    loadData()
  }, 500)
  loadData()
})
</script>

<template>
  <div>
    <v-autocomplete
      v-model="model"
      v-model:search="searchValue"
      :variant="props.variant"
      :label="t(props.labelKey)"
      :items="searchItems"
      :loading="loading"
      :width="fieldWidth"
      :error-messages="props.errorMessages"
      item-color="slate-800"
      base-color="slate-500"
    />
  </div>
</template>

<style>

</style>
