<script lang="ts" setup>
import 'vue-advanced-cropper/dist/style.css'

const { t } = useI18n()

interface LocalImageType {
  isLoaded: boolean
  uploadFailed: boolean
  rawFile?: string
  url?: string
  file?: File
  key?: string
}

const feedback = useFeedback()

// event creation panel data
const basicSettData = ref<{ name: string, cover_image_key: string, cover_image_url: string, description: string, start_time: Date, end_time: Date }>()
const locationData = ref<{ latitude: number, longitude: number, venue_name: string }>()
const additionalSettData = ref<{
  socials: { platform: string, link: string }[]
  policies: { type: string, details: string }[]
  tag_ids: number[]
}>()
const galleryData = ref<LocalImageType[]>([])
const ticketsData = ref<{
  id: number
  name: string
  max_amount: number
  price: number
  discounts: { percentage: number, start_date: Date, end_date: Date }[]
  description: string
}[]>([])

const basicSettValFunc = ref<() => Promise<boolean>>()
const locationValFunc = ref<() => Promise<boolean>>()
const additionalSettValFunc = ref<() => Promise<boolean>>()
const galleryValFunc = ref<() => Promise<boolean>>()
const ticketsValFunc = ref<() => Promise<boolean>>()

const disabledButtons = ref<boolean | 'prev' | 'next'>('prev')
const currentStep = ref<1 | 2 | 3 | 4 | 5>(1)
const prevStep = ref<1 | 2 | 3 | 4 | 5>(1)
const totalSteps = ref<5>(5)
const serverResponse = ref<null | any>(null)

onMounted(() => {
  preloadFirstTicket()
})

function preloadFirstTicket() {
  ticketsData.value.push({
    id: 0,
    name: '',
    max_amount: 0,
    price: 0,
    discounts: [],
    description: '',
  })
}

function updateButtonState() {
  if (currentStep.value === 1) {
    disabledButtons.value = 'prev'
  }
  else if (currentStep.value === totalSteps.value) {
    disabledButtons.value = 'next'
  }
  else {
    disabledButtons.value = false
  }
}

async function areDataValid(step: number) {
  switch (step) {
    case 1:
      if (basicSettValFunc.value && !await basicSettValFunc.value()) {
        return false
      }
      return true

    case 2:
      if (locationValFunc.value && !await locationValFunc.value()) {
        return false
      }
      return true

    case 3:
      if (additionalSettValFunc.value && !await additionalSettValFunc.value()) {
        return false
      }
      return true

    case 4:
      if (galleryValFunc.value && !await galleryValFunc.value()) {
        return false
      }
      return true

    case 5:
      if (ticketsValFunc.value && !await ticketsValFunc.value()) {
        return false
      }
      return true
  }
}

async function handleNext(next: () => void): Promise<void> {
  if (!await areDataValid(currentStep.value)) {
    return
  }

  prevStep.value = currentStep.value
  currentStep.value += 1
  updateButtonState()
  next()
}

function handlePrev(prev: () => void): void {
  if (currentStep.value > 1) {
    prevStep.value = currentStep.value
    currentStep.value -= 1
  }
  updateButtonState()
  prev()
}

async function handleStepClick() {
  if (!(currentStep.value > prevStep.value) || await areDataValid(prevStep.value)) {
    prevStep.value = currentStep.value
  }
  else {
    currentStep.value = prevStep.value
  }
}

async function createEvent() {
  if (!await areDataValid(currentStep.value)) {
    return
  }

  if (basicSettData.value && locationData.value && additionalSettData.value && galleryData.value && ticketsData.value) {
    const eventData = {
      event: {
        name: basicSettData.value.name,
        description: basicSettData.value.description,
        start_time: basicSettData.value.start_time,
        end_time: basicSettData.value.end_time,
        venue_name: locationData.value.venue_name,
        main_photo: basicSettData.value.cover_image_key,
        latitude: locationData.value.latitude,
        longitude: locationData.value.longitude,
        photo_gallery: galleryData.value.map(photo => photo.key),
        disabled: false,
        social_media_links: additionalSettData.value.socials.map(social => ({
          platform: social.platform.toLowerCase(),
          link: social.link,
        })),
        policies: additionalSettData.value.policies.map(policy => ({
          type: policy.type,
          details: policy.details,
        })),
        tag_ids: additionalSettData.value.tag_ids,
        ticket_types_attributes: ticketsData.value.map(ticket => ({
          name: ticket.name,
          max_amount: ticket.max_amount,
          price: ticket.price,
          discounts: ticket.discounts.map(discount => ({
            percentage: discount.percentage,
            start_date: discount.start_date,
            end_date: discount.end_date,
          })),
          description: ticket.description,
        })),
      },
    }
    try {
      const { error: fetchError } = await useAPI('/api/organiser/events', {
        method: 'POST',
        body: eventData,
      })
      if (fetchError.value) {
        feedback.error(t('errors/event_errors/create_event_error'), { level: 'error', rollbar: true, extras: fetchError.value })
        serverResponse.value = fetchError.value.message
      }
      else {
        navigateToWLocale(`/organiser/events/`)
      }
    }
    catch (err: any) {
      feedback.error(t('errors/unexpected_error'), { level: 'error', rollbar: true, extras: err })
      serverResponse.value = err.message
    }
  }
}
</script>

<template>
  <v-stepper v-model="currentStep" :editable="true" class="md:[&>*]:!shadow-none" :items="['Basic Setting', 'Location', 'Additional Settings', 'Gallery', 'Tickets']" flat @update:model-value="handleStepClick">
    <template #[`item.1`]>
      <EventEditBasicSettingsPanel
        v-model:event-data="basicSettData"
        v-model:validate-func="basicSettValFunc"
      />
    </template>
    <template #[`item.2`]>
      <EventEditLocationPanel
        v-model:event-data="locationData"
        v-model:validate-func="locationValFunc"
      />
    </template>
    <template #[`item.3`]>
      <EventEditAdditionalSetPanel
        v-model:event-data="additionalSettData"
        v-model:validate-func="additionalSettValFunc"
      />
    </template>
    <template #[`item.4`]>
      <EventEditGalleryPanel
        v-model:event-data="galleryData"
        v-model:validate-func="galleryValFunc"
      />
    </template>
    <template #[`item.5`]>
      <EventEditTicketsPanel
        v-model:event-data="ticketsData"
        v-model:validate-func="ticketsValFunc"
      />
    </template>
    <template #[`actions`]="{ prev, next }">
      <v-stepper-actions
        :next-text="t('organiser.create_event.next')"
        :prev-text="t('organiser.create_event.prev')"
        :disabled="disabledButtons"
        @click:next="handleNext(next)"
        @click:prev="handlePrev(prev)"
      />
    </template>
  </v-stepper>
  <v-row>
    <v-col cols="12" class="md:text-center">
      <v-btn v-if="currentStep === totalSteps" large @click="createEvent">
        {{ $t('nav.organiser.create_event') }}
      </v-btn>
      <div v-if="serverResponse">
        <p>{{ serverResponse }}</p>
      </div>
    </v-col>
  </v-row>
</template>

<style scoped>

</style>
