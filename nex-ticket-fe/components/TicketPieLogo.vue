<script setup lang="ts">
defineProps({
  isMobile: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const route = useRoute()
const path = ref<string>(route.path)
const mobileLogoStyleSpec = 'mobile.logo'
const mobileLogoStyle = ref<string>(getCustomStyle(path.value, mobileLogoStyleSpec))

watch(
  () => route.path,
  (newPath, _) => {
    path.value = newPath
    mobileLogoStyle.value = getCustomStyle(path.value, mobileLogoStyleSpec)
  },
)
</script>

<template>
  <svg width="222" height="50" viewBox="0 0 222 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M68.0594 41.1494C67.7118 41.1494 67.5379 40.9598 67.5379 40.5805V15.5963H61.3748C61.0272 15.5963 60.8534 15.4066 60.8534 15.0274V10.6658C60.8534 10.2865 61.0272 10.0969 61.3748 10.0969H79.8641C80.2118 10.0969 80.3856 10.2865 80.3856 10.6658V15.0274C80.3856 15.4066 80.2118 15.5963 79.8641 15.5963H73.701V40.5805C73.701 40.9598 73.543 41.1494 73.227 41.1494H68.0594ZM82.0508 41.1494C81.7032 41.1494 81.5293 40.9598 81.5293 40.5805V10.6658C81.5293 10.2865 81.7032 10.0969 82.0508 10.0969H87.2184C87.566 10.0969 87.7398 10.2865 87.7398 10.6658V40.5805C87.7398 40.9598 87.566 41.1494 87.2184 41.1494H82.0508ZM100.603 41.5287C97.1581 41.5287 94.519 40.6121 92.6859 38.779C90.8527 36.9459 89.9362 34.133 89.9362 30.3403V20.9534C89.9362 17.2239 90.8527 14.4269 92.6859 12.5621C94.519 10.6658 97.1581 9.71761 100.603 9.71761C103.005 9.71761 105.028 10.1759 106.671 11.0925C108.315 12.009 109.516 13.289 110.274 14.9325C111.065 16.576 111.317 18.5198 111.033 20.7638C111.001 21.1747 110.827 21.3801 110.511 21.3801H105.344C104.996 21.3801 104.838 21.1747 104.87 20.7638C104.996 18.9939 104.712 17.6348 104.016 16.6867C103.321 15.7069 102.199 15.217 100.65 15.217C99.1334 15.217 97.9956 15.6911 97.2371 16.6392C96.5101 17.5558 96.1467 19.0413 96.1467 21.0956V30.0558C96.1467 32.1734 96.5259 33.7063 97.2845 34.6545C98.043 35.571 99.165 36.0293 100.65 36.0293C102.294 36.0293 103.432 35.5236 104.064 34.5122C104.728 33.4692 105.012 32.1102 104.917 30.4351C104.886 30.0558 105.044 29.8662 105.391 29.8662H110.511C110.827 29.8662 111.017 30.0558 111.08 30.4351C111.396 33.8485 110.638 36.5508 108.805 38.5419C106.972 40.5331 104.238 41.5287 100.603 41.5287ZM113.794 41.1494C113.447 41.1494 113.273 40.9598 113.273 40.5805V10.6658C113.273 10.2865 113.447 10.0969 113.794 10.0969H118.962C119.31 10.0969 119.483 10.2865 119.483 10.6658V17.54C119.483 18.1721 119.468 18.8832 119.436 19.6734C119.436 20.4319 119.404 21.2063 119.341 21.9964C119.31 22.7865 119.262 23.4977 119.199 24.1298H119.341C119.752 23.2132 120.195 22.2176 120.669 21.143C121.174 20.0369 121.727 18.899 122.328 17.7296L126.974 10.3813C127.069 10.1917 127.274 10.0969 127.59 10.0969H133.564C133.753 10.0969 133.88 10.1601 133.943 10.2865C134.006 10.4129 133.975 10.5868 133.848 10.808L126.405 22.9446L134.796 40.4383C134.891 40.6279 134.907 40.8018 134.844 40.9598C134.781 41.0862 134.654 41.1494 134.465 41.1494H128.254C127.97 41.1494 127.764 41.023 127.638 40.7701L122.091 28.1595L119.483 31.9522V40.5805C119.483 40.9598 119.31 41.1494 118.962 41.1494H113.794ZM135.777 41.1494C135.43 41.1494 135.256 40.9598 135.256 40.5805V10.6658C135.256 10.2865 135.43 10.0969 135.777 10.0969H151.422C151.77 10.0969 151.944 10.2865 151.944 10.6658V15.0274C151.944 15.4066 151.77 15.5963 151.422 15.5963H141.467V22.8498H150.522C150.869 22.8498 151.043 23.0394 151.043 23.4187V27.4958C151.043 27.875 150.869 28.0647 150.522 28.0647H141.467V35.65H151.422C151.77 35.65 151.944 35.8397 151.944 36.2189V40.5805C151.944 40.9598 151.77 41.1494 151.422 41.1494H135.777ZM159.919 41.1494C159.571 41.1494 159.397 40.9598 159.397 40.5805V15.5963H153.234C152.886 15.5963 152.713 15.4066 152.713 15.0274V10.6658C152.713 10.2865 152.886 10.0969 153.234 10.0969H171.723C172.071 10.0969 172.245 10.2865 172.245 10.6658V15.0274C172.245 15.4066 172.071 15.5963 171.723 15.5963H165.56V40.5805C165.56 40.9598 165.402 41.1494 165.086 41.1494H159.919ZM173.91 41.1494C173.562 41.1494 173.389 40.9598 173.389 40.5805V10.6658C173.389 10.2865 173.562 10.0969 173.91 10.0969H181.495C185.035 10.0969 187.643 10.9502 189.318 12.6569C190.993 14.3636 191.831 16.9395 191.831 20.3845C191.831 23.6715 190.977 26.1999 189.27 27.9699C187.564 29.7082 185.004 30.5773 181.59 30.5773H179.599V40.5805C179.599 40.9598 179.425 41.1494 179.078 41.1494H173.91ZM179.599 25.1254H181.495C182.918 25.1254 183.961 24.7461 184.624 23.9876C185.32 23.229 185.667 22.0438 185.667 20.4319C185.667 18.6936 185.32 17.4452 184.624 16.6867C183.961 15.9281 182.918 15.5489 181.495 15.5489H179.599V25.1254ZM193.856 41.1494C193.508 41.1494 193.335 40.9598 193.335 40.5805V10.6658C193.335 10.2865 193.508 10.0969 193.856 10.0969H199.024C199.371 10.0969 199.545 10.2865 199.545 10.6658V40.5805C199.545 40.9598 199.371 41.1494 199.024 41.1494H193.856ZM202.737 41.1494C202.389 41.1494 202.216 40.9598 202.216 40.5805V10.6658C202.216 10.2865 202.389 10.0969 202.737 10.0969H218.382C218.729 10.0969 218.903 10.2865 218.903 10.6658V15.0274C218.903 15.4066 218.729 15.5963 218.382 15.5963H208.426V22.8498H217.481C217.829 22.8498 218.003 23.0394 218.003 23.4187V27.4958C218.003 27.875 217.829 28.0647 217.481 28.0647H208.426V35.65H218.382C218.729 35.65 218.903 35.8397 218.903 36.2189V40.5805C218.903 40.9598 218.729 41.1494 218.382 41.1494H202.737Z" fill="#1C0F69"
      :style="isMobile ? mobileLogoStyle : ''"
    />
    <image href="/assets/images/logo_pie_700.png" />
  </svg>
</template>
