<script lang="ts" setup>
import { Tick04Icon } from 'hugeicons-vue'

interface Step {
  label: string
}

const props = defineProps<{
  steps: Step[]
  currentStep: number
}>()

const emit = defineEmits(['stepClick'])

function getStatus(index: number) {
  if (index < props.currentStep)
    return 'completed'
  if (index === props.currentStep)
    return 'active'
  return 'upcoming'
}
</script>

<template>
  <div id="checkout_stepper" class="w-full px-4 py-4 md:px-0 md:py-8">
    <div class="flex w-full items-center">
      <template v-for="(step, index) in steps" :key="index">
        <div
          class="flex flex-col items-center cursor-pointer md:flex-row md:items-center md:gap-[6px]"
          @click="emit('stepClick', index)"
        >
          <div
            class="flex justify-center items-center w-[24px] h-[24px] rounded-full border-2 md:w-[30px] md:h-[30px]"
            :class="[
              getStatus(index) === 'completed'
                ? 'bg-pie-300 border-pie-300'
                : getStatus(index) === 'active'
                  ? 'bg-pie-700 border-pie-700'
                  : 'border-slate-500 bg-white',
            ]"
          >
            <template v-if="getStatus(index) === 'completed'">
              <Tick04Icon class="w-[12px] h-[12px] text-pie-700 fill-current md:w-[13px] md:h-[13px]" />
            </template>
            <template v-else>
              <span
                class="ml-[1px] md:ml-[0px] text-xs-bold md:text-lg-bold"
                :class="[
                  getStatus(index) === 'active'
                    ? 'text-pie-50'
                    : 'text-slate-500',
                ]"
              >
                {{ index + 1 }}
              </span>
            </template>
          </div>
          <div
            class="text-sm-bold block md:text-lg-bold"
            :class="[
              getStatus(index) === 'upcoming'
                ? 'text-slate-500 md:text-slate-500'
                : 'text-pie-900 md:text-pie-900',
            ]"
          >
            {{ step.label }}
          </div>
        </div>

        <template v-if="index < steps.length - 1">
          <div class="flex-grow bg-slate-400 mx-1 h-[1px] md:mx-2" />
        </template>
      </template>
    </div>
  </div>
</template>
