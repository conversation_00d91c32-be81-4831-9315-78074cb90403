<script lang="ts" setup>
import type NexOrder from '~/models/NexOrder'

const emit = defineEmits(['editClick'])
const checkoutStore = useCheckoutStore()
const order = ref<NexOrder>(checkoutStore.order || {} as NexOrder)
</script>

<template>
  <div class="flex flex-col items-start pt-8 px-8 pb-6 gap-6 backdrop-blur-[1.5px] bg-white/50 border border-slate-400 rounded-xl">
    <!-- Header Section -->
    <div class="flex flex-col w-full gap-3">
      <div class="flex justify-between items-baseline w-full gap-3">
        <h2 class="text-lg-bold text-slate-800">
          {{ $t('public.ticket.checkout.customer_details.shopper_details') }}
        </h2>
        <button class="transition-basic flex items-center gap-3 rounded-lg group" @click="emit('editClick')">
          <div class="flex items-center gap-3 border-b-2 border-slate-800">
            <span class="text-lg-bold text-slate-800">
              {{ $t('public.ticket.checkout.customer_details.edit') }}
            </span>
          </div>
        </button>
      </div>
    </div>

    <!-- Details Section -->
    <div class="flex flex-col w-full gap-3">
      <!-- Email -->
      <div class="flex flex-col gap-1">
        <span class="text-base-medium text-slate-600">
          {{ $t('public.ticket.checkout.customer_details.email') }}
        </span>
        <span class="text-lg-medium text-slate-800">
          {{ order.email }}
        </span>
      </div>

      <!-- First Name -->
      <div class="flex flex-col gap-1">
        <span class="text-base-medium text-slate-600">
          {{ $t('public.ticket.checkout.customer_details.first_name') }}
        </span>
        <span class="text-lg-medium text-slate-800">
          {{ order.first_name }}
        </span>
      </div>

      <!-- Last Name -->
      <div class="flex flex-col gap-1">
        <span class="text-base-medium text-slate-600">
          {{ $t('public.ticket.checkout.customer_details.last_name') }}
        </span>
        <span class="text-lg-medium text-slate-800">
          {{ order.last_name }}
        </span>
      </div>
    </div>
  </div>
</template>
