<script setup lang="ts">
import { ArrowDown01Icon, ArrowUp01Icon } from 'hugeicons-vue'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  maxLines: {
    type: Number,
    default: 2,
  },
})

const expanded = ref(false)
const containerRef = ref<HTMLElement | null>(null)
const isClamped = ref(false)
let observer: ResizeObserver | null = null

function checkClamping() {
  if (!containerRef.value)
    return

  const fullHeight = containerRef.value.scrollHeight
  const computedStyles = window.getComputedStyle(containerRef.value)
  const lineHeight = Number.parseFloat(computedStyles.lineHeight)
  const maxHeight = props.maxLines * lineHeight

  isClamped.value = fullHeight > maxHeight
};

onMounted(() => {
  nextTick(checkClamping)

  observer = new ResizeObserver(() => {
    nextTick(checkClamping)
  })

  if (containerRef.value) {
    observer.observe(containerRef.value)
  }
})

onUnmounted(() => {
  observer?.disconnect()
})

function toggle() {
  if (!isClamped.value)
    return
  expanded.value = !expanded.value
};
</script>

<template>
  <div
    class="text-sm-medium text-slate-500 md:text-sm-medium md:text-slate-500"
    :class="{ clickable: isClamped }"
    @click="toggle"
  >
    <p
      ref="containerRef"
      class="text-wrap rollout"
      :class="{ expanded, clamped: !expanded && isClamped }"
    >
      {{ text }}
    </p>
    <div v-if="isClamped" class="read-more-container">
      <span class="line w-full h-[1px] bg-slate-400" />
      <div class="flex text-sm-medium text-slate-400">
        <span class="text-xs font-medium text-slate-800">{{ expanded ? "Read Less" : "Read More" }}</span>
        <ArrowUp01Icon v-if="expanded" class="text-slate-800" width="16px" height="16px" />
        <ArrowDown01Icon v-else class="text-slate-800" width="16px" height="16px" />
      </div>
      <span class="line w-full h-[1px] bg-slate-400" />
    </div>
  </div>
</template>

<style scoped>
.clickable {
  cursor: pointer;
}

.clamped {
  -webkit-line-clamp: v-bind(maxLines);
  line-clamp: v-bind(maxLines);
}

.rollout {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-break: break-word;
  overflow-wrap: break-word;
}

.expanded {
  -webkit-line-clamp: unset;
  line-clamp: unset;
}

.read-more-container {
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;

}

.line {
  flex-grow: 1;
  width: 99.5px;
}
</style>
