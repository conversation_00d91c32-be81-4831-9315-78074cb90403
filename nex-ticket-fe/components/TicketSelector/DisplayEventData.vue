<script setup lang="ts">
import type NexEvent from '@/models/NexEvent'
import { ArrowTurnBackwardIcon, LinkSquare02Icon } from 'hugeicons-vue'

const props = defineProps<{
  event: NexEvent
}>()

const event = ref<NexEvent>(props.event)

onMounted(() => {
  event.value = props.event
})

const mapUrl = computed(() => {
  return `https://www.google.com/maps?q=${event.value?.latitude},${event.value?.longitude}`
})
</script>

<template>
  <!-- Desktop view -->
  <div class="hidden md:flex md:flex-col md:gap-8">
    <div class="md:flex md:flex-row md:items-stretch md:gap-4">
      <!-- Event Name -->
      <div class="md:flex-1 md:aspect-[2/1] md:h-full  md:box-border md:flex md:flex-col md:gap-6 md:rounded-2xl md:border md:border-slate-400 md:bg-white ">
        <!-- Content container -->
        <div class="md:p-5 md:z-10">
          <div class="md:flex md:flex-col md:items-start md:gap-6 md:z-10">
            <NuxtLinkLocale
              :to="`/events/${event.id}`"
              class="md:flex md:flex-row md:gap-3 md:border-b-2 md:border-slate-800 group md:hover:border-slate-400"
            >
              <ArrowTurnBackwardIcon class="md:text-slate-800 md:group-hover:text-slate-400" :stroke-width="2" />
              <div class="md:text-[36px] md:text-lg-bold md:text-slate-800 md:group-hover:text-slate-400">
                {{ $t('public.ticket.eventData.back_to_event') }}
              </div>
            </NuxtLinkLocale>
            <!-- Title -->
            <div class="md:text-[36px] md:text-4xl-extrabold md:text-slate-700">
              {{ event.name }}
            </div>
          </div>
        </div>
      </div>

      <!-- Main Photo -->
      <div
        class="md:flex-1 md:aspect-[2/1] md:rounded-2xl md:overflow-hidden "
      >
        <img
          :src="event.main_photo.url"
          class=" md:w-full md:h-full md:object-cover"
        >
      </div>
    </div>

    <!-- Event Data -->
    <div
      class="md:flex md:flex-row md:border md:border-slate-400
         md:bg-white md:overflow-hidden md:relative  md:gap-6 md:rounded-2xl md:z-10"
    >
      <DecorationBackground class="absolute" />
      <!-- Content container -->
      <div class="md:flex-1 md:grid md:grid-cols-2 md:z-10  md:p-6">
        <!-- Date & Time -->
        <div class="md:flex md:flex-1 md:flex-col md:gap-1">
          <span class="md:text-base-medium md:text-slate-400">{{ $t('public.ticket.eventData.date') }}</span>
          <div class="md:flex md:flex-col">
            <div class="md:text-2xl-bold md:text-slate-800 md:pr-4">
              {{ event.dateFromTo }}
            </div>
            <div class="md:text-xl-medium md:text-slate-800">
              {{ event.timeFromTo }}
            </div>
          </div>
        </div>

        <!-- Venue -->
        <div class="md:flex md:flex-1 md:flex-col md:gap-1 ">
          <span class="md:text-base-medium md:text-slate-400">{{ $t('public.ticket.eventData.venue') }}</span>
          <div class="md:flex md:flex-col">
            <div class="md:text-2xl-bold md:text-slate-800">
              {{ event.venue_name }}
            </div>
            <div class="md:text-xl-medium md:text-slate-800">
              {{ event.venue_address }}
            </div>
            <div class="md:flex md:flex-row">
              <a
                :href="mapUrl"
                target="_blank"
                class="md:flex md:flex-row md:border-b-2 md:border-slate-800 md:text-slate-800 md:hover:text-slate-400 md:hover:border-slate-400"
              >
                <div class="md:text-lg-bold ">
                  {{ $t('public.ticket.eventData.open_map') }}
                </div>
                <LinkSquare02Icon class="md:ml-3 md:stroke-2" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile view -->
  <div class="md:hidden w-full h-full flex flex-col gap-6">
    <div class="flex h-fit mt-5">
      <!-- Main Photo -->
      <div
        class="w-full h-full aspect-[2/1] rounded-2xl"
      >
        <img
          :src="event.main_photo.url"
          class=" w-full object-cover rounded-2xl h-full"
        >
      </div>
    </div>

    <!-- Event Data -->
    <div class="flex flex-row border border-slate-400 bg-white overflow-hidden relative rounded-2xl z-10">
      <!-- Decorative background element -->
      <div class="z-0 inset-0">
        <svg class="absolute" width="" height="285" fill="none">
          <g style="mix-blend-mode:plus-darker">
            <path d="M29.0024 275.431L29.0435 275.439L29.0847 275.445C71.317 282.218 99.6092 284.346 117.044 282.63C125.662 281.781 132.443 279.93 136.887 276.531C139.213 274.752 140.955 272.503 141.865 269.782C142.763 267.096 142.725 264.327 142.128 261.673C141.147 257.188 138.464 252.548 135.024 247.969C131.519 243.304 126.91 238.293 121.597 233.049C111.005 222.599 97.173 210.802 82.7237 198.479L82.6118 198.384C60.7578 179.746 37.4411 159.849 20.5032 140.767C12.0393 131.231 5.36293 122.111 1.26366 113.656C-2.84299 105.186 -4.13829 97.8592 -2.61794 91.6058L-2.61655 91.6C-0.164092 81.4606 11.9118 75.2197 23.1916 79.2174L23.1993 79.2201L23.2069 79.2228C34.24 83.0928 47.8946 91.6648 63.3069 103.106C78.6339 114.483 95.279 128.373 112.29 142.57L112.35 142.62C129.002 156.517 145.99 170.695 162.193 182.854C178.373 194.996 194.046 205.338 208.066 211.393C221.872 217.357 235.439 219.75 246.35 213.703C257.345 207.61 263.566 194.13 265.673 173.7L265.674 173.686L265.676 173.671C267.293 157.042 259.763 140.367 245.775 130.544L245.767 130.539L245.76 130.534C237.954 125.096 228.684 119.726 219.75 114.551L219.635 114.484C206.74 107.015 194.784 100.06 187.447 93.4521C183.752 90.1244 181.856 87.4198 181.246 85.3561C180.791 83.8143 180.908 82.2595 182.843 80.1454C184.79 78.0855 187.343 76.881 191.114 75.9307C252.337 61.1795 287.551 41.6602 303.868 22.5486C312.095 12.9115 315.801 3.01778 314.994 -6.24829C314.183 -15.5699 308.884 -23.3158 301.024 -28.6992C285.486 -39.3418 259.506 -41.2937 232.343 -30.112C204.966 -18.8419 175.994 5.84305 153.686 49.3413L153.685 49.3449C143.318 69.5987 124.748 85.0708 102.505 89.8266L102.492 89.8292C97.373 90.9372 94.0104 91.0168 91.9527 90.6082C90.1169 90.2437 89.77 89.6224 89.6093 89.2617C89.2875 88.5392 89.0375 86.9038 89.7538 83.7777C90.4465 80.7549 91.8994 76.955 94.1275 72.3957C103.121 53.9925 122.836 26.9127 144.694 -3.07379L144.89 -3.34194C169.763 -37.4641 197.168 -75.0601 212.801 -106.129C220.596 -121.621 225.83 -136.173 225.931 -148.199C225.983 -154.317 224.709 -160.058 221.509 -164.921C218.285 -169.819 213.403 -173.377 207.051 -175.61C192.642 -180.714 177.551 -176.9 166.527 -167.006C153.29 -155.307 136.465 -141.982 120.084 -130.731C103.559 -119.381 87.9675 -110.482 77.0901 -107.218C71.42 -105.517 68.3949 -105.778 67.1532 -106.42C66.7483 -106.63 65.3267 -107.437 65.8699 -112.466C66.4131 -117.494 68.87 -125.143 74.184 -136.101C79.4521 -146.964 87.3526 -160.73 98.4397 -177.889L101.945 -183.314L95.8149 -185.348C50.8214 -200.28 19.7519 -208.519 -0.527838 -211.369C-10.603 -212.785 -18.4992 -212.948 -24.2601 -211.66C-30.1721 -210.337 -34.9335 -207.139 -36.1564 -201.275C-37.2099 -196.222 -35.1965 -190.681 -32.3972 -185.55C-29.4739 -180.193 -25.0684 -174.183 -19.7649 -167.773C-9.17816 -154.976 5.61595 -139.842 21.0813 -124.021L21.2096 -123.89C45.1999 -99.3485 70.8111 -73.1249 86.493 -50.1974C94.3629 -38.6913 99.344 -28.5543 100.657 -20.2538C101.301 -16.1827 101.031 -12.7408 99.9503 -9.83194C98.9145 -7.04432 97.0226 -4.4422 93.8877 -2.10065C90.2654 -0.282877 85.1967 -0.150548 78.1926 -2.36841C71.0481 -4.63074 62.6426 -9.11091 53.1947 -15.3464C34.3061 -27.8127 12.3695 -46.5261 -10.3726 -65.9379L-10.8552 -66.3498C-41.9693 -92.908 -74.7136 -120.858 -101.802 -133.798C-115.357 -140.273 -128.652 -143.574 -140.014 -139.821C-151.806 -135.926 -159.716 -125.114 -163.902 -107.768L-163.923 -107.679L-163.941 -107.59C-165.937 -97.7576 -163.439 -87.7086 -156.664 -80.1181C-135.768 -56.5547 -120.29 -38.9682 -109.487 -25.7441C-98.5195 -12.3172 -92.899 -3.99284 -91.0446 1.20915C-90.1528 3.71081 -90.4624 4.60434 -90.541 4.77958C-90.6083 4.92974 -90.9645 5.58711 -92.9764 6.37429C-94.9886 7.16154 -97.932 7.79158 -102.031 8.33706C-106.082 8.87612 -110.903 9.29103 -116.565 9.77702L-116.63 9.78264C-131.541 11.0625 -151.512 12.7766 -174.764 18.5168L-174.853 18.5386L-174.94 18.5636C-186.683 21.9206 -196.106 32.0933 -196.573 44.9165C-198.401 80.1945 -196.848 103.46 -192.258 117.177C-189.957 124.056 -186.621 129.366 -181.736 132.17C-176.632 135.099 -171.023 134.644 -165.783 132.55C-160.634 130.493 -155.313 126.675 -149.975 121.903C-144.587 117.086 -138.916 111.051 -133.081 104.265C-121.445 90.7314 -108.846 73.8337 -96.215 56.8938L-96.1397 56.7928C-78.2803 32.8407 -60.4112 8.89626 -44.5443 -6.85205C-36.5742 -14.7626 -29.5217 -20.1766 -23.5931 -22.6325C-20.6845 -23.8375 -18.2854 -24.2284 -16.3134 -24.0404C-14.4248 -23.8603 -12.6377 -23.1258 -10.8763 -21.5463C-2.45062 -13.917 1.71488 -6.53069 3.0963 0.696661C4.48508 7.96221 3.18166 15.7007 -0.494482 24.2305C-7.9813 41.6025 -24.5046 60.5968 -43.8735 82.763L-44.0096 82.9188C-62.7856 104.407 -83.8281 128.488 -99.8019 155.725C-116.574 184.199 -123.633 207.413 -123.213 224.713C-123.002 233.417 -120.888 240.834 -116.932 246.597C-112.939 252.413 -107.267 256.247 -100.552 258.005C-87.3402 261.462 -70.7188 256.814 -54.2112 245.418C-37.5473 233.913 -20.3167 215.109 -5.49771 188.762L-5.46979 188.712L-5.44301 188.662C-0.224714 178.869 10.4371 173.616 20.9604 175.88L20.9722 175.882L20.9839 175.885C25.1908 176.769 27.3275 178.49 28.5369 180.425C29.8538 182.532 30.5237 185.619 30.3384 189.949C29.9606 198.776 26.2224 210.117 22.1841 222.22L22.0138 222.731C18.4374 233.448 14.6338 244.847 14.0703 254.016C13.7806 258.73 14.2835 263.568 16.7065 267.6C19.2686 271.863 23.5011 274.455 29.0024 275.431Z" stroke="#E9E8FD" stroke-width="10" />
          </g>
        </svg>
      </div>

      <!-- Content container -->
      <div class="flex flex-col z-10 px-4 py-5 gap-6">
        <!-- Back to event -->
        <NuxtLinkLocale
          :to="`/events/${event.id}`"
          class="flex flex-row gap-3 border-b-2 border-slate-800 group w-fit"
        >
          <ArrowTurnBackwardIcon class="text-slate-800 group-hover:text-slate-600" :stroke-width="2" />
          <div class=" text-base-bold text-slate-800 group-hover:text-slate-600">
            {{ $t('public.ticket.eventData.back_to_event') }}
          </div>
        </NuxtLinkLocale>

        <!-- Name of the event -->
        <div class="text-4xl-extrabold text-slate-700">
          {{ event.name }}
        </div>

        <!-- Date & Time -->
        <div>
          <span class="text-xs-medium text-slate-400">{{ $t('public.ticket.eventData.date') }}</span>
          <div class="flex flex-col">
            <div class="text-lg-bold text-slate-800 pr-4">
              {{ event.dateFromTo }}
            </div>
            <div class="text-base-medium text-slate-800">
              {{ event.timeFromTo }}
            </div>
          </div>
        </div>

        <!-- Venue -->
        <div class="flex flex-col ">
          <span class="text-xs-medium text-slate-400">{{ $t('public.ticket.eventData.venue') }}</span>
          <div class="flex flex-col">
            <div class="text-lg-bold text-slate-800">
              {{ event.venue_name }}
            </div>
            <div class="text-base-medium text-slate-800">
              {{ event.venue_address }}
            </div>
            <div class="flex flex-row">
              <a :href="mapUrl" class="flex flex-row border-b-2 border-slate-800 text-slate-800 hover:text-slate-600">
                <div class="text-lg-bold ">
                  {{ $t('public.ticket.eventData.open_map') }}
                </div>
                <LinkSquare02Icon class="ml-3 stroke-2" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
