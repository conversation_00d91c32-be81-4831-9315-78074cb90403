<script lang="ts" setup>
import type NexEvent from '~/models/NexEvent'

const route = useRoute()
const event = ref<NexEvent | null>(null)

const checkoutStore = useCheckoutStore()
const { order } = storeToRefs(checkoutStore)

const singleEventStore = useSingleEventStore()
const { loading } = storeToRefs(singleEventStore)
event.value = await singleEventStore.getEvent(Number(route.params.id), false)
</script>

<template>
  <!-- main content -->
  <div v-if="!loading">
    <!-- Summary Section -->
    <div class="flex flex-col md:flex-col">
      <div
        class="ml-7 text-5xl font-extrabold font-sofia text-pie-700 z-10 tracking-tighter
               md:ml-8 md:text-7xl md:font-extrabold md:font-sofia md:text-pie-700 md:tracking-tighter"
      >
        {{ $t('public.ticket.checkout.summary') }}
      </div>
      <div
        class="rounded-2xl -mt-6 border border-slate-400 bg-white/50 p-6 backdrop-blur-[1.5px]
               md:rounded-2xl md:border  md:p-6  md:-mt-8 z-20"
      >
        <div class="flex flex-col">
          <p class="text-xs-normal text-red-600">
            {{ formatOrderDate(event?.start_time || new Date()) }}
          </p>
          <p class="text-sm-bold md:text-lg-bold text-slate-900">
            {{ event?.name }}
          </p>
          <p class="text-xs-medium text-slate-500">
            {{ event?.venue_address }}
          </p>
        </div>

        <div v-if="order?.promocode" class="my-4 h-px w-full bg-slate-400" />
        <!-- Discount -->
        <div v-if="order?.promocode" class="flex justify-between">
          <div class="text-sm-normal md:text-lg-normal text-slate-900">
            <span>Promo code: {{ order.promocode }}</span>
          </div>
          <div class="text-sm-normal md:text-lg-normal text-slate-900">
            <span>{{ `- ${formatCurrency(order?.promo_discount_amount, order.currency_code)} ` }}</span>
          </div>
        </div>

        <div class="my-4 h-px w-full bg-slate-400" />

        <div class="flex flex-col gap-1">
          <!-- Items list -->
          <div v-for="(item, index) in order?.order_items" :key="index" class="flex justify-between">
            <div class="text-sm-normal md:text-lg-normal text-slate-900">
              {{ item.quantity }}x {{ item.name }}<span v-if="item.order_itemable_type === 'TicketType'"> {{ $t('public.ticket.checkout.ticket') }}</span>
            </div>
            <span class="text-sm-normal md:text-lg-normal text-slate-900">{{ formatCurrency(item.total_price, order?.currency_code) }}</span>
          </div>

          <div class="my-4 h-px w-full bg-slate-400" />

          <!-- Subtotal + VAT -->
          <div class="flex justify-between">
            <span class="text-sm-normal md:text-lg-normal text-slate-900">{{ $t('public.ticket.checkout.subtotal') }}</span>
            <span class="text-sm-normal md:text-lg-normal text-slate-900">{{ formatCurrency(order?.subtotal_without_vat, order?.currency_code) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm-normal md:text-lg-normal text-slate-900">{{ $t('public.ticket.checkout.vat') }} {{ order?.formattedVatPercentage }}%</span>
            <span class="text-sm-normal md:text-lg-normal text-slate-900">{{ formatCurrency(order.vat_value, order?.currency_code) }}</span>
          </div>

          <div class="mt-4 h-px w-full bg-slate-400" />

          <!-- Total -->
          <div class="mt-4 flex justify-between">
            <span class="text-sm-bold md:text-lg-bold text-slate-900">{{ $t('public.ticket.checkout.total') }}</span>
            <span class="md:text-lg-bold text-slate-900">{{ formatCurrency(order.total, order?.currency_code) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- placeholders while laoding -->
  <div v-else>
    <div class="flex flex-col md:flex-col">
      <div
        class="ml-7 text-5xl font-extrabold font-sofia text-pie-700 z-10 tracking-tighter
               md:ml-8 md:text-7xl md:font-extrabold md:font-sofia md:text-pie-700 md:tracking-tighter"
      >
        {{ $t('public.ticket.checkout.summary') }}
      </div>
      <div
        class="rounded-2xl -mt-6 border border-slate-400 bg-pie-100/50 p-6 backdrop-blur-[1.5px]
               md:rounded-2xl md:border  md:p-6  md:-mt-8 z-20"
      >
        <div class="flex flex-col gap-0.5">
          <div class="placeholder animate-pulse h-4 w-1/4" />
          <div class="placeholder animate-pulse md:h-6 h-4 w-1/4" />
          <div class="placeholder animate-pulse h-4 w-1/2" />
        </div>

        <div class="my-4 h-px w-full bg-slate-400" />

        <div class="flex flex-col gap-1">
          <!-- Items list -->
          <div class="flex justify-between pt-1">
            <div class="placeholder animate-pulse md:h-6 h-4 w-1/2" />
            <div class="placeholder animate-pulse md:h-6 h-4 w-1/3" />
          </div>

          <div class="my-4 h-px w-full bg-slate-400" />

          <!-- Subtotal + VAT -->
          <div class="flex justify-between">
            <span class="text-sm-normal md:text-lg-normal text-slate-900">{{ $t('public.ticket.checkout.subtotal') }}</span>
            <div class="placeholder animate-pulse md:h-6 h-4 w-2/5" />
          </div>
          <div class="flex justify-between pb-1">
            <div class="placeholder animate-pulse md:h-6 h-4 w-1/6" />
            <div class="placeholder animate-pulse md:h-6 h-4 w-2/5" />
          </div>

          <div class="mt-4 h-px w-full bg-slate-400" />

          <!-- Total -->
          <div class="mt-4 flex justify-between pb-1 md:pb-0">
            <span class="text-sm-bold md:text-lg-bold text-slate-900">{{ $t('public.ticket.checkout.total') }}</span>
            <div class="placeholder animate-pulse md:h-6 h-4 w-2/5" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
