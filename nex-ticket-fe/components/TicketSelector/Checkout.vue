<script lang="ts" setup>
import type StripeCheckout from './StripeCheckout.vue'
import NexOrder from '@/models/NexOrder'
import { useFeedback } from '~/composables/useFeedback'

const emit = defineEmits(['stepBackEvent'])

const feedback = useFeedback()

const stripeCheckoutRef = ref<InstanceType<typeof StripeCheckout> | null>(null)
const promoCode = ref('')
const isInputFocused = ref(false)
const checkoutStore = useCheckoutStore()
const order = ref<NexOrder>(checkoutStore.order || {} as NexOrder)

const paymentGateContainerRef = ref<HTMLElement | null>(null)

function handlePay() {
  if (paymentGateContainerRef.value) {
    const offset = 500
    const elementPosition = paymentGateContainerRef.value.offsetTop + offset
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth',
    })
  }
  stripeCheckoutRef.value?.handlePay()
}

async function applyPromoCode() {
  const promoCodeTrimmed = String(promoCode.value).trim()
  if (!promoCode.value || promoCodeTrimmed === '') {
    const errorMsg = 'A valid Promo Code is required to apply.'
    feedback.error(`applyPromoCode Error: ${errorMsg}`, { level: 'error', rollbar: true })
    return { success: false, error: errorMsg, data: null }
  }

  try {
    const { data: responseData, error: responseError, status: responseStatus } = await useAPI(`/api/public/orders/${order.value.id}/apply_promo_code`, {
      method: 'PATCH',
      body: {
        promo_code: promoCodeTrimmed,
      },
    })

    if (responseError.value) {
      let errorMessage = 'Failed to apply promo code.'
      if (responseError.value.message) {
        errorMessage = responseError.value.message
      }

      feedback.error(`applyPromoCode Error (${responseStatus.value}): ${errorMessage}`, { level: 'error', rollbar: true, extras: responseError.value })
      return { success: false, error: errorMessage, data: null }
    }
    const updatedOrder = NexOrder.create_from_request(responseData.value) as NexOrder
    updatedOrder.promocode = promoCode.value
    checkoutStore.setOrder(updatedOrder)
    feedback.success('Promo code applied successfully!')
  }
  catch (err: unknown) {
    feedback.error(`applyPromoCode Unexpected Error: ${err}`, { level: 'error', rollbar: true })
  }
}

const itemsWithAlternatingRotation = computed(() => {
  return order.value.order_items.map((item, index) => {
    const magnitude = getRandomMagnitude(1, 3)
    const direction = (index % 2 === 0) ? -1 : 1
    const finalRotation = magnitude * direction

    return {
      ...item,
      rotation: finalRotation,
    }
  })
})

function handleEditClick() {
  emit('stepBackEvent')
}
</script>

<template>
  <div
    class="flex flex-col gap-8 py-8 md:px-8 px-4
           md:flex-row"
  >
    <!-- Paymentgate -->
    <div class="flex flex-col flex-1 md:self-start gap-16">
      <div class="flex flex-col gap-5">
        <TicketSelectorOrderSummary
          class="md:hidden"
          :promo-code="promoCode"
        />
        <TicketSelectorCustomerDetails @edit-click="handleEditClick" />
      </div>

      <div class="w-full space-y-4 px-2">
        <div
          v-for="(item, index) in itemsWithAlternatingRotation"
          :key="item.id"
          class="w-full h-28 md:h-32 transition-transform duration-150 ease-in-out -rotate-3"
        >
          <TicketSelectorTicketInfoNew
            :ticket="item"
            :index="index + 1"
            @edit-click="handleEditClick"
          />
        </div>
      </div>

      <div>
        <div
          class="ml-8 text-5xl uppercase font-sofia z-10 font-extrabold tracking-tighter text-pie-700
                 md:text-7xl"
        >
          {{ $t('public.ticket.checkout.payment.payment') }}
        </div>
        <div
          class="flex flex-col -mt-7 backdrop-blur-[1.5px] pb-9 pt-6 px-4 gap-6
                 md:flex md:flex-col md:self-start md:border border-slate-400 md:rounded-2xl md:px-8 md:pt-8 md:pb-12   md:-mt-5 md:z-20 bg-white/50 w-full"
        >
          <div class="flex flex-col md:flex md:flex-col gap-3">
            <span class="text-sm-bold md:text-lg-bold"> {{ $t('public.ticket.checkout.payment.promo_question') }} </span>
            <div class="flex flex-row gap-2 w-full">
              <div class="flex-1 md:py-0.5">
                <div class="relative h-full border border-slate-400 rounded-lg bg-white">
                  <input
                    v-model="promoCode"
                    class="w-full h-full px-4 py-2 bg-transparent rounded-lg md:text-base-medium"
                    @focus="isInputFocused = true"
                    @blur="isInputFocused = false"
                  >
                  <div
                    v-if="!promoCode && !isInputFocused"
                    class="absolute inset-0 flex items-center px-4 pointer-events-none space-x-2"
                  >
                    <span class="text-base-medium text-slate-600">{{ $t('public.ticket.checkout.payment.promo_code') }}</span>
                    <span class="text-base-medium text-slate-400 lowercase">({{ $t('public.ticket.checkout.payment.optional') }})</span>
                  </div>
                </div>
              </div>
              <div class="ml-auto">
                <button
                  class="bg-pie-700 text-white text-lg-bold rounded-lg px-6 h-14 transition-basic"
                  @click="applyPromoCode"
                >
                  {{ $t('public.ticket.checkout.payment.apply') }}
                </button>
              </div>
            </div>
          </div>
          <div ref="paymentGateContainerRef" class="flex flex-col md:flex md:flex-col bg-white rounded-2xl p-4">
            <TicketSelectorStripeCheckout ref="stripeCheckoutRef" />
          </div>
        </div>
      </div>
    </div>
    <div class="hidden md:flex md:flex-1 md:flex-col md:self-start md:gap-12 md:sticky md:top-48">
      <TicketSelectorOrderSummary />
      <!-- Payment button -->
      <button
        :disabled="!stripeCheckoutRef?.paymentReady"
        class="md:px-1 md:py-1 md:gap-3 md:rounded-lg transition-basic"
        :class="stripeCheckoutRef?.paymentReady ? 'md:bg-pie-700 md:hover:bg-pie-600' : 'md:bg-pie-300'"
        @click="handlePay"
      >
        <div
          class="md:border md:border-white md:rounded-lg md:px-12 md:py-2"
        >
          <div
            id="pay-button"
            class="text-slate-100 text-nowrap text-lg-bold md:text-xl-bold"
          >
            {{ `${$t('public.ticket.stripeCheckout.pay')} ${formatCurrency(order.total, order.currency_code)}` }}
          </div>
        </div>
      </button>
    </div>
  </div>

  <!-- Mobile payment button -->
  <div class="bg-white w-screen md:w-full border-t border-slate-300 sticky bottom-0 px-4 py-3 md:hidden">
    <!-- Payment button -->
    <button
      :disabled="!stripeCheckoutRef?.paymentReady"
      class="p-1 rounded-lg gap-3 transition-basic w-full"
      :class="stripeCheckoutRef?.paymentReady ? 'bg-pie-700 hover:bg-pie-600' : 'bg-pie-300'"
      @click="handlePay"
    >
      <div
        class="border border-slate-300 rounded-lg px-6 py-3 "
      >
        <div
          id="pay-button"
          class="text-slate-100 text-nowrap text-lg-bold"
        >
          {{ `${$t('public.ticket.stripeCheckout.pay')} ${formatCurrency(order.total, order.currency_code)}` }}
        </div>
      </div>
    </button>
  </div>
</template>
