<script lang="ts" setup>
import type NexOrderItem from '~/models/NexOrderItem'

const props = defineProps<{
  ticket: NexOrderItem
  index: number
}>()

const emit = defineEmits(['editClick'])
</script>

<template>
  <div>
    <TicketSelectorEmptyTicket>
      <div class="flex overflow-hidden h-full w-full relative shadow-xl">
        <div class="h-full w-full flex">
          <!-- Main Content Section -->
          <div class="flex p-[7px] relative w-[75%] h-full">
            <div class="flex relative w-full h-full">
              <div class="w-full h-full relative">
                <!-- Content -->
                <div class="flex pl-8 flex-col w-full h-full justify-center ">
                  <div class="text-lg-bold text-slate-700">
                    {{ $t('public.ticket.checkout.payment.ticket') }} {{ props.index }} - {{ props.ticket.name }} {{ $t('public.ticket.checkout.payment.ticket') }}
                  </div>
                  <div class="text-lg-medium text-slate-500">
                    {{ $t('public.ticket.checkout.payment.quantity') }}: {{ ticket.quantity }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Side Button Section -->
          <div class="bg-pie-700 w-[25%] h-full">
            <div class="flex p-[7px] bg-pie-700  w-full h-full">
              <div class="flex relative w-full h-full">
                <div class="flex items-center justify-center w-full h-full">
                  <!-- Button Content -->
                  <button class="flex items-center justify-center z-10" @click="emit('editClick')">
                    <div>
                      <span class="text-base-bold text-white">{{ $t('public.ticket.checkout.payment.edit') }}</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TicketSelectorEmptyTicket>
  </div>
</template>
