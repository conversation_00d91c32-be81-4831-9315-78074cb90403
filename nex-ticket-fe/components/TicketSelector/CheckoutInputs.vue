<script setup lang="ts">
import NexOrder from '@/models/NexOrder'

import { boolean as yupBoolean, object as yupObject, string as yupString } from 'yup'
import NexCheckbox from '../NexCheckbox.vue'

const emit = defineEmits(['paymentButtonClicked', 'backButtonClicked'])

const { t } = useI18n({ useScope: 'global' })

const orderDetailsSchema = yupObject({
  first_name: yupString().required(t('public.ticket.checkout.messages.first_name_required')).label(t('public.ticket.checkout.first_name')),
  last_name: yupString().required(t('public.ticket.checkout.messages.last_name_required')).label(t('public.ticket.checkout.last_name')),
  email_address: yupString().email(t('public.ticket.checkout.messages.invalid_email')).required(t('public.ticket.checkout.messages.email_required')).label(t('public.ticket.checkout.email')),
  user_agreement: yupBoolean().required().oneOf([true]),
})

const { validate: validateOrderDetails } = useForm({
  validationSchema: orderDetailsSchema,
})

const first_name = useField<string>('first_name')
const last_name = useField<string>('last_name')
const email_address = useField<string>('email_address')
const user_agreement = useField<boolean>('user_agreement')
const subscribe = ref<boolean>(false)

const checkoutStore = useCheckoutStore()
const updateOrdererror = ref<string | null>(null)

const formContainer = ref<HTMLElement | null>(null) // Add this line

const validatedInput = ref<boolean>(false)

onMounted(() => {
  if (checkoutStore.order?.first_name) {
    first_name.value.value = checkoutStore.order.first_name
  }
  if (checkoutStore.order?.last_name) {
    last_name.value.value = checkoutStore.order.last_name
  }
  if (checkoutStore.order?.email) {
    email_address.value.value = checkoutStore.order.email
  }
})

async function updateOrder() {
  updateOrdererror.value = null
  try {
    const { data: responseData, error: responseError } = await useAPI(`/api/public/orders/${checkoutStore.order?.id}`, {
      method: 'PUT',
      body: {
        first_name: first_name.value.value,
        last_name: last_name.value.value,
        email: email_address.value.value,
        agreed_to_terms_and_cond: user_agreement.value.value,
        subscribe: subscribe.value,
      },
    })

    if (responseError.value) {
      updateOrdererror.value = responseError.value.message
      return
    }

    const order = NexOrder.create_from_request(responseData.value) as NexOrder
    checkoutStore.setOrder(order)
  }
  catch (err: unknown) {
    updateOrdererror.value = err instanceof Error ? err.message : t('errors.event_errors.update_event_fail')
  }
}

async function handlePaymentButton() {
  const { valid } = await validateOrderDetails()
  validatedInput.value = true
  if (!valid) {
    if (formContainer.value) {
      const offset = 100 // Adjust this value as needed
      const elementPosition = formContainer.value.offsetTop - offset
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth',
      })
    }
    return
  }
  await updateOrder()
  if (updateOrdererror.value) {
    return
  }
  emit('paymentButtonClicked')
}
</script>

<template>
  <!-- Main Container with bottom padding for button spacing -->
  <div
    class="flex flex-col gap-16 mt-8 px-4 py-8
           md:flex-row md:gap-4 md:px-0 "
  >
    <!-- Mobile summary -->
    <TicketSelectorOrderSummary class="md:hidden" />

    <!-- Checkout Form -->
    <div class="flex flex-1 md:flex md:flex-1 md:flex-col md:w-full md:gap-12 ">
      <div class="flex-col md:flex md:flex-col">
        <div
          class="ml-7 text-5xl font-extrabold font-sofia text-pie-700 z-10 tracking-tighter
                 md:ml-8 md:text-7xl md:font-extrabold md:font-sofia"
        >
          {{ $t('public.ticket.checkout.checkoutForm.checkout') }}
        </div>
        <div
          ref="formContainer"
          class="-mt-6 rounded-2xl border border-slate-400 bg-white/50 p-6 flex flex-col backdrop-blur-[1.5px] z-20 gap-6 md:-mt-8"
        >
          <div class="text-lg-bold text-slate-800">
            {{ $t('public.ticket.checkout.checkoutForm.shop_detail') }}
          </div>

          <div class="space-y-4">
            <div class="space-y-1">
              <VTextField
                id="first_name"
                v-model="first_name.value.value"
                :label="$t('public.ticket.checkout.customer_details.first_name')"
                :placeholder="$t('public.ticket.checkout.customer_details.first_name')"
                :error-messages="first_name.errorMessage.value"
                variant="outlined"
                required
                class="[&_.v-field]:rounded-lg [&_.v-field]:border-slate-400 [&_.v-field]:bg-white [&_.v-field]:text-base-medium [&_.v-field]:text-slate-900"
              />
            </div>
            <div class="space-y-1">
              <VTextField
                id="last_name"
                v-model="last_name.value.value"
                :label="$t('public.ticket.checkout.customer_details.last_name')"
                :placeholder="$t('public.ticket.checkout.customer_details.last_name')"
                :error-messages="last_name.errorMessage.value"
                variant="outlined"
                required
                class="[&_.v-field]:rounded-lg [&_.v-field]:border-slate-400 [&_.v-field]:bg-white [&_.v-field]:text-base-medium [&_.v-field]:text-slate-900"
              />
            </div>
            <div class="space-y-1">
              <VTextField
                id="email_address"
                v-model="email_address.value.value"
                :label="$t('public.ticket.checkout.customer_details.email_address')"
                :placeholder="$t('public.ticket.checkout.customer_details.email_address')"
                :error-messages="email_address.errorMessage.value"
                variant="outlined"
                required
                class="[&_.v-field]:rounded-lg [&_.v-field]:border-slate-400 [&_.v-field]:bg-white [&_.v-field]:text-base-medium [&_.v-field]:text-slate-900"
              />
            </div>
          </div>

          <NexCheckbox
            id="user_agreement"
            v-model="user_agreement.value.value"
            :text="t('public.ticket.checkout.checkBox.agreement')"
            :validated="validatedInput"
            :required="true"
          />
          <NexCheckbox
            v-model="subscribe"
            :text="t('public.ticket.checkout.checkBox.news')"
          />
        </div>
      </div>
    </div>

    <!-- Summary Section -->
    <div class="hidden md:flex md:flex-1 md:flex-col md:w-full md:self-start md:gap-12 md:sticky md:top-48">
      <TicketSelectorOrderSummary />
      <!-- Payment button -->
      <button
        class=" md:p-1 md:transition-basic md:gap-3 md:bg-pie-700 md:rounded-lg md:hover:bg-pie-600 bottom-0"
        @click="handlePaymentButton"
      >
        <div
          class="border border-white rounded-lg
                     md:border md:border-white md:rounded-lg md:px-12 md:py-3"
        >
          <div
            class="text-slate-100 text-nowrap text-lg-bold md:text-xl-bold"
          >
            {{ $t('public.ticket.checkout.buttons.continue_pay') }}
          </div>
        </div>
      </button>
    </div>
  </div>
  <!-- Mobile payment button -->
  <div class="bg-white w-screen md:w-full sticky bottom-0 px-4 py-3 mt-8 border-t border-slate-300 transition-basic md:hidden">
    <!-- Payment button -->
    <button
      class="flex-1 p-1 gap-3 bg-pie-700 rounded-lg w-full"
      @click="handlePaymentButton"
    >
      <div
        class="border border-white rounded-lg px-6 py-3"
      >
        <div
          class="text-slate-100 text-nowrap text-lg-bold"
        >
          {{ $t('public.ticket.checkout.buttons.continue_pay') }}
        </div>
      </div>
    </button>
  </div>
</template>
