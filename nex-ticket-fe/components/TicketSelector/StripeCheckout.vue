<script setup lang="ts">
import { loadStripe, type Stripe, type StripeCheckout } from '@stripe/stripe-js'

const { t } = useI18n()

const feedback = useFeedback()

const publishableKey = useNuxtApp().$config.public.stripeKey
const stripe = ref<Stripe | null>(null)
const clientSecret = ref<string | null>(null)
const paymentElementRef = ref<HTMLElement | null>(null)
const paymentReady = ref(false)
const errorMessage = ref<string | null>(null)
const checkout = ref<StripeCheckout | null>(null)

const checkoutStore = useCheckoutStore()
const order = checkoutStore.order

defineExpose({
  handlePay,
  paymentReady,
})

onMounted(async () => {
  try {
    stripe.value = await loadStripe(publishableKey, { betas: ['custom_checkout_beta_5'] })
  }
  catch (err) {
    errorMessage.value = t('errors.payment_errors.load_payment_form_fail')
    feedback.error(t('errors.payment_errors.payment_element_error'), { level: 'error', rollbar: true, extras: err })
  }
  await createCheckoutSession()
  await initializePaymentElement()
})

async function createCheckoutSession() {
  try {
    const { data, error } = await useAPI('/api/public/create_stripe_checkout_session', {
      method: 'POST',
      body: {
        order_id: checkoutStore.order?.order_id,
      },
    })

    if (error.value) {
      throw new Error(error.value.message)
    }

    clientSecret.value = (data.value as { id: string }).id
    const checkoutResult = await stripe.value?.initCheckout({ clientSecret: clientSecret.value })
    if (checkoutResult) {
      checkout.value = checkoutResult
      if (order?.email) {
        checkout.value?.updateEmail(order.email)
      }
      paymentReady.value = true
    }
  }
  catch (err) {
    feedback.error(t('errors.payment_errors.create_checkout_error'), { level: 'error', rollbar: true, extras: err })
    errorMessage.value = t('errors.payment_errors.initialize_payment_fail')
    throw err
  }
}

async function initializePaymentElement() {
  if (!clientSecret.value)
    return

  if (!stripe.value)
    throw new Error(t('errors.initialize_stripe_fail'))

  const paymentElement = checkout.value?.createElement('payment', { layout: {
    type: 'accordion',
    defaultCollapsed: false,
    radios: false,
    spacedAccordionItems: true,
  } })
  if (paymentElement) {
    paymentElement.mount('#payment-element')
  }
}

async function handlePay() {
  errorMessage.value = ''

  const checkoutResults = await checkout.value?.confirm()
  if (checkoutResults?.type === 'success') {
    // Handle successful payment
  }
  else if (checkoutResults?.type === 'error') {
    errorMessage.value = checkoutResults.error.message
  }
}
</script>

<template>
  <div v-if="paymentReady">
    <div id="payment-element" ref="paymentElementRef" class="md:!border-0" />
    <ErrorPublic v-if="errorMessage" class="mt-4" :error="errorMessage" />
  </div>

  <!-- Loading State -->
  <div v-else class="md:items-center h-[22rem]">
    <div class="flex flex-col border border-slate-200 p-5 w-full h-full rounded-lg gap-6">
      <div class="placeholder animate-pulse w-1/6 h-6" />
      <div class="placeholder animate-pulse w-4/6 h-6 mt-5" />
      <div class="placeholder animate-pulse w-full h-12" />
      <div class="flex flex-row gap-3">
        <div class="placeholder animate-pulse w-1/2 h-12" />
        <div class="placeholder animate-pulse w-1/2 h-12" />
      </div>
      <div class="placeholder animate-pulse w-full h-12" />
    </div>
  </div>
</template>
