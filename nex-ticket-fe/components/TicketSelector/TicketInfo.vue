<script lang="ts" setup>
import type NexOrderItem from '~/models/NexOrderItem'

const props = defineProps<{
  ticket: NexOrderItem
  index: number
}>()

const emit = defineEmits(['editClick'])


</script>

<template>
  <div class="flex overflow-hidden h-full w-full relative">
    <div class="absolute h-[20px] w-[20px] rounded-full -top-[10px] -left-2 border border-slate-600 bg-pie-25 z-30" />
    <div class="absolute h-[20px] w-[20px] rounded-full -bottom-[10px] -left-2 bg-pie-25 border border-slate-600 z-30 " />
    <div class="absolute h-[20px] w-[20px] rounded-full -top-[10px] -right-2 border border-slate-600 bg-pie-25 z-30" />
    <div class="absolute h-[20px] w-[20px] rounded-full -bottom-[10px] -right-2  bg-pie-25 border border-slate-600 z-30 " />

    <div class="h-full w-full shadow-xl relative overflow-hidden">
      <div class="w-[75%] absolute h-full">
        <div class="h-[20px] w-[20px] rounded-full absolute -top-[10px] -right-[10px] border border-slate-600 bg-pie-25 z-20" />
        <div class="h-[20px] w-[20px] rounded-full absolute -bottom-[10px] -right-[10px] border border-slate-600 bg-pie-25 z-20" />
      </div>
      <div class="border border-slate-600 h-full w-full flex">
        <!-- Main Content Section -->
        <div class="flex p-[7px] bg-white relative w-[75%] h-full">
          <div class="flex relative w-full h-full">
            <div class="absolute overflow-hidden w-full h-full ">
              <!-- <div class="absolute h-[20px] w-[20px] rounded-full -top-[10px] -left-2 border border-slate-600 bg-white z-10 " />
              <div class="absolute h-[20px] w-[20px] rounded-full -bottom-[10px] -left-2 border border-slate-600 bg-white z-10 " />
              <div class="absolute h-[20px] w-[20px] rounded-full -top-[10px] -right-2 border border-slate-600 bg-white z-10 " />
              <div class="absolute h-[20px] w-[20px] rounded-full -bottom-[10px] -right-2 border border-slate-600 bg-white z-10 " /> -->
            </div>

            <div class="border border-black w-full h-full relative">
              <!-- Content -->
              <div class="flex pl-8 flex-col w-full h-full justify-center ">
                <div class="text-lg-bold text-slate-700">
                  {{ $t('public.ticket.checkout.payment.ticket') }} {{ props.index }} - {{ props.ticket.name }} {{ $t('public.ticket.checkout.payment.ticket') }}
                </div>
                <div class="text-lg-medium text-slate-500">
                  {{ $t('public.ticket.checkout.payment.quantity') }}: {{ ticket.quantity }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Side Button Section -->
        <div class="bg-pie-700 w-[25%] h-full">
          <div class="flex p-[7px] bg-pie-700 border-l border-slate-600 w-full h-full">
            <div class="flex relative w-full h-full">
              <div class="absolute overflow-hidden w-full h-full">
                <div class="absolute h-[20px] w-[20px] rounded-full -top-[10px] -left-2 border border-slate-200 bg-pie-700 z-10 " />
                <div class="absolute h-[20px] w-[20px] rounded-full -bottom-[10px] -left-2 border border-slate-200 bg-pie-700 z-10 " />
                <div class="absolute h-[20px] w-[20px] rounded-full -top-[10px] -right-2  border border-slate-200 bg-pie-700 z-10 " />
                <div class="absolute h-[20px] w-[20px] rounded-full -bottom-[10px] -right-2 border border-slate-200 bg-pie-700 z-10 " />
              </div>
              <div class="flex items-center justify-center border border-slate-200 w-full h-full">
                <!-- Button Content -->
                <button class="transition-basic flex items-center justify-center z-10" @click="emit('editClick')">
                  <div>
                    <span class="text-base-bold text-white">{{ $t('public.ticket.checkout.payment.edit') }}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>
