<script lang="ts" setup>
import { ArrowRight01Icon, Cancel01Icon, GoogleMapsIcon, InformationCircleIcon, Mail01Icon, SentIcon, TelephoneIcon } from 'hugeicons-vue'
import { object as yupObject, string as yupString } from 'yup'
import { useFeedback } from '~/composables/useFeedback'

const props = defineProps({
  organiserEmail: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['close'])
const feedback = useFeedback()
const { t } = useI18n()

const schema = yupObject({
  name: yupString().required().min(5).max(25).label(t('auth.fields.name')),
  email: yupString().email().required().label(t('auth.fields.email')),
  subject: yupString().required().min(5).max(20).label(t('auth.fields.subject')),
  message: yupString().required().min(10).max(2500).label(t('auth.fields.contact_message')),
})

const { validate: validateSchema } = useForm({
  validationSchema: schema,
})

const name = useField<string>('name')
const email = useField<string>('email')
const subject = useField<string>('subject')
const message = useField<string>('message')
const serverResponse = ref<null | any>(null)

async function sendMessage() {
  const { valid: isValid } = await validateSchema()
  if (!isValid) {
    return
  }
  try {
    const { error: fetchError } = await useAPI('/api/public/contact_form', {
      method: 'POST',
      body: {
        name: name.value.value,
        email: email.value.value,
        subject: subject.value.value,
        message: message.value.value,
        promoter_email: props.organiserEmail,
      },
    })
    if (fetchError.value) {
      feedback.error(`${t('public.event_info.contact_form.messages.error')} ${fetchError.value}`, { level: 'error', rollbar: true })
      serverResponse.value = fetchError.value.message
    }
    feedback.success(t('public.event_info.contact_form.messages.success'))
    emit('close')
  }
  catch (err: any) {
    feedback.error('Unexpected error:', { level: 'error', rollbar: true, extras: err })
    serverResponse.value = err.message
  }
}
</script>

<template>
  <!-- Overlay -->
  <div class="fixed inset-0 bg-gray-100 bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50" @click="emit('close')">
    <!-- Popup container -->
    <div
      class="lg:flex-row lg:max-w-[80rem] md:max-w-[60rem]
             flex flex-col w-full items-start p-3 gap-6 bg-pie-25 border border-slate-400 shadow-2xl rounded-3xl max-h-screen overflow-y-auto" @click.stop
    >
      <!-- close -->
      <div
        class="lg:items-center lg:bg-pie-25 lg:right-2 lg:top-1
              flex flex-row gap-3 p-3 rounded-lg absolute right-10 z-50 top-10"
      >
        <div class="w-6 h-6">
          <button @click="emit('close')">
            <Cancel01Icon
              class="lg:text-slate-500
                    text-slate-100"
            />
          </button>
        </div>
      </div>
      <!-- Contact Information -->
      <div class="flex flex-col items-start p-9 gap-6 w-full h-fit bg-pie-700 rounded-2xl">
        <div class="flex flex-col items-start gap-9 w-full">
          <div
            class="lg:items-center lg:gap-0
                  text-3xl-bold flex flex-row items-start text-slate-100 z-10 gap-9"
          >
            Contact Information
          </div>
          <!-- Promoter -->
          <div class="flex flex-col items-start p-5 gap-6 w-full bg-pie-900 rounded-2xl z-20">
            <div class="flex items-center justify-between w-full">
              <div class="flex items-center gap-3">
                <!-- Placeholder image -->
                <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-slate-400">
                  No Image
                </div>

                <!-- Placeholder name  -->
                <NuxtLinkLocale
                  to="/promoter_info"
                  class="text-xl-medium text-slate-100 underline"
                >
                  Promoter Name
                </NuxtLinkLocale>
              </div>
              <ArrowRight01Icon class="flex items-center text-slate-100" />
            </div>
          </div>
          <!-- Telephone -->
          <div class="flex flex-row items-center gap-6 w-full z-30">
            <div class="w-6 h-6">
              <TelephoneIcon class="w-5 h-5 left-0.5 top-0.5 text-slate-200" />
            </div>
            <div class="flex flex-col justify-center items-start">
              <div class="text-lg-medium flex items-center text-slate-200">
                +421 123 123 123
              </div>
              <div class="text-lg-medium flex items-center text-slate-200">
                +421 123 123 123
              </div>
            </div>
          </div>
          <!-- Email -->
          <div
            class="flex flex-row items-center gap-6 w-full z-40"
          >
            <div class="w-6 h-6">
              <Mail01Icon class="w-5 h-5 left-0.5 top-0.5 text-slate-200" />
            </div>
            <div class="flex flex-col justify-center items-start">
              <div class="text-lg-medium flex items-center text-slate-200">
                <EMAIL>
              </div>
            </div>
          </div>
          <!-- Location -->
          <div class="flex flex-row items-center gap-6 w-full z-40">
            <div class="w-6 h-6">
              <GoogleMapsIcon class="w-5 h-5 left-0.5 top-0.5 text-slate-200" />
            </div>
            <div class="flex flex-col justify-center items-start">
              <div class="text-lg-medium flex items-center text-slate-200">
                Stockholm, Sweden
              </div>
            </div>
          </div>
          <!-- Add. Information -->
          <div class="flex flex-row items-center gap-6 w-full z-60">
            <div class="w-6 h-6">
              <InformationCircleIcon class="w-5 h-5 left-0.5 top-0.5 text-slate-200" />
            </div>
            <div class="flex flex-col justify-center items-start ">
              <div class="text-lg-medium flex items-center text-slate-200">
                Any short additional information that is important to convey and was provided by the promoter
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Message -->
      <div
        class="lg:gap-2 lg:h-full
              flex flex-col items-start w-full gap-8 p-9 order-0 relative"
      >
        <!-- Details -->
        <div class="flex flex-col items-start gap-2 w-full">
          <div class="text-base-medium text-slate-500 ">
            Details
          </div>
          <div
            class="lg:gap-2
                  flex flex-col items-start w-full order-1 gap-4"
          >
            <div
              class="md:flex-row
                    flex flex-col items-start gap-4 w-full order-1 "
            >
              <!-- Name -->
              <v-text-field
                v-model="name.value.value"
                class="lg:w-1/2 w-full"
                variant="outlined"
                :label="t('auth.fields.name')"
                append-inner-icon="mdi-account"
                :error-messages="name.errorMessage.value"
              />
              <!-- Email -->
              <v-text-field
                v-model="email.value.value"
                class="lg:w-1/2 w-full"
                variant="outlined"
                :label="t('auth.fields.email')"
                append-inner-icon="mdi-email"
                :error-messages="email.errorMessage.value"
              />
            </div>
            <div class="flex flex-row items-center gap-2 w-full order-1 flex-grow-0">
              <!-- Subject -->
              <v-text-field
                v-model="subject.value.value"
                variant="outlined"
                :label="t('auth.fields.subject')"
                :error-messages="subject.errorMessage.value"
              />
            </div>
          </div>
        </div>
        <!-- Message -->
        <div class="flex flex-col items-start gap-2 w-full order-1 flex-grow">
          <div class="text-base-medium text-slate-500">
            Message
          </div>
          <v-textarea
            id="message"
            v-model="message.value.value"
            :label="t('auth.fields.contact_message')"
            :error-messages="message.errorMessage.value"
            variant="outlined"
            placeholder="Enter your message"
            class="flex items-center w-full h-full min-h-32 order-1"
          />
        </div>
        <!-- Button -->
        <div class="flex justify-center items-start gap-3 isolation bg-pie-700 rounded-lg order-2">
          <div
            class="lg:flex
                  p-1 gap-3 bg-pie-700 rounded-lg hover:bg-pie-600 justify-center items-center w-full"
          >
            <div class="border border-white rounded-lg py-2 px-4 place-items-center w-full">
              <button class="bg-transparent text-white text-lg-bold text-center w-full text-nowrap" @click="sendMessage">
                <div class="flex flex-row place-items-center gap-3 justify-center">
                  {{ $t('public.event_info.contact_form.buttons.send') }}
                  <SentIcon />
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
