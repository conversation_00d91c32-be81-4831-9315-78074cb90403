<script lang="ts" setup>
import { Facebook02Icon, Globe02Icon, InstagramIcon, Link04Icon, TelegramIcon } from 'hugeicons-vue'

const props = defineProps({
  eventLinks: Array<{
    link: string
    platform: string
  }>,
})

const iconsMap = [
  {
    key: 'facebook',
    icon: Facebook02Icon,
  },
  {
    key: 'instagram',
    icon: InstagramIcon,
  },
  {
    key: 'telegram',
    icon: TelegramIcon,
  },
  {
    key: 'website',
    icon: Globe02Icon,
  },
  {
    key: 'other',
    icon: Link04Icon,
  },
]

const visiblePlatforms = computed(() =>
  iconsMap.filter(platform =>
    props.eventLinks?.some(link => link.platform === platform.key),
  ),
)

// Grab the actual link for a given platform
function getPlatformLink(key: string) {
  return props.eventLinks?.find(link => link.platform === key)?.link || '#'
}
</script>

<template>
  <section v-if="visiblePlatforms?.length !== 0" class="space-y-4">
    <div class="text-slate-500 text-base-medium">
      {{ $t('public.event_info.links') }}
    </div>
    <div class="flex gap-3">
      <a
        v-for="platform in visiblePlatforms"
        :key="platform.key" :href="getPlatformLink(platform.key)"
        class="p-4 border-2 border-slate-800 rounded-lg hover:bg-slate-50"
      >

        <component :is="platform.icon" class="text-slate-800 text-2xl" />
      </a>
    </div>
  </section>
</template>

<style>

</style>
