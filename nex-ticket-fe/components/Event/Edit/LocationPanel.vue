<script lang="ts" setup>
import { number as yupNumber, object as yupObject, string as yupString } from 'yup'

const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.location_comp.yup_texts.${key}`)
const label_t = (key: string) => t(`organiser.edit_event.location_comp.labels.${key}`)

const schema_step_2 = yupObject({
  venue_name: yupString()
    .required(yup_t('venue_name_required'))
    .min(3, yup_t('venue_name_min'))
    .max(50, yup_t('venue_name_max'))
    .label(label_t('venue_name')),
  latitude: yupNumber()
    .required(yup_t('latitude_required'))
    .min(-90, yup_t('latitude_range'))
    .max(90, yup_t('latitude_range'))
    .label(label_t('latitude')),
  longitude: yupNumber()
    .required(yup_t('longitude_required'))
    .min(-180, yup_t('longitude_range'))
    .max(180, yup_t('longitude_range'))
    .label(label_t('longitude')),
})
const { validate: validateStep2 } = useForm({
  validationSchema: schema_step_2,
})

const eventData = defineModel<{ latitude: number, longitude: number, venue_name: string }>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

const latitude = useField<number>('latitude')
const longitude = useField<number>('longitude')
const venue_name = useField<string>('venue_name')

onMounted(async () => {
  validateFunc.value = validateFields
  setExistingEventData()
})

function setExistingEventData() {
  if (eventData.value) {
    latitude.value.value = eventData.value.latitude
    longitude.value.value = eventData.value.longitude
    venue_name.value.value = eventData.value.venue_name
  }
  else {
    eventData.value = {
      latitude: 37.39094933041195,
      longitude: -122.02503913145092,
      venue_name: '',
    }
  }
}

async function validateFields(): Promise<boolean> {
  const { valid } = await validateStep2()
  return valid
}
</script>

<template>
  <v-card class="!shadow-none">
    <v-row class="pt-5 px-5">
      <v-col cols="12">
        <VTextField
          v-model="venue_name.value.value"
          class="font-onest"
          :label="$t('organiser.edit_event.location_comp.labels.venue_name')"
          :placeholder="$t('organiser.edit_event.location_comp.place_holders.venue_name')"
          :error-messages="venue_name.errorMessage.value"
          required
          variant="outlined"
          @update:model-value="eventData && (eventData.venue_name = venue_name.value.value)"
        />
      </v-col>
    </v-row>
    <div class="px-5 pb-5">
      <GoogleMapsOrganiser
        v-model:lat="latitude.value.value"
        v-model:lng="longitude.value.value"
        height="h-svh"
        @update:lat="eventData && (eventData.latitude = latitude.value.value)"
        @update:lng="eventData && (eventData.longitude = longitude.value.value)"
      />
    </div>
  </v-card>
</template>

<style>

</style>
