<script lang="ts" setup>
import { Delete02Icon, ImageAdd02Icon } from 'hugeicons-vue'
import { <PERSON>ropper } from 'vue-advanced-cropper'
import { date as yupDate, object as yupObject, string as yupString } from 'yup'
import 'vue-advanced-cropper/dist/style.css'

const feedback = useFeedback()
const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.basic_settings_comp.yup_texts.${key}`)
const label_t = (key: string) => t(`organiser.edit_event.basic_settings_comp.labels.${key}`)

const nuxt = useNuxtApp()

interface LocalCoverImageType {
  isLoaded: boolean
  uploadFailed: boolean
  rawFile?: string
  url?: string
  key?: string
  file?: File
}

const schema_step_1 = yupObject({
  name: yupString()
    .required(yup_t('event_name_required'))
    .min(3, yup_t('event_name_min'))
    .max(50, yup_t('event_name_max'))
    .label(label_t('event_name')),
  cover_image: yupString()
    .required(yup_t('cover_image_required'))
    .label(label_t('cover_image')),
  description: yupString()
    .required(yup_t('description_required'))
    .min(10, yup_t('description_min'))
    .max(2500, yup_t('description_max'))
    .label(label_t('description')),
  start_time: yupDate()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .required(yup_t('start_time_required'))
    .min(new Date(), yup_t('start_time_min'))
    .label(label_t('start_time')),
  end_time: yupDate()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .required(yup_t('end_time_required'))
    .test(
      'is-greater',
      yup_t('end_time_min'),
      function (value) {
        const { start_time } = this.parent
        return value > start_time
      },
    )
    .label(label_t('end_time')),
})
const { validate: validateStep1 } = useForm({
  validationSchema: toTypedSchema(schema_step_1),
})

const eventData = defineModel<{ name: string, cover_image_key: string, cover_image_url: string, description: string, start_time: Date, end_time: Date }>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

const name = useField<string>('name')
const cover_image = useField<string>('cover_image', undefined, {
  validateOnValueUpdate: true,
  initialValue: '',
})
const description = useField<string>('description')
const start_time = useField<string>('start_time')
const end_time = useField<string>('end_time')

// Cropper related refs
const cropperModal = ref(false)
const cropperAspectRatio = 16 / 9
const imagePreview = ref<string | null>(null)
const cropper = ref<InstanceType<typeof Cropper>>()
const fileInput = ref<HTMLInputElement | null>(null)

const localCoverImage = reactive<LocalCoverImageType>({
  isLoaded: false,
  uploadFailed: false,
})

async function validateFields(): Promise<boolean> {
  const { valid } = await validateStep1()
  return valid
}

onMounted(async () => {
  validateFunc.value = validateFields
  setExistingEventData()
})

function setExistingEventData() {
  if (eventData.value) {
    name.value.value = eventData.value.name
    cover_image.value.value = eventData.value.cover_image_key
    description.value.value = eventData.value.description
    start_time.value.value = toLocalISOString(eventData.value.start_time).slice(0, 16)
    end_time.value.value = toLocalISOString(eventData.value.end_time).slice(0, 16)

    if (eventData.value.cover_image_url || eventData.value.cover_image_key) {
      localCoverImage.url = eventData.value.cover_image_url
      localCoverImage.key = eventData.value.cover_image_key
      localCoverImage.isLoaded = true
    }
  }
  else {
    eventData.value = {
      name: '',
      cover_image_key: '',
      cover_image_url: '',
      description: '',
      start_time: new Date(),
      end_time: new Date(),
    }
  }
}

// Image handling functions
async function uploadCoverImage(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  try {
    const response: { image_key: string, image_url: string } = await nuxt.$api('/api/organiser/images/upload', {
      method: 'POST',
      body: formData,
    })
    if (response.image_key && response.image_url) {
      localCoverImage.key = response.image_key
      localCoverImage.url = response.image_url
      localCoverImage.isLoaded = true
      cover_image.value.value = response.image_key
      if (eventData.value) {
        eventData.value.cover_image_key = response.image_key
        eventData.value.cover_image_url = response.image_url
      }
    }
    else {
      localCoverImage.uploadFailed = true
    }
  }
  catch (error) {
    localCoverImage.uploadFailed = true
    feedback.error(t('errors/file_errors/upload_error'), { level: 'error', rollbar: true, extras: error })
  }
}

function handleFileSelect(event: Event) {
  const input = event.target as HTMLInputElement
  if (input.files?.[0]) {
    const file = input.files[0]
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string
      cropperModal.value = true
      localCoverImage.rawFile = imagePreview.value
      localCoverImage.file = file
      localCoverImage.isLoaded = false
      localCoverImage.uploadFailed = false
    }
    reader.readAsDataURL(file)
  }
}

function confirmCrop() {
  if (cropper.value) {
    const result = cropper.value.getResult()
    if (result.canvas) {
      result.canvas.toBlob(async (blob) => {
        if (blob) {
          const file = new File([blob], 'cover-image.jpg', {
            type: 'image/jpeg',
            lastModified: Date.now(),
          })
          localCoverImage.file = file
          await uploadCoverImage(file)
          cropperModal.value = false
        }
      }, 'image/jpeg')
    }
  }
}

async function removeImage() {
  if (localCoverImage.key) {
    try {
      await nuxt.$api(`/api/organiser/images/delete/${localCoverImage.key}`, {
        method: 'DELETE',
      })
    }
    catch (error) {
      feedback.error(t('errors/file_errors/delete_image_error'), { level: 'error', rollbar: true, extras: error })
    }
  }
  resetCroperState()
}

function resetCroperState() {
  Object.assign(localCoverImage, {
    isLoaded: false,
    uploadFailed: false,
    rawFile: undefined,
    url: undefined,
    key: undefined,
    file: undefined,
  })
  cover_image.value.value = ''
  if (eventData.value) {
    eventData.value.cover_image_key = ''
    eventData.value.cover_image_url = ''
  }
  if (fileInput.value)
    fileInput.value.value = ''
}

async function retryCoverUpload() {
  if (!localCoverImage.file) {
    feedback.error(t('errors/file_errors/retry_fail'), { level: 'error', rollbar: true })
    return
  }

  // Reset state for new attempt
  localCoverImage.isLoaded = false
  localCoverImage.uploadFailed = false

  await uploadCoverImage(localCoverImage.file)
}
</script>

<template>
  <!-- main content -->
  <v-card flat>
    <v-container>
      <v-row>
        <v-container>
          <!-- Cropper Modal -->
          <v-dialog v-model="cropperModal" max-width="800">
            <!-- @focusout="resetCroperState"> -->
            <v-card>
              <v-card-title class="text-base-medium mt-1 text-center">
                {{ $t('organiser.edit_event.basic_settings_comp.cover_image.crop') }}
              </v-card-title>
              <v-card-text>
                <div class="md:h-96 w-full relative">
                  <Cropper
                    ref="cropper"
                    :src="imagePreview"
                    :stencil-props="{ aspectRatio: cropperAspectRatio }"
                    :resize-image="{ adjustStencil: false }"
                    default-boundaries="fit"
                  />
                </div>
              </v-card-text>
              <v-card-actions>
                <v-btn color="error" class="text-base-medium" @click="() => { resetCroperState(), cropperModal = false }">
                  {{ $t('organiser.edit_event.basic_settings_comp.cover_image.cancel') }}
                </v-btn>
                <v-btn color="primary" class="text-base-medium" @click="confirmCrop">
                  {{ $t('organiser.edit_event.basic_settings_comp.cover_image.confirm_crop') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>

          <!-- Image Upload Section -->
          <div class="space-y-4 mb-6">
            <input
              id="cover-image-input"
              ref="fileInput"
              type="file"
              accept="image/*"
              hidden
              @change="handleFileSelect"
            >
            <div v-if="!localCoverImage.url">
              <label
                for="cover-image-input"
                class="block w-full px-4 py-6 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-primary transition-basic text-center"
              >
                <div class="flex flex-col items-center justify-center space-y-2">
                  <ImageAdd02Icon :size="48" class="text-slate-400" />
                  <span class="text-lg-medium text-slate-600">
                    {{ $t('organiser.edit_event.basic_settings_comp.cover_image.cover_image') }}
                  </span>
                  <span class="text-sm-medium text-slate-400">
                    {{ $t('organiser.edit_event.basic_settings_comp.cover_image.cover_image_info') }}
                  </span>
                </div>
              </label>
              <!-- Validation Error -->
              <p v-if="cover_image.errorMessage.value" class="text-red-500 text-sm-bold mt-1">
                {{ cover_image.errorMessage.value }}
              </p>
            </div>

            <!-- Preview -->
            <div v-else>
              <div class="relative aspect-w-16 aspect-h-9 rounded-lg overflow-hidden border border-slate-600">
                <img
                  :src="localCoverImage.url || localCoverImage.rawFile"
                  alt="Cover preview"
                  class="object-cover w-full h-full"
                >
                <!-- Upload Status Overlay -->
                <div
                  v-if="!localCoverImage.isLoaded && !localCoverImage.uploadFailed"
                  class="z-10 h-full w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                >
                  <v-overlay :model-value="true" class="align-center justify-center" contained>
                    <v-progress-circular indeterminate color="white" size="120" width="10" />
                  </v-overlay>
                </div>
                <!-- Action Buttons -->
                <div class="absolute z-10 top-3 right-3 mx-2 my-2 grid grid-cols-2 gap-2">
                  <v-btn
                    :icon="getCloudIcon(localCoverImage)"
                    size="small"
                    :color="localCoverImage.isLoaded ? 'success' : localCoverImage.uploadFailed ? 'error' : 'primary'"
                    :disabled="!localCoverImage.uploadFailed"
                    @click="retryCoverUpload"
                  />
                  <v-btn icon color="#dc2626" size="small" @click="removeImage">
                    <Delete02Icon />
                  </v-btn>
                </div>
              </div>

              <!-- Validation Error -->
              <p v-if="cover_image.errorMessage.value" class="text-red-500 text-sm-bold mt-1">
                {{ cover_image.errorMessage.value }}
              </p>
            </div>

            <!-- Rest of Step 1 Form -->
            <VTextField
              id="name"
              v-model="name.value.value"
              :label="$t('organiser.edit_event.basic_settings_comp.labels.event_name')"
              :placeholder="$t('organiser.edit_event.basic_settings_comp.place_holders.event_name')"
              :error-messages="name.errorMessage.value"
              variant="outlined"
              required
              @update:model-value="eventData && (eventData.name = name.value.value)"
            />
            <!-- PC version of Dates selectors -->
            <div class="hidden md:block">
              <v-row>
                <v-col cols="6">
                  <VTextField
                    id="start_time"
                    v-model="start_time.value.value"
                    :label="$t('organiser.edit_event.basic_settings_comp.labels.start_time')"
                    :error-messages="start_time.errorMessage.value"
                    required
                    variant="outlined"
                    type="datetime-local"
                    @update:model-value="eventData && (eventData.start_time = new Date(start_time.value.value))"
                  />
                </v-col>
                <v-col cols="6">
                  <VTextField
                    id="end_time"
                    v-model="end_time.value.value"
                    :label="$t('organiser.edit_event.basic_settings_comp.labels.end_time')"
                    :error-messages="end_time.errorMessage.value"
                    required
                    variant="outlined"
                    type="datetime-local"
                    @update:model-value="eventData && (eventData.end_time = new Date(end_time.value.value))"
                  />
                </v-col>
              </v-row>
            </div>

            <!-- Mobile version of Dates selectors -->
            <div class="md:hidden">
              <v-row>
                <v-col cols="12">
                  <VTextField
                    id="start_time"
                    v-model="start_time.value.value"
                    :label="$t('organiser.edit_event.basic_settings_comp.labels.start_time')"
                    :error-messages="start_time.errorMessage.value"
                    required
                    variant="outlined"
                    type="datetime-local"
                    @update:model-value="eventData && (eventData.start_time = new Date(start_time.value.value))"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <VTextField
                    id="end_time"
                    v-model="end_time.value.value"
                    :label="$t('organiser.edit_event.basic_settings_comp.labels.end_time')"
                    :error-messages="end_time.errorMessage.value"
                    required
                    variant="outlined"
                    type="datetime-local"
                    @update:model-value="eventData && (eventData.end_time = new Date(end_time.value.value))"
                  />
                </v-col>
              </v-row>
            </div>
            <v-row>
              <v-col cols="12">
                <v-textarea
                  id="description"
                  v-model="description.value.value"
                  :label="$t('organiser.edit_event.basic_settings_comp.labels.description')"
                  :error-messages="description.errorMessage.value"
                  :placeholder="$t('organiser.edit_event.basic_settings_comp.place_holders.description')"
                  variant="outlined"
                  @update:model-value="eventData && (eventData.description = description.value.value)"
                />
              </v-col>
            </v-row>
          </div>
        </v-container>
      </v-row>
    </v-container>
  </v-card>
</template>

<style>

</style>
