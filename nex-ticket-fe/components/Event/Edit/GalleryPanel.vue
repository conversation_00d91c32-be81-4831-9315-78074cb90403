<script lang="ts" setup>
import { array as yupArray, object as yupObject, string as yupString } from 'yup'

const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.gallery_comp.yup_texts.${key}`)
const label_t = (key: string) => t(`organiser.edit_event.gallery_comp.labels.${key}`)

const schema_step_4 = yupObject({
  eventData: yupArray().of(
    yupObject({
      isLoaded: yupString().notRequired().label(label_t('loaded_image')),
      uploadFailed: yupString().notRequired().label(label_t('upload_failed')),
      rawFile: yupString().notRequired().label(label_t('raw_file')),
      url: yupString().url(yup_t('invalid_url_format')).test('is-image', yup_t('valid_image_url'), value => !value || /\.(?:jpg|jpeg|png|gif)$/.test(value)),
      file: yupString().notRequired().label(label_t('file')),
      key: yupString().notRequired().label(label_t('key')),
    }).notRequired().label(label_t('event_data')),
  ).notRequired().label(label_t('photo_gallery')),
})

const { validate: validateStep4 } = useForm({
  validationSchema: schema_step_4,
})

const eventData = defineModel<LocalImageType[]>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

onMounted(async () => {
  validateFunc.value = validateFields
})

async function validateFields(): Promise<boolean> {
  const { valid } = await validateStep4()
  return valid
}
</script>

<template>
  <v-card class="!shadow-none">
    <v-row>
      <v-col cols="12">
        <v-card>
          <EditableGallery v-model="eventData" class="p-3" />
        </v-card>
      </v-col>
    </v-row>
  </v-card>
</template>

<style>

</style>
