<script lang="ts" setup>
import { Delete02Icon } from 'hugeicons-vue'
import { array as yupArray, number as yupNumber, object as yupObject, string as yupString } from 'yup'

const { t } = useI18n()
const yup_t = (key: string) => t(`organiser.edit_event.addit_sett_comp.yup_texts.${key}`)
const label_t = (key: string) => t(`organiser.edit_event.addit_sett_comp.labels.${key}`)

const schema_step_3 = yupObject({
  social_media_links: yupArray().of(
    yupObject({
      platform: yupString()
        .required(yup_t('platform_required'))
        .oneOf(['Facebook', 'Twitter', 'Instagram', 'LinkedIn', 'Snapchat', 'Website', 'Other'], yup_t('invalid_platform'))
        .label(label_t('platform')),
      link: yupString()
        .url(yup_t('invalid_url_format'))
        .required(yup_t('link_required'))
        .label(label_t('link')),
    }).required(yup_t('socials_required')),
  ).notRequired().label(label_t('socials')),
  policies: yupArray().of(
    yupObject({
      type: yupString()
        .required(yup_t('policy_type_required'))
        .min(3, yup_t('policy_type_min'))
        .max(50, yup_t('policy_type_max'))
        .label(label_t('policy_type')),
      details: yupString()
        .required(yup_t('policy_details_required'))
        .min(5, yup_t('policy_details_min'))
        .max(500, yup_t('policy_details_max'))
        .label(label_t('policy_details')),
    }),
  ).notRequired().label(label_t('policies')),
  tag_ids: yupArray().of(yupNumber().required(yup_t('tag_id_required')).positive(yup_t('tag_id_positive'))).notRequired().label(label_t('tag_ids')),
})
const { validate: validateStep3, errors: errorsStep3 } = useForm({
  validationSchema: schema_step_3,
})

const eventData = defineModel<{ socials: { platform: string, link: string }[], policies: { type: string, details: string }[], tag_ids: number[] }>('eventData')
const validateFunc = defineModel<() => Promise<boolean>>('validateFunc')

const { fields: socials, push: addSocial, remove: deleteSocial, update: updateSocial } = useFieldArray<{ platform: string, link: string }>('social_media_links')
const { fields: policies, push: addPolicy, remove: deletePolicy, update: updatePolicy } = useFieldArray<{ type: string, details: string }>('policies')
const tag_ids = useField<number[]>('tag_ids')

onMounted(async () => {
  validateFunc.value = validateFields
  setExistingEventData()
})

function setExistingEventData() {
  if (eventData.value) {
    eventData.value.socials.forEach((social) => {
      addSocial({ platform: social.platform, link: social.link })
    })

    eventData.value.policies.forEach((policy) => {
      addPolicy({ type: policy.type, details: policy.details })
    })

    tag_ids.value.value = eventData.value.tag_ids
  }
  else {
    eventData.value = { socials: [], policies: [], tag_ids: [] }
  }
}

async function validateFields(): Promise<boolean> {
  const { valid } = await validateStep3()
  return valid
}
// Changing values for social links
function onSocialsCreate() {
  if (eventData.value) {
    eventData.value.socials.push({ platform: 'Instagram', link: '' })
  }
}
function onSocialsDelete(index: number) {
  if (eventData.value) {
    eventData.value.socials = eventData.value?.socials.filter((_, i) => i !== index)
  }
}
function onSocialsLinkChange(index: number, newLink: string) {
  if (eventData.value) {
    eventData.value.socials[index].link = newLink
  }
}
function onSocialsPlatformChange(index: number, newPlatform: string) {
  if (eventData.value) {
    eventData.value.socials[index].platform = newPlatform
  }
}

// Changing values for Policies
function onPoliciesCreate() {
  if (eventData.value) {
    eventData.value.policies.push({ type: '', details: '' })
  }
}
function onPoliciesDelete(index: number) {
  if (eventData.value) {
    eventData.value.policies = eventData.value?.policies.filter((_, i) => i !== index)
  }
}
function onPoliciesTypeChange(index: number, newType: string) {
  if (eventData.value) {
    eventData.value.policies[index].type = newType
  }
}
function onPoliciesDetailsChange(index: number, newDetails: string) {
  if (eventData.value) {
    eventData.value.policies[index].details = newDetails
  }
}
</script>

<template>
  <v-card flat>
    <v-card
      class="md:mx-4
        mx-2 mt-3 mb-8"
    >
      <v-card-title class="text-base-normal text-slate-800">
        {{ $t('organiser.edit_event.addit_sett_comp.links') }}
      </v-card-title>
      <div
        v-for="(social, index) in socials"
        :key="social.key"
        class="md:mb-0 md:px-4 md:flex-row md:gap-4
          mb-4 px-2 flex flex-col justify-around align-middle"
      >
        <div
          class="md:w-1/5 md:gap-0
            flex gap-5"
        >
          <VSelect
            class="text-base-normal"
            :value="social.value.platform" :items="['Facebook', 'Twitter', 'Instagram', 'LinkedIn', 'Snapchat', 'Website', 'Other']"
            :placeholder="$t('organiser.edit_event.addit_sett_comp.place_holders.platform')"
            :error-messages="errorsStep3[`social_media_links[${index}].platform`]"
            @update:model-value="(value) => {
              updateSocial(index, { platform: value ? value : '', link: social.value.link })
              onSocialsPlatformChange(index, value ? value : '')
            }"
          />
          <!-- Mobile version of the button -->
          <div class="md:hidden mt-1">
            <v-btn icon color="#dc2626" @click="deleteSocial(index), onSocialsDelete(index)">
              <Delete02Icon />
            </v-btn>
          </div>
        </div>
        <VTextField
          :value="social.value.link"
          placeholder="Links"
          class="text-base-normal md:w-4/5"
          :error-messages="errorsStep3[`social_media_links[${index}].link`]"
          @update:model-value="(value) => {
            updateSocial(index, { platform: social.value.platform, link: value ? value : '' })
            onSocialsLinkChange(index, value ? value : '')
          }"
        />
        <!-- PC version of the button -->
        <div class="mt-1 hidden md:block">
          <v-btn icon color="#dc2626" @click="deleteSocial(index), onSocialsDelete(index)">
            <Delete02Icon />
          </v-btn>
        </div>
      </div>
      <v-btn
        class="md:m-3
          text-base-normal mb-2 me-2 float-right"
        color="#1e293b"
        @click="addSocial({ platform: 'Instagram', link: '' }), onSocialsCreate()"
      >
        {{ $t('organiser.edit_event.addit_sett_comp.add_social') }}
      </v-btn>
    </v-card>
    <v-card
      class="md:mx-4
        mx-2 mt-3 mb-8"
    >
      <v-card-title class="text-base-normal text-slate-800">
        {{ $t('organiser.edit_event.addit_sett_comp.policies') }}
      </v-card-title>
      <div
        v-for="(policy, index) in policies"
        :key="policy.key"
        class="md:mb-0 md:px-4 md:flex-row md:gap-4
          mb-4 px-2 flex flex-col justify-around align-middle"
      >
        <div
          class="md:w-1/5 md:gap-0
            flex gap-5"
        >
          <VTextField
            class="text-base-normal"
            :value="policy.value.type"
            :error-messages="errorsStep3[`policies[${index}].type`]"
            :placeholder="$t('organiser.edit_event.addit_sett_comp.place_holders.policy_type')"
            @update:model-value="(value) => {
              updatePolicy(index, { type: value ? value : '', details: policy.value.details })
              onPoliciesTypeChange(index, value ? value : '')
            }"
          />
          <div class="md:hidden mt-1">
            <v-btn icon color="#dc2626" class="mt-1" @click="deletePolicy(index), onPoliciesDelete(index)">
              <Delete02Icon />
            </v-btn>
          </div>
        </div>
        <VTextField
          :value="policy.value.details"
          :placeholder="$t('organiser.edit_event.addit_sett_comp.place_holders.policy_details')"
          class="md:w-4/5 text-base-normal" :error-messages="errorsStep3[`policies[${index}].details`]"
          @update:model-value="(value) => {
            updatePolicy(index, { type: policy.value.type, details: value ? value : '' })
            onPoliciesDetailsChange(index, value ? value : '')
          }"
        />
        <div class="mt-1 hidden md:block">
          <v-btn icon color="#dc2626" class="mt-1" @click="deletePolicy(index), onPoliciesDelete(index)">
            <Delete02Icon />
          </v-btn>
        </div>
      </div>
      <v-btn
        class="md:m-3
          text-base-normal mb-2 me-2 float-right"
        color="#1e293b"
        @click="addPolicy({ type: '', details: '' }), onPoliciesCreate()"
      >
        {{ $t('organiser.edit_event.addit_sett_comp.add_policy') }}
      </v-btn>
    </v-card>
    <v-row>
      <v-col cols="12">
        <v-card
          class="md:mx-4
        mx-2 mt-3 mb-8"
        >
          <v-card-title class="text-base-normal">
            {{ $t('organiser.edit_event.addit_sett_comp.tags') }}
          </v-card-title>
          <div class="md:px-4 px-2">
            <SearchableTagsAdder
              v-model="tag_ids.value.value"
              @update:model-value="eventData && (eventData.tag_ids = tag_ids.value.value)"
            />
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-card>
</template>

<style>

</style>
