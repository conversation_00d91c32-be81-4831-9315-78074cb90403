<script lang="ts" setup>
const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
  mainPhotoUrl: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  venueName: {
    type: String,
    required: true,
  },
  venueAddress: {
    type: String,
    required: true,
  },
  startTime: {
    type: Date,
    required: true,
  },
  endTime: {
    type: Date,
    required: true,
  },
  saved: {
    type: Boolean,
    required: false,
  },
  blockWidthClass: {
    type: String,
    required: true,
  },
  photoHeightClass: {
    type: String,
    required: true,
  },
  /**
   * @prop {string} mode - Determines the mode of the component.
   * Acceptable values: 'organiser', 'public'.
   */
  mode: {
    type: String,
    required: true,
    validator: (value: string) => ['organiser', 'public'].includes(value),
  },
})

const emit = defineEmits(['eventSaved'])

const { t } = useI18n()

const { pageLoading } = storeToRefs(useGeneralStore())

function getMonthNameShort(monthNum: number) {
  switch (monthNum) {
    case 0:
      return t('public.month_shorts.jan')
    case 1:
      return t('public.month_shorts.feb')
    case 2:
      return t('public.month_shorts.mar')
    case 3:
      return t('public.month_shorts.apr')
    case 4:
      return t('public.month_shorts.may')
    case 5:
      return t('public.month_shorts.jun')
    case 6:
      return t('public.month_shorts.jul')
    case 7:
      return t('public.month_shorts.aug')
    case 8:
      return t('public.month_shorts.sep')
    case 9:
      return t('public.month_shorts.oct')
    case 10:
      return t('public.month_shorts.nov')
    case 11:
      return t('public.month_shorts.dec')
  }
}

function formatEventDate(startDate: Date, endDate: Date) {
  const today = new Date()
  const tomorrow = new Date()
  tomorrow.setDate(today.getDate() + 1)

  const startDateStr = startDate.toLocaleDateString()
  const todayStr = today.toLocaleDateString()
  const tomorrowStr = tomorrow.toLocaleDateString()

  if (startDateStr === todayStr)
    return t('public.today')
  if (startDateStr === tomorrowStr)
    return t('public.tomorrow')

  return startDate === endDate
    ? `${startDate.getDate()} ${getMonthNameShort(startDate.getMonth())} ${startDate.getFullYear()}`
    : (startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear())
        ? `${startDate.getDate()} - ${endDate.getDate()} ${getMonthNameShort(startDate.getMonth())} ${startDate.getFullYear()}`
        : (startDate.getFullYear() === endDate.getFullYear())
            ? `${startDate.getDate()} ${getMonthNameShort(startDate.getMonth())} - ${endDate.getDate()} ${getMonthNameShort(endDate.getMonth())} ${startDate.getFullYear()}`
            : `${startDate.getDate()} ${getMonthNameShort(startDate.getMonth())} ${startDate.getFullYear()} - ${endDate.getDate()} ${getMonthNameShort(endDate.getMonth())} ${endDate.getFullYear()}`
}

function isEventEditable(): boolean {
  if (props.startTime > new Date()) {
    return true
  }
  return false
}
</script>

<template>
  <!-- Main content -->
  <div v-if="!pageLoading">
    <div
      class="md:pb-6 md:w-[18.25rem]
        bg-slate-100 border-slate-500 border rounded-2xl pb-4 relative w-[17.5rem]"
      :class="`${props.blockWidthClass}`"
    >
      <NuxtLinkLocale
        :to="mode === 'public' ? `/events/${props.id}` : `/organiser/events/${props.id}`"
        class="absolute z-10 inset-0"
        :class="`${props.blockWidthClass}`"
      />
      <div
        class="z-10"
        :class="`${props.photoHeightClass}`"
      >
        <img
          :src="props.mainPhotoUrl"
          class="rounded-t-[15px]"
          :class="`${props.photoHeightClass} ${props.blockWidthClass}`"
        >
      </div>
      <div
        class="flex flex-col gap-1 mt-5"
      >
        <div v-if="props.mode === 'public'" class="z-20 absolute right-6 -mt-11">
          <SaveEventButton
            :event-id="props.id"
            :saved="props.saved"
            border-size="40"
            icon-size="20"
            @reload-saved-events="emit('eventSaved')"
          />
        </div>
        <div
          class="md:gap-4
            px-6 gap-3 flex flex-col relative"
        >
          <div>
            <p class="text-slate-950 text-sm-bold truncate">
              {{ props.name }}
            </p>
            <p class="text-slate-500 text-sm-medium truncate">
              {{ props.venueName }} - {{ props.venueAddress }}
            </p>
          </div>
          <div class="flex justify-between">
            <div>
              <p
                class=" md:max-w-28
                  text-sm-bold text-red-600 truncate max-w-28"
              >
                {{ formatEventDate(props.startTime, props.endTime) }}
              </p>
              <p class="text-sm-bold text-red-600">
                {{ formatDateToTime(props.startTime) }}
              </p>
            </div>
            <NuxtLinkLocale
              v-if="props.mode === 'public'"
              :to="`/events/${props.id}/tickets`"
              class="border border-slate-800 rounded-lg flex gap-1 py-2 px-3 z-20 md:hover:bg-slate-200"
            >
              <button @click.stop>
                <p class="text-base-bold text-slate-800">
                  {{ $t('public.ticket.get_tickets') }}
                </p>
              </button>
            </NuxtLinkLocale>
            <NuxtLinkLocale
              v-else-if="props.mode === 'organiser' && isEventEditable()"
              :to="`/organiser/events/${props.id}/edit`"
              class="border border-slate-800 rounded-lg flex gap-1 py-2 px-3 z-20 md:hover:bg-slate-200"
            >
              <button @click.stop>
                <p class="text-base-bold text-slate-800">
                  {{ $t('organiser.edit_event.buttons.edit_event') }}
                </p>
              </button>
            </NuxtLinkLocale>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- placeholder for pageLoading -->
  <div v-else>
    <div
      class="md:pb-6
        bg-slate-100 border-slate-500 border rounded-2xl pb-4 relative"
      :class="`${props.blockWidthClass}`"
    >
      <div
        class="z-10 flex bg-slate-200 items-center justify-between px-6 rounded-t-[15px]"
        :class="`${props.photoHeightClass}`"
      >
        <div class="placeholder-circle size-20 animate-pulse" />
        <div class="placeholder-circle size-16 animate-pulse" />
        <div class="placeholder-circle size-14 animate-pulse" />
      </div>
      <div
        class="flex flex-col gap-1 mt-5"
      >
        <div class="z-20 absolute right-6 -mt-11">
          <div class="placeholder animate-pulse" />
        </div>
        <div
          class="md:gap-6
            px-6 gap-3 flex flex-col relative"
        >
          <div class="flex flex-col gap-1">
            <div class="placeholder animate-pulse" />
            <div class="placeholder animate-pulse" />
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col gap-1">
              <div class="placeholder animate-pulse" />
              <div class="placeholder animate-pulse" />
            </div>
            <div>
              <div class="placeholder animate-pulse size-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
