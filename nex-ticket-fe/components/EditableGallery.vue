<script setup lang="ts">
import { Delete02Icon, ImageAdd02Icon } from 'hugeicons-vue'
import { useFeedback } from '~/composables/useFeedback'

const { t } = useI18n({ useScope: 'global' })

const images = defineModel<Array<LocalImageType>>({
  default: [],
})

const nuxt = useNuxtApp()

const rawImages = reactive<LocalImageType[]>([])

/** Hidden file input reference */
const fileInput = ref<HTMLInputElement | null>(null)

const feedback = useFeedback()

/** Trigger the hidden file input to add a new image */
function selectImage() {
  fileInput.value?.click()
}

/** Prefill gallery */
onMounted(async () => {
  prefill_values()
})

function prefill_values() {
  images.value.forEach((image) => {
    rawImages.push({
      isLoaded: image.isLoaded,
      uploadFailed: image.uploadFailed,
      rawFile: image.rawFile,
      url: image.url,
      file: image.file,
      key: image.key,
    })
  })
}

/** Handle file selection and add the new image */
function onImageSelected(event: Event) {
  if (!event || !event.target || !(event.target instanceof HTMLInputElement) || !event.target.files || !event.target.files[0]) {
    feedback.error(t('errors.file_errors.invalid_file_input_event_fail'), { level: 'error', rollbar: true, extras: event })
    return
  }
  const file = event.target.files[0]
  rawImages.push({ file, isLoaded: false, uploadFailed: false })
  const id = rawImages.length - 1
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result
      if (typeof result === 'string') {
        rawImages[id].rawFile = result
        uploadImage(file, id)
        // images.value.push(result)
        return
      }
      feedback.error(t('errors.file_errors.read_image_file_error'), { level: 'error', rollbar: true, extras: file })
    }
    reader.readAsDataURL(file)
  }
  // Reset the file input so the user can pick again
  event.target.value = ''
}

async function uploadImage(file: File | undefined, id: number): Promise<void> {
  if (!file) {
    feedback.error(t('errors.file_errors.invalid_file_error'), { level: 'error', rollbar: true, extras: file })
    return
  }

  rawImages[id].isLoaded = false

  const formData = new FormData()
  formData.append('file', file)
  try {
    const response: { image_key: string, image_url: string } = await nuxt.$api('/api/organiser/images/upload', {
      method: 'POST',
      body: formData,
    })
    if (response && response.image_key && response.image_url) {
      rawImages[id] = { url: response.image_url, key: response.image_key, isLoaded: true, uploadFailed: false }
      images.value.push({ url: response.image_url, key: response.image_key, isLoaded: true, uploadFailed: false })
      return
    }
    feedback.error(t('errors.file_errors.upload_image_error'), { level: 'error', rollbar: true, extras: response })
  }
  catch (error) {
    feedback.error(t('errors.file_errors.upload_image_error'), { level: 'error', rollbar: true, extras: error })
  }
  rawImages[id].uploadFailed = true
}

async function deleteImage(index: number) {
  if (rawImages.length <= index) {
    feedback.error(t('errors.file_errors.image_index_invalid_error'), { level: 'error', rollbar: true, extras: index })
    return
  }

  if (rawImages[index].isLoaded && rawImages[index].key) {
    await nuxt.$api(`/api/organiser/images/delete/${rawImages[index].key}`, {
      method: 'DELETE',
    })
  }
  rawImages.splice(index, 1)
  images.value = rawImages.filter(key => !!key)
}
</script>

<template>
  <!-- PC version -->
  <div class="hidden md:block">
    <v-row class="md:p-2">
      <v-col
        v-for="(image, index) in rawImages"
        :key="index"
        cols="2"
        class="md:flex md:relative"
      >
        <v-img
          :id="`gallery-image-${index}`"
          :src="image.url || image.rawFile"
          aspect-ratio="1"
          class="md:bg-grey-lighten-2 md:rounded-lg md:border md:border-slate-400"
          cover
        />
        <div
          v-if="!image.isLoaded && !image.uploadFailed"
          class="md:z-10 md:h-full md:w-full md:absolute md:top-1/2 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2"
        >
          <v-overlay
            :model-value="true"
            class="md:align-center md:justify-center"
            contained
          >
            <v-progress-circular
              indeterminate color="white"
              size="120"
              width="10"
            />
          </v-overlay>
        </div>

        <div class="md:absolute md:z-10 md:top-3 md:right-3 md:mx-2 md:my-2 md:grid md:grid-cols-2 md:gap-2">
          <v-btn
            :icon="getCloudIcon(image)"
            size="small"
            :color="image.isLoaded ? 'success' : image.uploadFailed ? 'error' : 'primary'"
            :disabled="!image.uploadFailed"
            @click="uploadImage(image.file, index)"
          />
          <v-btn
            v-if="image.isLoaded || image.uploadFailed"
            size="small"
            icon
            color="#dc2626"
            @click="deleteImage(index)"
          >
            <Delete02Icon />
          </v-btn>
        </div>
      </v-col>

      <v-col cols="2" class="md:flex md:relative md:my-2">
        <v-responsive aspect-ratio="1">
          <template #default>
            <v-btn
              class="md:flex md:align-middle md:justify-center md:bg-grey-lighten-2 md:text-base-normal"
              style="width: 100%; height: 100%"
              @click="selectImage"
            >
              + Add Image
            </v-btn>
          </template>
        </v-responsive>

        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          class="md:hidden"
          @change="onImageSelected"
        >
      </v-col>
    </v-row>
  </div>

  <!-- Phone version -->
  <div class="md:hidden grid grid-cols-2">
    <v-col
      v-for="(image, index) in rawImages"
      :key="index"
      class="flex md:flex-row flex-col relative"
    >
      <v-img
        :id="`gallery-image-${index}`"
        :src="image.url || image.rawFile"
        aspect-ratio="1"
        class="bg-grey-lighten-2 rounded-lg border border-slate-400"
        cover
      />
      <div
        v-if="!image.isLoaded && !image.uploadFailed"
        class="z-10 h-full w-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <v-overlay
          :model-value="true"
          class="align-center justify-center ma-3"
          contained
        >
          <v-progress-circular
            indeterminate color="white"
            size="120"
            width="10"
          />
        </v-overlay>
      </div>

      <div class="absolute z-10 top-3 right-3 mx-2 my-2 grid grid-cols-2 gap-2">
        <v-btn
          :icon="getCloudIcon(image)"
          size="small"
          :color="image.isLoaded ? 'success' : image.uploadFailed ? 'error' : 'primary'"
          :disabled="!image.uploadFailed"
          @click="uploadImage(image.file, index)"
        />
        <v-btn
          v-if="image.isLoaded || image.uploadFailed"
          size="small"
          icon
          color="#dc2626"
          @click="deleteImage(index)"
        >
          <Delete02Icon />
        </v-btn>
      </div>
    </v-col>

    <v-col class="flex relative">
      <v-responsive aspect-ratio="1">
        <template #default>
          <v-btn
            class="flex align-middle justify-center bg-grey-lighten-2 text-base-normal"
            style="width: 100%; height: 100%"
            @click="selectImage"
          >
            <!-- + Add Image -->
            <ImageAdd02Icon class="md:block hidden text-slate-800" :size="60" />
            <ImageAdd02Icon class="md:hidden text-slate-800" :size="40" />
          </v-btn>
        </template>
      </v-responsive>

      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        class="hidden"
        @change="onImageSelected"
      >
    </v-col>
  </div>
</template>

<style scoped>
</style>
