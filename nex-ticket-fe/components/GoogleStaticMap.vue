<script setup lang="ts">
interface Props {
  lat: number
  long: number
}

const props = defineProps<Props>()

const config = useRuntimeConfig()
const apiKey = config.public.googleMapsApiKey

const mapUrl = computed(() => {
  return `https://maps.googleapis.com/maps/api/staticmap?center=${props.lat},${props.long}&zoom=14&size=600x300&maptype=roadmap&markers=color:red%7C${props.lat},${props.long}&key=${apiKey}`
})
</script>

<template>
  <div>
    <img :src="mapUrl" alt="Google Map">
  </div>
</template>

<style scoped>
img {
  width: 100%;
  height: auto;
  border: 1px solid #ccc;
  border-radius: 8px;
}
</style>
