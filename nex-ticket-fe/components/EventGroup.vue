<script lang="ts" setup>
const props = defineProps<{
  eventsGrouped: deSerGroupedEventData
  titleColor: string
  title?: string
  addAllEventsButton: boolean
  icon?: Function
  bgColor: string
}>()
const emit = defineEmits(['seeAll', 'savedTriggered'])

function emitSavedTriggered(id: number) {
  emit('savedTriggered', id)
}
</script>

<template>
  <div
    v-if="(eventsGrouped[0]?.Events?.length > 0)"
    class="flex flex-col p-4 gap-8 rounded-2xl event-group-container"
    :class="[props.bgColor, { 'saved-events-group': eventsGrouped[0]?.G_KEY === 'saved_events' }]"
  >
    <div v-for="events in eventsGrouped" :key="events.G_KEY">
      <EventGroupRow
        :events="events.Events"
        :title="props.title ?? events.Name"
        :icon="props.icon"
        :title-color="props.titleColor"
        :add-all-events-button="props.addAllEventsButton"
        @saved-triggerd="emitSavedTriggered"
        @see-all="emit('seeAll', events.G_KEY)"
      />
    </div>
  </div>
</template>

<style>
.event-group-container {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  width: fit-content;
  max-width: 100%;
  overflow: hidden;
  will-change: width, height;
  backface-visibility: hidden;
  perspective: 1000px;
}

.saved-events-group {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}
</style>
