<script lang="ts" setup>
import type NexEvent from '@/models/NexEvent'
import { ArrowRight04Icon } from 'hugeicons-vue'

const props = defineProps<{
  events: NexEvent[] | undefined
  title: string
  titleColor: string
  addAllEventsButton: boolean
  icon?: Component
}>()

const emit = defineEmits(['seeAll', 'savedTriggerd'])

const { pageLoading } = storeToRefs(useGeneralStore())

const stepSize = 306

const carouselElement = ref<HTMLElement>()

function handleWheelScroll(event: WheelEvent) {
  if (props.events && props.events.length > 4) {
    event.preventDefault()
    carouselElement.value?.scrollBy({ left: (event.deltaY > 0 ? stepSize : -stepSize), behavior: 'smooth' })
  }
}
</script>

<template>
  <div class="gap-3 flex flex-col">
    <div class="flex justify-between">
      <div class="w-full gap-8 h-auto">
        <div v-if="!pageLoading" class="w-auto min-w-[188px] h-auto min-h-[28px] flex gap-2">
          <component
            :is="props.icon"
            v-if="props.icon"
            class="w-6 h-6"
            :class="props.titleColor"
          />
          <p

            class="w-[160px] h-[28px] text-lg-bold size-[18px]"
            :class="props.titleColor"
          >
            {{ props.title }}
          </p>
        </div>
        <div v-else class="w-[160px] h-[28px] text-lg-bold placeholder animate-pulse" />
      </div>
      <div
        v-if="addAllEventsButton" class="text-slate-800 gap-1 relative"
      >
        <div v-if="!pageLoading">
          <button
            variant="plain"
            class="flex float-right min-w-[79px] min-h-[24px] gap-1 grid-cols-2"
            @click="emit('seeAll')"
          >
            <h4
              class="text-base-bold"
            >
              {{ $t('public.index.buttons.seeAll') }}
            </h4>
            <ArrowRight04Icon class="size-6 self-center" />
          </button>
          <div class="absolute bottom-[1px] left-0 w-full border-b-2 border-slate-800" />
        </div>
      </div>
    </div>
    <div class="md:relative">
      <div ref="carouselElement" class="md:flex md:overflow-x-hidden md:gap-4 event-carousel overflow-x-scroll scrollbar-not-display" @wheel="handleWheelScroll">
        <transition-group name="event-block" tag="div" class="flex gap-4 overflow-x-scroll scrollbar-hidden event-blocks-container">
          <div v-for="event in props.events" :key="event.id" class="event-block-item">
            <!-- PC version -->
            <EventBlock
              :id="event?.id"
              class="hidden md:block"
              :main-photo-url="event?.main_photo?.url"
              :name="event?.name"
              :venue-name="event?.venue_name"
              :venue-address="event?.venue_address"
              :start-time="event?.start_time"
              :end-time="event?.end_time"
              :saved="event?.saved"
              mode="public"
              block-width-class="w-[292px]"
              photo-height-class="h-[146px]"
              @event-saved="emit('savedTriggerd', event.id)"
            />

            <!-- Mobile version -->
            <EventBlock
              :id="event?.id"
              class="md:hidden"
              :main-photo-url="event?.main_photo?.url"
              :name="event?.name"
              :venue-name="event?.venue_name"
              :venue-address="event?.venue_address"
              :start-time="event?.start_time"
              :end-time="event?.end_time"
              :saved="event?.saved"
              mode="public"
              block-width-class="w-[280px]"
              photo-height-class="h-[140px]"
              @event-saved="emit('savedTriggerd', event.id)"
            />
          </div>
        </transition-group>
      </div>
    </div>
  </div>
</template>

<style>
.event-carousel {
  transition: width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: width;
  backface-visibility: hidden;
  perspective: 1000px;
}

.event-blocks-container {
  display: flex;
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.event-block-item {
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.event-block-enter-active,
.event-block-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.event-block-enter-from {
  opacity: 0;
  transform: translateX(30px) translateZ(0);
}

.event-block-leave-to {
  opacity: 0;
  transform: translateX(-30px) translateZ(0);
}

.event-block-move {
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.scrollbar-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-not-display {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-not-display::-webkit-scrollbar {
  display: none;
}
</style>
