# Blog Management Guide

This guide explains how to manage blog posts in the TicketPie blog system.

## Overview

The blog system is designed to be simple and easy to manage. All blog posts are stored in a TypeScript file (`data/blogPosts.ts`) which makes it easy to add, edit, and manage content without needing a complex CMS.

## File Structure

```
nex-ticket-fe/
├── pages/
│   └── blog/
│       ├── index.vue               # Main blog listing page
│       └── [slug].vue              # Individual blog post page
├── components/
│   └── Blog/
│       └── BlogCard.vue            # Blog post card component
├── data/
│   └── blogPosts.ts                # Blog posts data and helper functions
└── i18n/locales/blog/
    ├── en.ts                       # English translations
    ├── sk.ts                       # Slovak translations
    ├── de.ts                       # German translations
    └── pl.ts                       # Polish translations
```

## Adding a New Blog Post

To add a new blog post, follow these steps:

### 1. Add the Blog Post Data

Open `data/blogPosts.ts` and add a new blog post object to the `blogPosts` array:

```typescript
{
  id: 5, // Use the next available ID
  title: 'Your Blog Post Title',
  excerpt: 'A brief description of your blog post that will appear in the listing and meta tags.',
  content: `
    <h2>Your Content Here</h2>
    <p>Write your blog post content using HTML. You can use:</p>
    <ul>
      <li>Headings (h2, h3, h4)</li>
      <li>Paragraphs</li>
      <li>Lists (ul, ol)</li>
      <li>Links</li>
      <li>Bold and italic text</li>
    </ul>
  `,
  author: 'Author Name',
  publishedAt: new Date('2024-01-20'), // Publication date
  category: 'guides', // One of: 'events', 'tips', 'updates', 'guides'
  slug: 'your-blog-post-slug', // URL-friendly version of the title
  featuredImage: '/api/placeholder/800/400', // Replace with actual image path
  readTime: 5, // Estimated reading time in minutes
  tags: ['tag1', 'tag2', 'tag3'], // Relevant tags
  featured: false, // Set to true for featured posts
  published: true, // Set to false for drafts
}
```

### 2. Image Management

- Place blog post images in the `public/images/blog/` directory
- Use descriptive filenames (e.g., `event-planning-guide-hero.jpg`)
- Recommended image sizes:
  - Featured images: 800x400px (2:1 aspect ratio)
  - In-content images: 600-800px wide
- Update the `featuredImage` path to point to your actual image

### 3. Content Guidelines

#### HTML Content
- Use semantic HTML tags
- Keep headings hierarchical (h2 → h3 → h4)
- Use `<p>` tags for paragraphs
- Use `<ul>` and `<ol>` for lists
- Use `<strong>` for bold text and `<em>` for italic text

#### Categories
Choose the appropriate category:
- **events**: Posts about specific events or event types
- **tips**: Practical advice and tips
- **updates**: Platform updates and announcements
- **guides**: Comprehensive how-to guides

#### Tags
- Use 3-5 relevant tags
- Keep tags consistent across posts
- Use lowercase with spaces (e.g., 'event planning', 'marketing tips')

#### Slug
- Use lowercase letters, numbers, and hyphens only
- Keep it concise but descriptive
- Example: 'how-to-create-unforgettable-events'

## Managing Existing Posts

### Editing a Post
1. Find the post in `data/blogPosts.ts` by its ID or slug
2. Make your changes
3. Save the file

### Unpublishing a Post
Set `published: false` in the post object.

### Featuring a Post
Set `featured: true` in the post object. Featured posts may be highlighted in the UI.

## Translation Management

Blog-specific translations are stored in `i18n/locales/blog/` directory. The main translatable elements are:

- Page titles and descriptions
- Category names
- UI labels (Read More, Share, etc.)
- Search placeholders

To add new translatable content:
1. Add the key to all language files (`en.ts`, `sk.ts`, `de.ts`, `pl.ts`)
2. Use the translation in your components with `$t('blog.your.key')`

## SEO Optimization

Each blog post automatically includes:
- Meta title: `{Post Title} - TicketPie Blog`
- Meta description: Uses the post excerpt
- Open Graph tags for social sharing
- Structured data for search engines

## Responsive Design

The blog is fully responsive and follows the existing design system:
- Mobile-first approach
- Consistent with TicketPie's color scheme and typography
- Optimized for all screen sizes
- Touch-friendly navigation

## Performance Considerations

- Images should be optimized for web (use WebP format when possible)
- Keep blog post content reasonable in length
- The system uses static data, so it's very fast
- Consider implementing lazy loading for images if you have many posts

## Future Enhancements

The current system can be easily extended with:
- CMS integration (Strapi, Contentful, etc.)
- Comment system
- Newsletter subscription
- Social sharing analytics
- Search functionality improvements
- Author profiles
- Related posts algorithm improvements

## Troubleshooting

### Common Issues

1. **Blog post not showing**: Check that `published: true` is set
2. **404 error on individual post**: Verify the slug is correct and matches the URL
3. **Images not loading**: Check the image path and ensure the file exists in the public directory
4. **Translation missing**: Ensure all translation keys exist in all language files

### Development

To test your changes:
1. Run `npm run dev` in the `nex-ticket-fe` directory
2. Navigate to `/blog` to see the blog listing
3. Click on a post to view the individual post page
4. Test responsive design by resizing your browser window

## Best Practices

1. **Content Quality**: Write engaging, valuable content for your audience
2. **Consistency**: Maintain consistent tone and style across posts
3. **SEO**: Use descriptive titles and excerpts
4. **Images**: Always include alt text and optimize file sizes
5. **Categories**: Use categories consistently to help users find related content
6. **Tags**: Don't over-tag; use relevant, specific tags
7. **Publishing**: Review posts thoroughly before setting `published: true`

## Support

For technical issues or questions about the blog system, contact the development team or refer to the main project documentation.
